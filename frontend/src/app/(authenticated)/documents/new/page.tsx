'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeft, Settings, Wand, File, FileText, AlignLeft, Loader2, Plus } from 'lucide-react'

import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { useUser } from '@/contexts/UserContext'

import {
  DocumentDraftMode,
  DocumentDraftStep,
} from '@/lib/documents'
import type { DocumentTemplate } from '@/lib/documents/templates'
import { templateService } from '@/lib/supabase/templates'

export default function NewDocumentPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const initialTemplateId = searchParams.get('template')

  const [draftMode, setDraftMode] = useState<DocumentDraftMode>(DocumentDraftMode.HYBRID)
  const [templates, setTemplates] = useState<DocumentTemplate[]>([])
  const [filteredTemplates, setFilteredTemplates] = useState<DocumentTemplate[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { role, tenantId } = useUser()

  useEffect(() => {
    const fetchTemplates = async () => {
      setIsLoading(true)
      setError(null)
      try {
        console.log('Fetching templates with role:', role, 'and tenantId:', tenantId)
        const data = await templateService.getAllAvailable()
        if (data && data.length > 0) {
          setTemplates(data)
          setFilteredTemplates(data)
        } else {
          console.warn('No templates found or empty array returned')
          setTemplates([])
          setFilteredTemplates([])
          // Only show error if no templates were found
          if (data.length === 0) {
            setError('No templates available. This could be due to permission issues or no templates exist for your account.')
          }
        }
      } catch (err: any) {
        console.error('Error fetching templates:', err)
        if (err?.code === '42501') {
          setError('You do not have permission to access templates. Please contact your administrator.')
        } else {
          setError('Failed to load templates. Please try again later.')
        }
        // Set empty arrays to prevent further errors
        setTemplates([])
        setFilteredTemplates([])
      } finally {
        setIsLoading(false)
      }
    }

    fetchTemplates()

    // If a template ID is provided in the URL, go directly to drafting
    if (initialTemplateId) {
      router.push(`/documents/draft?mode=${DocumentDraftMode.TEMPLATE}&template=${initialTemplateId}`)
    }
  }, [initialTemplateId, router, role, tenantId])

  useEffect(() => {
    // Filter templates based on search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      const filtered = templates.filter(template =>
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query) ||
        template.category.toLowerCase().includes(query)
      )
      setFilteredTemplates(filtered)
    } else {
      setFilteredTemplates(templates)
    }
  }, [searchQuery, templates])

  const handleModeSelect = (mode: DocumentDraftMode): void => {
    setDraftMode(mode)
  }

  const handleTemplateSelect = (templateId: string): void => {
    router.push(`/documents/draft?mode=${draftMode}&template=${templateId}`)
  }

  const handleAICreate = (): void => {
    router.push(`/documents/draft?mode=${DocumentDraftMode.AI_ASSISTED}`)
  }

  const getCategoryBadgeColor = (category: string) => {
    const colors: Record<string, string> = {
      'complaint': 'bg-red-100 text-red-800',
      'motion': 'bg-blue-100 text-blue-800',
      'letter': 'bg-green-100 text-green-800',
      'agreement': 'bg-purple-100 text-purple-800',
      'form': 'bg-yellow-100 text-yellow-800',
      'authorization': 'bg-orange-100 text-orange-800',
      'discovery': 'bg-indigo-100 text-indigo-800',
      'other': 'bg-gray-100 text-gray-800'
    }

    return colors[category] || colors.other
  }

  return (
    <div className="container max-w-7xl mx-auto py-8 px-4">
      <div className="mb-8 flex justify-between items-center">
        <div className="flex items-center">
          <Link href="/documents">
            <Button variant="outline" size="icon" className="mr-4">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Create New Document</h1>
            <p className="text-muted-foreground mt-1">
              Choose a method to start drafting your legal document
            </p>
          </div>
        </div>
      </div>

      <Tabs defaultValue="mode" className="max-w-4xl mx-auto">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="mode">1. Select Method</TabsTrigger>
          <TabsTrigger value="template" disabled={draftMode === DocumentDraftMode.AI_ASSISTED}>
            2. Choose Template
          </TabsTrigger>
        </TabsList>

        <TabsContent value="mode" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Template-based drafting */}
            <Card className={`cursor-pointer ${draftMode === DocumentDraftMode.TEMPLATE ? 'border-primary' : ''}`}
                  onClick={() => handleModeSelect(DocumentDraftMode.TEMPLATE)}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  Template-Based
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">
                  Start with a pre-defined template and customize it for your specific case.
                </p>
              </CardContent>
              <CardFooter>
                <div className="text-xs text-muted-foreground">
                  Best for: Standard filings and routine documents
                </div>
              </CardFooter>
            </Card>

            {/* AI-assisted drafting */}
            <Card className={`cursor-pointer ${draftMode === DocumentDraftMode.AI_ASSISTED ? 'border-primary' : ''}`}
                  onClick={() => handleModeSelect(DocumentDraftMode.AI_ASSISTED)}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Wand className="h-5 w-5 mr-2" />
                  AI-Assisted
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">
                  Generate a document from scratch with AI assistance, providing key details along the way.
                </p>
              </CardContent>
              <CardFooter>
                <div className="text-xs text-muted-foreground">
                  Best for: Unique situations requiring custom language
                </div>
              </CardFooter>
            </Card>

            {/* Hybrid approach */}
            <Card className={`cursor-pointer ${draftMode === DocumentDraftMode.HYBRID ? 'border-primary' : ''}`}
                  onClick={() => handleModeSelect(DocumentDraftMode.HYBRID)}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="h-5 w-5 mr-2" />
                  Hybrid Approach
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">
                  Start with a template, then use AI to enhance specific sections and provide suggestions.
                </p>
              </CardContent>
              <CardFooter>
                <div className="text-xs text-muted-foreground">
                  Best for: Complex documents requiring both structure and customization
                </div>
              </CardFooter>
            </Card>
          </div>

          <div className="mt-6 flex justify-between">
            <Button variant="outline" onClick={() => router.push('/documents')}>
              Cancel
            </Button>

            {draftMode === DocumentDraftMode.AI_ASSISTED ? (
              <Button onClick={handleAICreate}>
                Start AI Drafting
              </Button>
            ) : (
              <Button onClick={() => (document.querySelector('[data-value="template"]') as HTMLElement)?.click()}>
                Next: Choose Template
              </Button>
            )}
          </div>
        </TabsContent>

        <TabsContent value="template" className="mt-6">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Select a Template</h2>
              <div className="flex items-center gap-4">
                <div className="relative w-60">
                  <Input
                    placeholder="Search templates..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <AlignLeft className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                </div>
                <Link href="/templates/new">
                  <Button variant="outline" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    New Template
                  </Button>
                </Link>
              </div>
            </div>

            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : error ? (
              <Card className="border-red-400">
                <CardContent className="pt-6 text-center">
                  <div className="flex items-center justify-center text-red-600 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="12" y1="8" x2="12" y2="12"></line>
                      <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                    <h3 className="text-lg font-medium">Error Loading Templates</h3>
                  </div>
                  <p className="mb-4">{error}</p>
                  <div className="flex justify-center">
                    <Button variant="outline" onClick={() => window.location.reload()} className="mr-2">
                      Try Again
                    </Button>
                    <Link href="/documents">
                      <Button variant="secondary">
                        Back to Documents
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ) : filteredTemplates.length === 0 ? (
              <div className="text-center py-12 border rounded-md">
                <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium">No templates found</h3>
                <p className="text-muted-foreground mt-1">
                  {searchQuery
                    ? 'Try adjusting your search query'
                    : 'Create your first template to get started'}
                </p>
                {!searchQuery && (
                  <Link href="/templates/new">
                    <Button variant="outline" className="mt-4">
                      <Plus className="mr-2 h-4 w-4" />
                      Create Template
                    </Button>
                  </Link>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-4">
                {filteredTemplates.map(template => (
                  <Card
                    key={template.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleTemplateSelect(template.id)}
                  >
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <CardDescription>{template.description}</CardDescription>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <div className="flex justify-between">
                        <div className="flex items-center">
                          <Badge className={getCategoryBadgeColor(template.category)}>
                            {template.category.charAt(0).toUpperCase() + template.category.slice(1)}
                          </Badge>
                          <Badge className="ml-2 bg-blue-100 text-blue-800">
                            {template.documentType === 'case-specific' ? 'Case-Specific' : 'General Operations'}
                          </Badge>
                        </div>
                        {template.jurisdiction && (
                          <div className="text-sm">
                            Jurisdiction: {template.jurisdiction}
                          </div>
                        )}
                      </div>
                    </CardContent>
                    <CardFooter>
                      <div className="text-xs text-muted-foreground">
                        <span>{template.sections.length} sections</span>
                        <span className="mx-2">•</span>
                        <span>{template.variables.length} variables</span>
                      </div>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            )}
          </div>

          <div className="mt-6 flex justify-between">
            <Button
              variant="outline"
              onClick={() => (document.querySelector('[data-value="mode"]') as HTMLElement)?.click()}
            >
              Back
            </Button>

            <Link href="/templates">
              <Button variant="outline">
                Manage All Templates
              </Button>
            </Link>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
