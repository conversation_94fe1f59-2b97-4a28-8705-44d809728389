'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import {
  Activity,
  Users,
  Briefcase,
  Clock,
  Calendar,
  AlertCircle,
} from 'lucide-react'
import { useSupabase } from '@/lib/supabase/provider'
import { useTasksApi } from '@/hooks/useTasksApi'

import { useMattersApi } from '@/hooks/useMattersApi' // New Matter API
import { useClientsApi } from '@/hooks/useClientsApi'
import { PracticeArea, WorkType, getMatterDisplayLabel } from '@/types/domain/tenants/Matter'
import ProactiveContainer from './ProactiveContainer'
import { CopilotChatComponent } from '@/components/copilot/copilot-chat'
import { useCopilotContext } from '@copilotkit/react-core'
import DeadlineInsightsPanel from '@/components/dashboard/DeadlineInsightsPanel'
import MorningBriefing from '@/components/dashboard/MorningBriefing'

interface RecentActivity {
  id: string
  type: 'case_created' | 'deadline_approaching' | 'task_completed' | 'client_added'
  title: string
  description: string
  timestamp: string
}

export default function DashboardPage() {
  const router = useRouter()
  const { supabase } = useSupabase()
  const copilot = useCopilotContext()

  // Access schema-aware hooks
  const { getAllTasks } = useTasksApi()

  const { getAllMatters } = useMattersApi() // New Matter API
  const { getAllClients } = useClientsApi()

  // State for dashboard data
  const [tasks, setTasks] = useState<any[]>([])
  const [cases, setCases] = useState<any[]>([])
  const [matters, setMatters] = useState<any[]>([]) // New matters state
  const [clients, setClients] = useState<any[]>([])
  const [navigationLabel, setNavigationLabel] = useState('Cases') // Dynamic label
  const [stats, setStats] = useState({
    activeCases: 0,
    activeClients: 0,
    pendingTasks: 0,
    upcoming7Days: 0
  })
  const [loading, setLoading] = useState(true)
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true)

        // Get task statistics efficiently using dedicated stats endpoint
        try {
          const statsResponse = await fetch('/api/tasks/stats');
          if (statsResponse.ok) {
            const taskStats = await statsResponse.json();
            setStats(prev => ({
              ...prev,
              pendingTasks: taskStats.pending || 0,
              upcoming7Days: taskStats.upcomingDeadlines || 0
            }));
          }
        } catch (statsErr) {
          console.error('Error fetching task stats:', statsErr);
        }

        // Still get some tasks for the recent activity display (limited set is fine for this)
        try {
          const tasksData = await getAllTasks();
          setTasks(tasksData || []);
        } catch (taskErr) {
          console.error('Error fetching tasks:', taskErr);
          // Continue with other data fetching
        }

        // Try to fetch matters and cases with more error tolerance
        try {
          // Added delay to prevent multiple API calls at once
          await new Promise(resolve => setTimeout(resolve, 100));

          // Fetch matters using new API
          const mattersData = await getAllMatters({ limit: 50 });
          setMatters(mattersData || []);

          // Determine navigation label based on practice areas
          if (mattersData && mattersData.length > 0) {
            const allLitigation = mattersData.every(matter => matter.workType === WorkType.LITIGATION);
            setNavigationLabel(allLitigation ? 'Cases' : 'Matters');
          }

          // Cases data is now handled by matters API

          // Calculate stats from matters
          const activeMattersCount = (mattersData || []).filter((m: any) => m?.status === 'active').length;

          setStats(prev => ({
            ...prev,
            activeCases: activeMattersCount
          }));
        } catch (caseErr) {
          console.error('Error fetching cases/matters:', caseErr);
          // Use empty arrays
          setCases([]);
          setMatters([]);
        }

        // Try to fetch clients with more error tolerance
        try {
          // First try the test endpoint which is more resilient
          try {
            console.log('Attempting to fetch clients from test endpoint first');
            const testResponse = await fetch('/api/clients-test', {
              headers: {
                'Content-Type': 'application/json',
              },
              credentials: 'include'
            });

            if (!testResponse.ok) {
              console.warn('Clients test endpoint failed with status:', testResponse.status);
              throw new Error(`Test endpoint failed with status ${testResponse.status}`);
            }

            const testData = await testResponse.json();
            console.log('Clients test endpoint _response:', testData);

            if (testData.success) {
              // Use the data from the most successful method
              let clientsData = [];
              if (testData.data.schemaClient?.length > 0) {
                console.log('Using schema client data');
                clientsData = testData.data.schemaClient;
              } else if (testData.data.schemaMethod?.length > 0) {
                console.log('Using schema method data');
                clientsData = testData.data.schemaMethod;
              }

              if (clientsData.length > 0) {
                setClients(clientsData);
                // Calculate client stats
                const activeClientsCount = clientsData.filter((c: any) => c?.status === 'active').length;
                setStats(prev => ({
                  ...prev,
                  activeClients: activeClientsCount
                }));
                return; // Skip the regular endpoint if test was successful
              }
            }
          } catch (testErr) {
            console.warn('Clients test endpoint error:', testErr);
            // Continue to try the main endpoint
          }

          // Added delay to prevent multiple API calls at once
          await new Promise(resolve => setTimeout(resolve, 100));
          console.log('Falling back to regular clients endpoint');
          const clientsData = await getAllClients();
          setClients(clientsData || []);
          // Calculate client stats
          const activeClientsCount = (clientsData || []).filter((c: any) => c?.status === 'active').length;
          setStats(prev => ({
            ...prev,
            activeClients: activeClientsCount
          }));
        } catch (clientErr) {
          console.error('Error fetching clients:', clientErr);
          // Use empty array
          setClients([]);
        }

        // Update deadlines count
        setStats(prev => ({
          ...prev,
          upcoming7Days: 0  // placeholder, would need a separate query for deadlines
        }))

        // Calculate stats with safe array handling (only for cases and clients, tasks stats come from API)
        const activeCasesCount = Array.isArray(cases) ?
          cases.filter((c: any) => c?.status === 'active').length : 0;

        const activeClientsCount = Array.isArray(clients) ?
          clients.filter((c: any) => c?.status === 'active').length : 0;

        setStats(prev => ({
          ...prev,
          activeCases: activeCasesCount,
          activeClients: activeClientsCount,
        }));

        // Set up listener for document insertion event if available
        // @ts-expect-error - The copilot interface might not be fully typed
        if (copilot && typeof copilot.registerEventHandler === 'function') {
          // @ts-expect-error - Ignore potential type mismatch errors
          copilot.registerEventHandler('document_inserted', (data: any) => {
            console.log('Document inserted event received!', data)
          })
        }

        // Generate recent activity from the fetched data
        const activity: RecentActivity[] = [];

        // Add recent cases (with safe handling)
        if (Array.isArray(cases) && cases.length > 0) {
          const recentCases = [...cases]
            .filter(c => c?.created_at) // Ensure valid cases
            .sort((a: any, b: any) => {
              try {
                return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
              } catch (e) {
                return 0;
              }
            })
            .slice(0, 2);

          recentCases.forEach((c: any) => {
            activity.push({
              id: `case-${c.id || Math.random().toString(36).substring(2)}`,
              type: 'case_created',
              title: 'New case filed',
              description: `${c.title || 'Untitled Case'} - ${c.case_type || 'Unknown Type'}`,
              timestamp: c.created_at || new Date().toISOString(),
            });
          });
        }

        // Add upcoming deadlines (with safe handling)
        if (Array.isArray(tasks) && tasks.length > 0) {
          // Define date variables for deadline filtering
          const now = new Date();
          const nextWeek = new Date();
          nextWeek.setDate(now.getDate() + 7);

          const upcomingDeadlines = tasks
            .filter((t: any) => {
              if (!t?.due_date) return false;
              try {
                const dueDate = new Date(t.due_date);
                return dueDate >= now && dueDate <= nextWeek && t.status !== 'done';
              } catch (e) {
                return false;
              }
            })
            .sort((a: any, b: any) => {
              try {
                return new Date(a.due_date).getTime() - new Date(b.due_date).getTime();
              } catch (e) {
                return 0;
              }
            })
            .slice(0, 2);

          upcomingDeadlines.forEach((t: any) => {
            activity.push({
              id: `deadline-${t.id || Math.random().toString(36).substring(2)}`,
              type: 'deadline_approaching',
              title: 'Deadline approaching',
              description: t.title || 'Untitled Task',
              timestamp: t.due_date || new Date().toISOString(),
            });
          });
        }

        // Sort activity by timestamp (most recent first) with safe handling
        activity.sort((a, b) => {
          try {
            return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
          } catch (e) {
            return 0;
          }
        });

        setRecentActivity(activity);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Set empty data on error
        setRecentActivity([]);
      } finally {
        // Always set loading to false, even on errors
        setLoading(false)
      }
    };

    fetchDashboardData();

    // Set a timeout to stop loading state if it takes too long
    const loadingTimeout = setTimeout(() => {
      setLoading(false);
    }, 10000); // 10 seconds max loading time

    return () => clearTimeout(loadingTimeout);
  }, [getAllMatters, getAllClients, getAllTasks, copilot]);

  // No duplicate effect needed, the event handler is already set up in the main useEffect

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Dashboard</h1>
      </div>

      {/* Proactive Messaging */}
      <ProactiveContainer />

      {/* Morning Briefing */}
      <MorningBriefing />

      {/* Deadline Insights */}
      <DeadlineInsightsPanel />

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active {navigationLabel}</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-2">
                <div className="h-8 w-16 bg-gray-200 rounded animate-pulse" />
                <div className="h-4 w-28 bg-gray-200 rounded animate-pulse" />
              </div>
            ) : (
              <>
                <div className="text-2xl font-bold">{stats.activeCases}</div>
                <p className="text-xs text-muted-foreground">
                  Currently active {navigationLabel.toLowerCase()}
                </p>
              </>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Clients</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-2">
                <div className="h-8 w-16 bg-gray-200 rounded animate-pulse" />
                <div className="h-4 w-28 bg-gray-200 rounded animate-pulse" />
              </div>
            ) : (
              <>
                <div className="text-2xl font-bold">{stats.activeClients}</div>
                <p className="text-xs text-muted-foreground">
                  Currently active clients
                </p>
              </>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Upcoming Deadlines
            </CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-2">
                <div className="h-8 w-16 bg-gray-200 rounded animate-pulse" />
                <div className="h-4 w-28 bg-gray-200 rounded animate-pulse" />
              </div>
            ) : (
              <div className="flex flex-col">
                <span className="text-3xl font-bold">{stats.upcoming7Days || 0}</span>
                <span className="text-muted-foreground">Upcoming Deadlines</span>
              </div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Tasks</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-2">
                <div className="h-8 w-16 bg-gray-200 rounded animate-pulse" />
                <div className="h-4 w-28 bg-gray-200 rounded animate-pulse" />
              </div>
            ) : (
              <>
                <div className="text-2xl font-bold">{stats.pendingTasks}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.pendingTasks} tasks need attention
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="bg-gray-200 p-2 rounded-full animate-pulse">
                  <div className="h-4 w-4" />
                </div>
                <div className="flex-1 space-y-1">
                  <div className="h-4 w-32 bg-gray-200 rounded animate-pulse" />
                  <div className="h-3 w-48 bg-gray-200 rounded animate-pulse" />
                </div>
                <div className="h-3 w-12 bg-gray-200 rounded animate-pulse" />
              </div>
              <div className="flex items-center space-x-4">
                <div className="bg-gray-200 p-2 rounded-full animate-pulse">
                  <div className="h-4 w-4" />
                </div>
                <div className="flex-1 space-y-1">
                  <div className="h-4 w-32 bg-gray-200 rounded animate-pulse" />
                  <div className="h-3 w-48 bg-gray-200 rounded animate-pulse" />
                </div>
                <div className="h-3 w-12 bg-gray-200 rounded animate-pulse" />
              </div>
            </div>
          ) : recentActivity.length > 0 ? (
            <div className="space-y-4">
              {recentActivity.map(activity => {
                // Format the relative time (e.g., "2h ago")
                const getRelativeTime = (timestamp: string) => {
                  const now = new Date()
                  const activityTime = new Date(timestamp)
                  const diffMs = now.getTime() - activityTime.getTime()
                  const diffMins = Math.floor(diffMs / 60000)

                  if (diffMins < 60) return `${diffMins}m ago`

                  const diffHours = Math.floor(diffMins / 60)
                  if (diffHours < 24) return `${diffHours}h ago`

                  const diffDays = Math.floor(diffHours / 24)
                  return `${diffDays}d ago`
                }

                // Choose the icon based on activity type
                const getActivityIcon = (type: string) => {
                  switch (type) {
                    case 'case_created':
                      return <Briefcase className="h-4 w-4 text-primary" />
                    case 'deadline_approaching':
                      return <AlertCircle className="h-4 w-4 text-primary" />
                    case 'task_completed':
                      return <Clock className="h-4 w-4 text-primary" />
                    case 'client_added':
                      return <Users className="h-4 w-4 text-primary" />
                    default:
                      return <Activity className="h-4 w-4 text-primary" />
                  }
                }

                return (
                  <div key={activity.id} className="flex items-center space-x-4">
                    <div className="bg-primary/10 p-2 rounded-full">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium">{activity.title}</p>
                      <p className="text-xs text-muted-foreground">
                        {activity.description}
                      </p>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {getRelativeTime(activity.timestamp)}
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-4 text-muted-foreground">
              No recent activity to display
            </div>
          )}
        </CardContent>
      </Card>
      <CopilotChatComponent />
    </div>
  )
}
