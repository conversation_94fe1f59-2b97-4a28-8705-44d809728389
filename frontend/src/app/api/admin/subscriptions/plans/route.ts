import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { withSubscriptionRateLimit } from '@/lib/rate-limiting/rate-limit-middleware';
import { checkSuperAdminAuth } from '@/lib/auth/superadmin';

// Type for plan with pricing data
interface PlanWithPricing {
  id: string;
  name: string;
  code: string;
  description: string | null;
  is_active: boolean | null;
  is_public: boolean | null;
  base_price_monthly: number;
  base_price_yearly: number;
  features: any;
  available_countries: string[];
  base_currency: string;
  stripe_product_id: string | null;
  last_synced_at: string | null;
  created_at: string | null;
  updated_at: string | null;
  plan_pricing?: {
    id: string;
    country_code: string;
    currency: string;
    price_monthly: number;
    price_yearly: number;
    tax_inclusive: boolean;
    stripe_price_id: string | null;
    created_at: string | null;
    updated_at: string | null;
  }[];
}

/**
 * GET /api/admin/subscriptions/plans
 * Get all subscription plans with multi-country pricing
 */
export const GET = withSubscriptionRateLimit(async function(request: NextRequest) {
  try {
    // Check superadmin authentication
    const authResult = await checkSuperAdminAuth();
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const isActive = searchParams.get('is_active');
    const isPublic = searchParams.get('is_public');
    const search = searchParams.get('search');
    const countries = searchParams.get('countries');
    const currencies = searchParams.get('currencies');

    const supabase = await createClient();

    // Build query
    let query = supabase
      .from('subscription_plans')
      .select(`
        *,
        plan_pricing (
          id,
          country_code,
          currency,
          price_monthly,
          price_yearly,
          tax_inclusive,
          stripe_price_id,
          created_at,
          updated_at
        )
      `);

    // Apply filters
    if (isActive !== null) {
      query = query.eq('is_active', isActive === 'true');
    }

    if (isPublic !== null) {
      query = query.eq('is_public', isPublic === 'true');
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,code.ilike.%${search}%`);
    }

    if (countries) {
      const countryList = countries.split(',');
      query = query.overlaps('available_countries', countryList);
    }

    // Execute query
    const { data: plans, error } = await query.order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching subscription plans:', error);
      return NextResponse.json(
        { error: 'Failed to fetch subscription plans' },
        { status: 500 }
      );
    }

    // Filter by currencies if specified
    let filteredPlans = plans;
    if (currencies) {
      const currencyList = currencies.split(',');
      filteredPlans = (plans as any)?.filter((plan: any) =>
        plan.plan_pricing?.some((pricing: any) =>
          currencyList.includes(pricing.currency)
        )
      ) || [];
    }

    return NextResponse.json({
      success: true,
      data: filteredPlans,
      count: filteredPlans?.length || 0
    });

  } catch (error) {
    console.error('Error in subscription plans API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, 'crud');

/**
 * POST /api/admin/subscriptions/plans
 * Create a new subscription plan
 */
export const POST = withSubscriptionRateLimit(async function(request: NextRequest) {
  try {
    // Check superadmin authentication
    const authResult = await checkSuperAdminAuth();
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      name,
      code,
      description,
      is_active = true,
      is_public = true,
      features = {},
      available_countries = ['US'],
      base_currency = 'USD',
      pricing = []
    } = body;

    // Validate required fields
    if (!name || !code) {
      return NextResponse.json(
        { error: 'Name and code are required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // Check if plan code already exists
    const { data: existingPlan } = await supabase
      .from('subscription_plans')
      .select('id')
      .eq('code', code)
      .single();

    if (existingPlan) {
      return NextResponse.json(
        { error: 'Plan code already exists' },
        { status: 409 }
      );
    }

    // Create the plan
    const { data: plan, error: planError } = await supabase
      .from('subscription_plans')
      .insert({
        name,
        code,
        description,
        is_active,
        is_public,
        features,
        available_countries,
        base_currency
      })
      .select()
      .single();

    if (planError) {
      console.error('Error creating subscription plan:', planError);
      return NextResponse.json(
        { error: 'Failed to create subscription plan' },
        { status: 500 }
      );
    }

    // Create pricing entries if provided
    if (pricing && pricing.length > 0 && plan) {
      const pricingData = pricing.map((p: any) => ({
        plan_id: (plan as any).id,
        country_code: p.country_code,
        currency: p.currency,
        price_monthly: p.price_monthly,
        price_yearly: p.price_yearly,
        tax_inclusive: p.tax_inclusive || false
      }));

      const { error: pricingError } = await supabase
        .from('plan_pricing')
        .insert(pricingData);

      if (pricingError) {
        console.error('Error creating plan pricing:', pricingError);
        // Don't fail the entire operation, but log the error
      }
    }

    // Fetch the complete plan with pricing
    const { data: completePlan } = await supabase
      .from('subscription_plans')
      .select(`
        *,
        plan_pricing (*)
      `)
      .eq('id', (plan as any).id)
      .single();

    return NextResponse.json({
      success: true,
      data: completePlan
    });

  } catch (error) {
    console.error('Error creating subscription plan:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, 'crud');
