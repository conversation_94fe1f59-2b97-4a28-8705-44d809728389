import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { withAdminRateLimit } from '@/lib/rate-limiting/rate-limit-middleware';
import { checkSuperAdminAuth } from '@/lib/auth/superadmin';
import { webhookQueueService } from '@/lib/services/webhook-queue-service';
import Redis from 'ioredis';

/**
 * GET /api/admin/webhooks/monitoring
 * Get comprehensive webhook monitoring data
 */
export const GET = withAdminRateLimit(async function(request: NextRequest) {
  try {
    // Check superadmin authentication
    const authResult = await checkSuperAdminAuth();
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '24h'; // 1h, 24h, 7d, 30d

    // Get queue statistics
    const queueStats = await webhookQueueService.getQueueStats();
    const healthCheck = await webhookQueueService.healthCheck();

    // Get detailed Redis metrics
    const redis = new Redis(process.env.REDIS_URL!, {
      keyPrefix: `${process.env.REDIS_QUEUE_PREFIX || 'pi_lawyer_docs'}:webhook_queue:`,
    });

    let redisMetrics = {};
    const recentEvents = [];
    const processingTimes = [];
    let processedCount = 0;

    try {
      // Get recent webhook events for analysis
      const recentWebhooks = await redis.zrevrange(
        'processing_queue',
        0,
        99, // Last 100 items
        'WITHSCORES'
      );

      // Analyze recent events
      const eventTypes = new Map<string, number>();
      const statusCounts = new Map<string, number>();
      let totalProcessingTime = 0;

      for (let i = 0; i < recentWebhooks.length; i += 2) {
        const webhookId = recentWebhooks[i];
        const timestamp = parseInt(recentWebhooks[i + 1]);

        const webhookData = await redis.hget(`webhook:${webhookId}`, 'data');
        if (webhookData) {
          const parsed = JSON.parse(webhookData);
          
          // Count event types
          const eventType = parsed.event?.type || 'unknown';
          eventTypes.set(eventType, (eventTypes.get(eventType) || 0) + 1);
          
          // Count statuses
          statusCounts.set(parsed.status, (statusCounts.get(parsed.status) || 0) + 1);
          
          // Calculate processing time if completed
          if (parsed.status === 'completed' && parsed.completedAt) {
            const processingTime = parsed.completedAt - parsed.createdAt;
            totalProcessingTime += processingTime;
            processedCount++;
            processingTimes.push(processingTime);
          }

          recentEvents.push({
            id: webhookId,
            eventType,
            status: parsed.status,
            attempts: parsed.attempts,
            createdAt: parsed.createdAt,
            lastError: parsed.lastError
          });
        }
      }

      // Calculate metrics
      const averageProcessingTime = processedCount > 0 ? totalProcessingTime / processedCount : 0;
      const successRate = recentEvents.length > 0 
        ? (recentEvents.filter(e => e.status === 'completed').length / recentEvents.length) * 100 
        : 100;

      // Get Redis memory usage and connection info
      let redisMemory = 'Unknown';
      try {
        const redisInfo = await redis.info('memory');
        const redisMemoryMatch = /used_memory_human:([^\r\n]+)/.exec(redisInfo);
        redisMemory = redisMemoryMatch ? redisMemoryMatch[1].trim() : 'Unknown';
      } catch (infoError) {
        console.warn('Could not get Redis memory info:', infoError);
        redisMemory = 'N/A';
      }

      redisMetrics = {
        memory: redisMemory,
        connected: true,
        keyCount: await redis.dbsize(),
        averageProcessingTime: Math.round(averageProcessingTime),
        successRate: Math.round(successRate * 100) / 100,
        eventTypes: Object.fromEntries(eventTypes),
        statusCounts: Object.fromEntries(statusCounts)
      };

    } catch (redisError) {
      console.error('Error getting Redis metrics:', redisError);
      redisMetrics = {
        connected: false,
        error: 'Failed to connect to Redis'
      };
    } finally {
      await redis.quit();
    }

    // Calculate health score
    let healthScore = 100;
    const issues = [];

    if (!healthCheck.redis) {
      healthScore -= 30;
      issues.push('Redis connection unavailable');
    }

    if (queueStats.deadLetterCount > 10) {
      healthScore -= 20;
      issues.push(`High dead letter queue count: ${queueStats.deadLetterCount}`);
    }

    if (queueStats.pendingCount > 100) {
      healthScore -= 15;
      issues.push(`High pending queue count: ${queueStats.pendingCount}`);
    }

    if ('successRate' in redisMetrics && typeof redisMetrics.successRate === 'number' && redisMetrics.successRate < 95) {
      healthScore -= 25;
      issues.push(`Low success rate: ${redisMetrics.successRate}%`);
    }

    if ('averageProcessingTime' in redisMetrics && typeof redisMetrics.averageProcessingTime === 'number' && redisMetrics.averageProcessingTime > 30000) {
      healthScore -= 10;
      issues.push(`High processing time: ${redisMetrics.averageProcessingTime}ms`);
    }

    // Determine health status
    let healthStatus = 'healthy';
    if (healthScore < 70) {
      healthStatus = 'critical';
    } else if (healthScore < 85) {
      healthStatus = 'warning';
    }

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          healthScore: Math.max(0, healthScore),
          healthStatus,
          issues,
          timestamp: new Date().toISOString()
        },
        queue: {
          pending: queueStats.pendingCount,
          deadLetter: queueStats.deadLetterCount,
          redisAvailable: queueStats.redisAvailable
        },
        performance: {
          averageProcessingTime: ('averageProcessingTime' in redisMetrics ? redisMetrics.averageProcessingTime : 0) || 0,
          successRate: ('successRate' in redisMetrics ? redisMetrics.successRate : 100) || 100,
          totalProcessed: processedCount,
          recentEvents: recentEvents.slice(0, 20) // Last 20 events
        },
        redis: redisMetrics,
        alerts: issues.map(issue => ({
          level: healthScore < 70 ? 'critical' : 'warning',
          message: issue,
          timestamp: new Date().toISOString()
        }))
      }
    });

  } catch (error) {
    console.error('Error getting webhook monitoring data:', error);
    return NextResponse.json(
      { error: 'Failed to get webhook monitoring data' },
      { status: 500 }
    );
  }
}, true);

/**
 * POST /api/admin/webhooks/monitoring/test
 * Test webhook processing system
 */
export const POST = withAdminRateLimit(async function(request: NextRequest) {
  try {
    // Check superadmin authentication
    const authResult = await checkSuperAdminAuth();
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { testType = 'basic' } = body;

    // Create a test webhook event
    const testEvent = {
      id: `test_${Date.now()}`,
      type: 'test.webhook.monitoring',
      created: Math.floor(Date.now() / 1000),
      data: {
        object: {
          id: 'test_object',
          test: true,
          timestamp: Date.now()
        }
      },
      livemode: false,
      api_version: '2023-10-16'
    };

    try {
      // Queue the test webhook
      const webhookId = await webhookQueueService.queueWebhook(testEvent);
      
      return NextResponse.json({
        success: true,
        message: 'Test webhook queued successfully',
        data: {
          testType,
          webhookId,
          testEvent: {
            id: testEvent.id,
            type: testEvent.type,
            created: testEvent.created
          }
        }
      });

    } catch (queueError) {
      console.error('Error queuing test webhook:', queueError);
      return NextResponse.json({
        success: false,
        error: 'Failed to queue test webhook',
        details: queueError instanceof Error ? queueError.message : 'Unknown error'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error testing webhook system:', error);
    return NextResponse.json(
      { error: 'Failed to test webhook system' },
      { status: 500 }
    );
  }
}, true);
