import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { withAuth, UserRole } from '@/lib/auth/server-exports';
import type { AuthUser } from '@/lib/auth/server-exports';

/**
 * Proxies morning briefing to the Python backend.
 * Returns a safe fallback in development if backend is unavailable.
 */
export const GET = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (_req: NextRequest, _user: AuthUser): Promise<Response> => {
    const backendBase = process.env.BACKEND_API_URL || process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000';
    const url = `${backendBase}/api/deadline-insights/morning-briefing`;

    try {
      // Forward auth header if present (Supabase cookies also validated by withAuth)
      const res = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        cache: 'no-store'
      });

      if (!res.ok) {
        const text = await res.text().catch(() => '');
        throw new Error(`Backend responded ${res.status}: ${res.statusText} ${text}`);
      }

      const data = await res.json();
      return NextResponse.json(data);
    } catch (error) {
      const useFallback = process.env.DEV_API_FALLBACKS === 'true' && process.env.NODE_ENV !== 'production';
      const reason = (error instanceof Error && error.message.includes('Backend responded')) ? 'backend error' : 'backend unreachable';
      if (useFallback) {
        console.warn(`[DEV_FALLBACK] morning-briefing route using fallback due to: ${reason}`);
        return NextResponse.json({
          greeting: 'Good day!',
          today_summary: {
            critical_deadlines: 0,
            scheduled_tasks: 0,
            upcoming_meetings: 0,
            priority_matters: 0
          },
          priority_actions: [],
          insights: [],
          weather_check: { message: 'No weather impact detected' },
          meta: { devFallback: true }
        });
      }

      console.warn('Morning briefing proxy error:', error);
      return NextResponse.json({ error: 'Failed to fetch morning briefing' }, { status: 502 });
    }
  }
);

