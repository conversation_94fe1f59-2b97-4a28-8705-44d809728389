import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getEnvironmentConfig, validateEnvironmentConfig } from '@/lib/config/environment';
import Stripe from 'stripe';

/**
 * Enhanced health check endpoint for production monitoring
 * Supports both simple connectivity checks and detailed health reports
 * GET /api/health?detailed=true for comprehensive health check
 * GET /api/health for simple connectivity check
 */
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const detailed = searchParams.get('detailed') === 'true';
  const startTime = Date.now();

  // Simple health check for basic connectivity
  if (!detailed) {
    return NextResponse.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      responseTime: Date.now() - startTime
    });
  }

  // Detailed health check for monitoring
  try {
    const config = getEnvironmentConfig();
    const checks: {
      service: string;
      status: 'healthy' | 'degraded' | 'unhealthy';
      responseTime?: number;
      error?: string;
      details?: Record<string, unknown>;
    }[] = [];

    // Environment configuration check
    const envValidation = validateEnvironmentConfig(config);
    checks.push({
      service: 'environment',
      status: envValidation.valid ? 'healthy' : 'unhealthy',
      error: envValidation.errors.length > 0 ? envValidation.errors[0].message : undefined,
      details: {
        errors: envValidation.errors.length,
        warnings: envValidation.warnings.length,
        nodeEnv: config.nodeEnv
      }
    });

    // Database connectivity check
    const dbStart = Date.now();
    try {
      const supabase = await createClient();

      // Simple query to test database connectivity
      const { error: dbError } = await supabase
        .from('subscription_plans')
        .select('id')
        .limit(1);

      const dbResponseTime = Date.now() - dbStart;

      checks.push({
        service: 'database',
        status: dbError ? 'unhealthy' : 'healthy',
        responseTime: dbResponseTime,
        error: dbError?.message,
        details: {
          schema: 'tenants',
          table: 'subscription_plans'
        }
      });
    } catch (error) {
      checks.push({
        service: 'database',
        status: 'unhealthy',
        responseTime: Date.now() - dbStart,
        error: error instanceof Error ? error.message : 'Database connection failed'
      });
    }

    // Stripe API connectivity check
    const stripeStart = Date.now();
    try {
      const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
      if (stripeSecretKey) {
        const stripe = new Stripe(stripeSecretKey, { apiVersion: '2025-06-30.basil' });
        const account = await stripe.accounts.retrieve();
        const stripeResponseTime = Date.now() - stripeStart;

        checks.push({
          service: 'stripe',
          status: stripeResponseTime < 2000 ? 'healthy' : 'degraded',
          responseTime: stripeResponseTime,
          details: {
            account_id: account.id,
            country: account.country,
            charges_enabled: account.charges_enabled,
            payouts_enabled: account.payouts_enabled
          }
        });
      } else {
        checks.push({
          service: 'stripe',
          status: 'unhealthy',
          error: 'Stripe secret key not configured'
        });
      }
    } catch (error) {
      checks.push({
        service: 'stripe',
        status: 'unhealthy',
        responseTime: Date.now() - stripeStart,
        error: error instanceof Error ? error.message : 'Stripe API connection failed'
      });
    }

    // Payment encryption check
    const encryptionStart = Date.now();
    try {
      const encryptionKey = process.env.PAYMENT_ENCRYPTION_KEY;
      const masterKey = process.env.PAYMENT_MASTER_KEY;

      if (encryptionKey && masterKey) {
        // Test basic encryption functionality
        const crypto = require('crypto');
        const testData = 'health-check-test';
        const cipher = crypto.createCipher('aes-256-cbc', encryptionKey);
        let encrypted = cipher.update(testData, 'utf8', 'hex');
        encrypted += cipher.final('hex');

        const decipher = crypto.createDecipher('aes-256-cbc', encryptionKey);
        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');

        const encryptionResponseTime = Date.now() - encryptionStart;

        checks.push({
          service: 'payment_encryption',
          status: decrypted === testData ? 'healthy' : 'unhealthy',
          responseTime: encryptionResponseTime,
          details: {
            encryption_test: decrypted === testData ? 'passed' : 'failed',
            key_configured: true
          }
        });
      } else {
        checks.push({
          service: 'payment_encryption',
          status: 'unhealthy',
          error: 'Payment encryption keys not configured'
        });
      }
    } catch (error) {
      checks.push({
        service: 'payment_encryption',
        status: 'unhealthy',
        responseTime: Date.now() - encryptionStart,
        error: error instanceof Error ? error.message : 'Payment encryption test failed'
      });
    }

    // Monitoring systems check
    const monitoringStart = Date.now();
    try {
      const sentryDsn = process.env.NEXT_PUBLIC_SENTRY_DSN;
      const metricsApiKey = process.env.METRICS_API_KEY;

      let monitoringStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      const monitoringDetails: Record<string, unknown> = {};

      if (!sentryDsn) {
        monitoringStatus = 'degraded';
        monitoringDetails.sentry = 'not_configured';
      } else {
        monitoringDetails.sentry = 'configured';
      }

      if (!metricsApiKey) {
        monitoringDetails.metrics_api = 'not_configured';
      } else {
        monitoringDetails.metrics_api = 'configured';
      }

      const monitoringResponseTime = Date.now() - monitoringStart;

      checks.push({
        service: 'monitoring',
        status: monitoringStatus,
        responseTime: monitoringResponseTime,
        details: monitoringDetails
      });
    } catch (error) {
      checks.push({
        service: 'monitoring',
        status: 'unhealthy',
        responseTime: Date.now() - monitoringStart,
        error: error instanceof Error ? error.message : 'Monitoring check failed'
      });
    }

    // Calculate overall health
    const healthyCount = checks.filter(c => c.status === 'healthy').length;
    const degradedCount = checks.filter(c => c.status === 'degraded').length;
    const unhealthyCount = checks.filter(c => c.status === 'unhealthy').length;

    let overall: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    if (unhealthyCount > 0) {
      overall = 'unhealthy';
    } else if (degradedCount > 0) {
      overall = 'degraded';
    }

    const score = Math.round((healthyCount / checks.length) * 100);
    const totalResponseTime = Date.now() - startTime;

    const healthData = {
      status: overall,
      score,
      timestamp: new Date().toISOString(),
      responseTime: totalResponseTime,
      checks,
      version: process.env.npm_package_version || '1.0.0',
      environment: config.nodeEnv,
      uptime: process.uptime()
    };

    // Return appropriate HTTP status based on health
    const httpStatus = overall === 'healthy' ? 200 : overall === 'degraded' ? 200 : 503;

    return NextResponse.json(healthData, { status: httpStatus });

  } catch (error) {
    console.error('Health check failed:', error);

    return NextResponse.json({
      status: 'unhealthy',
      score: 0,
      timestamp: new Date().toISOString(),
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Health check failed',
      checks: []
    }, { status: 503 });
  }
}

// Support HEAD requests for lightweight connectivity checks
export async function HEAD() {
  return new Response(null, { status: 200 });
}
