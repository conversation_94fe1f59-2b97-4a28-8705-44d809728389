// @ts-nocheck - API route type issues
// frontend/src/app/api/deadlines/route.ts
import { withAuth, UserRole } from '@/lib/auth/server-exports';
import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import type { AuthUser } from '@/lib/auth/server-exports';
import type { SupabaseClient } from '@supabase/supabase-js';
import { z } from 'zod';
import { createServices } from '@/lib/services';
import { DeadlineSchema } from '@/lib/services/calendar-deadline-service';
import type { Database } from '@/lib/database.types';

// Define specific types for clarity
type DeadlineRow = Database['public']['Tables']['task_deadlines']['Row'];

// Define interfaces for deadline query parameters and result
interface DeadlineQueryParams {
  start_date?: string;
  end_date?: string;
  case_id?: string;
  page: number;
  limit: number;
}

interface DeadlineQueryResult {
  deadlines: DeadlineRow[];
  totalCount: number;
}

// GET /api/deadlines - Get all calendar deadlines for current tenant
export const GET = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (
    req: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database, "public", any>,
    context: Record<string, unknown>
  ): Promise<Response> => {
  try {
    // Parse query parameters
    const url = new URL(req.url);
    const startDate = url.searchParams.get('start_date') || undefined;
    const endDate = url.searchParams.get('end_date') || undefined;
    const caseId = url.searchParams.get('case_id') || undefined;
    const status = url.searchParams.get('status') || undefined;
    const page = url.searchParams.get('page') ? parseInt(url.searchParams.get('page')!) : 1;
    const limit = url.searchParams.get('limit') ? parseInt(url.searchParams.get('limit')!) : 50;

    // Create services with the authenticated user's tenant ID
    const services = createServices(supabase, user.tenantId);

    // Use the calendar deadline service to get deadlines with filtering
    const { deadlines, totalCount } = await services.deadlines.getAll({
      start_date: startDate,
      end_date: endDate,
      case_id: caseId,
      // status, // Removed as 'status' is not a valid property for getAll on task_deadlines
      page,
      limit
    });

    return NextResponse.json({
      deadlines,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error: unknown) {
    console.error('Error in GET /api/deadlines:', error);
    let errorMessage = 'Internal server error';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    return NextResponse.json({ error: errorMessage, details: String(error) }, { status: 500 });
  }
});

// POST /api/deadlines - Create a new calendar deadline
export const POST = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (
    req: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database, "public", any>,
    context: Record<string, unknown>
  ): Promise<Response> => {
  try {
    // Create services with the authenticated user's tenant ID
    const services = createServices(supabase, user.tenantId);

    // Parse and validate request body
    const body = await req.json();
    const validationResult = DeadlineSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Validation failed',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const validatedData = validationResult.data;

    // Create the calendar deadline using the calendar deadline service
    const createdDeadline = await services.deadlines.create(user.id, validatedData);

    if (!createdDeadline) {
      return NextResponse.json({ error: 'Failed to create calendar deadline' }, { status: 500 });
    }

    return NextResponse.json(createdDeadline as DeadlineRow, { status: 201 });
  } catch (error: unknown) {
    console.error('Error in POST /api/deadlines:', error);
    let errorMessage = 'Internal server error';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: error.errors }, { status: 400 });
    }
    return NextResponse.json({ error: errorMessage, details: String(error) }, { status: 500 });
  }
});
