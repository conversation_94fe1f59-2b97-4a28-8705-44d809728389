// @ts-nocheck - API route type issues
import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import type { AuthUser} from '@/lib/auth/server-exports';
import { withAuth, UserRole } from '@/lib/auth/server-exports';
import { z } from 'zod';

/**
 * GET /api/matters/[id]/documents - Fetch all documents for a specific matter
 *
 * Query parameters:
 * - type: Filter by document type
 * - status: Filter by processing status
 * - search: Search term for document title/content
 * - page: Page number for pagination
 * - limit: Number of documents per page
 * - sortBy: Field to sort by (default: 'created_at')
 * - sortOrder: 'asc' or 'desc' (default: 'desc')
 */
export const GET = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (req: NextRequest, user: AuthUser, supabase, context) => {
    try {
      if (!context?.params) {
        return NextResponse.json({ error: 'No parameters provided' }, { status: 400 });
      }

      const params = context.params;
      const { id: matterId } = params as { id: string };

      if (!matterId || !z.string().uuid().safeParse(matterId).success) {
        return NextResponse.json({ error: 'Invalid matter ID' }, { status: 400 });
      }

      // Ensure tenantId exists for matter-specific operations
      if (!user.tenantId) {
        return NextResponse.json({ error: 'User is not associated with a tenant' }, { status: 400 });
      }

      // Extract query parameters
      const { searchParams } = new URL(req.url);
      const documentType = searchParams.get('type') || undefined;
      const status = searchParams.get('status') || undefined;
      const searchTerm = searchParams.get('search') || undefined;
      const page = parseInt(searchParams.get('page') || '1', 10);
      const limit = parseInt(searchParams.get('limit') || '20', 10);
      const sortBy = searchParams.get('sortBy') || 'created_at';
      const sortOrder = searchParams.get('sortOrder') === 'asc' ? 'asc' : 'desc';

      // First check if the matter exists and user has access
      const { data: matter, error: matterError } = await supabase
        .schema('tenants')
        .from('matters')
        .select('id')
        .eq('id', matterId)
        .eq('tenant_id', user.tenantId)
        .single();

      if (matterError || !matter) {
        return NextResponse.json({ error: 'Matter not found' }, { status: 404 });
      }

      // Build documents query using case_documents table with matter_id
      let documentsQuery = supabase
        .schema('tenants')
        .from('case_documents')
        .select(`
          id,
          title,
          citation,
          document_type,
          gcs_path,
          sensitive,
          created_at,
          updated_at,
          metadata,
          practice_area,
          document_category,
          subcategory
        `, { count: 'exact' })
        .eq('tenant_id', user.tenantId)
        .eq('matter_id', matterId);

      // Documents are now queried directly from case_documents table with matter_id filter

      // Apply filters
      if (documentType) {
        documentsQuery = documentsQuery.eq('document_type', documentType);
      }
      if (status) {
        documentsQuery = documentsQuery.eq('status', status);
      }
      if (searchTerm) {
        documentsQuery = documentsQuery.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
      }

      // Apply pagination and sorting
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      documentsQuery = documentsQuery
        .range(from, to)
        .order(sortBy, { ascending: sortOrder === 'asc' });

      const { data: documents, error: documentsError, count } = await documentsQuery;

      if (documentsError) {
        console.error('Error fetching documents:', documentsError);
        throw new Error(`Database error: ${documentsError.message}`);
      }

      // Return paginated results
      return NextResponse.json({
        documents: documents || [],
        pagination: {
          currentPage: page,
          totalPages: Math.ceil((count || 0) / limit),
          totalCount: count || 0,
          hasMore: page * limit < (count || 0)
        }
      });
    } catch (error: unknown) {
      console.error('Error in GET /api/matters/[id]/documents:', error);
      return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
    }
  }
);

// Document upload schema
const DocumentUploadSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  document_type: z.string().min(1, 'Document type is required'),
  file_path: z.string().min(1, 'File path is required'),
  file_size: z.number().positive('File size must be positive'),
  mime_type: z.string().min(1, 'MIME type is required'),
  metadata: z.record(z.any()).optional(),
  matter_id: z.string().uuid('Invalid matter ID'),
});

type DocumentUploadRequestBody = z.infer<typeof DocumentUploadSchema>;

/**
 * POST /api/matters/[id]/documents - Upload/create a new document for a matter
 */
export const POST = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (req: NextRequest, user: AuthUser, supabase, context) => {
    try {
      if (!context?.params) {
        return NextResponse.json({ error: 'No parameters provided' }, { status: 400 });
      }

      const params = context.params;
      const { id: matterId } = params as { id: string };

      if (!matterId || !z.string().uuid().safeParse(matterId).success) {
        return NextResponse.json({ error: 'Invalid matter ID' }, { status: 400 });
      }

      // Ensure tenantId exists for matter-specific operations
      if (!user.tenantId) {
        return NextResponse.json({ error: 'User is not associated with a tenant' }, { status: 400 });
      }

      const body: DocumentUploadRequestBody = await req.json();
      body.matter_id = matterId;

      try {
        // Validate document data
        const validatedData = DocumentUploadSchema.parse(body);

        // First check if the matter exists and user has access
        const { data: matter, error: matterError } = await supabase
          .schema('tenants')
          .from('matters')
          .select('id')
          .eq('id', matterId)
          .eq('tenant_id', user.tenantId)
          .single();

        if (matterError || !matter) {
          return NextResponse.json({ error: 'Matter not found' }, { status: 404 });
        }

        // Create the document directly in case_documents table with matter_id
        const { data: createdDocument, error: createError } = await supabase
          .schema('tenants')
          .from('case_documents')
          .insert({
            title: validatedData.title,
            citation: validatedData.description, // Map description to citation
            document_type: validatedData.document_type,
            gcs_path: validatedData.file_path, // Map file_path to gcs_path
            sensitive: false, // Default value
            metadata: validatedData.metadata || {},
            matter_id: matterId,
            tenant_id: user.tenantId,
            created_by: user.id,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select('*')
          .single();

        if (createError) {
          console.error('Error creating document:', createError);
          throw new Error(`Database error: ${createError.message}`);
        }

        return NextResponse.json(createdDocument, { status: 201 });
      } catch (error: unknown) {
        if (error instanceof z.ZodError) {
          return NextResponse.json({
            error: 'Validation failed',
            details: error.errors
          }, { status: 400 });
        }

        throw error;
      }
    } catch (error: unknown) {
      console.error('Error in POST /api/matters/[id]/documents:', error);
      return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
    }
  }
);
