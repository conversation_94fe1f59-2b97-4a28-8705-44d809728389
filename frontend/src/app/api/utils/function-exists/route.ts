import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { createClientAsync } from '@/lib/supabase/server';

/**
 * API endpoint to check if a database function exists
 * GET /api/utils/function-exists?name=function_name
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const functionName = searchParams.get('name');

    if (!functionName) {
      return NextResponse.json({ error: 'Function name is required' }, { status: 400 });
    }

    // In development, avoid RPC probing to prevent noisy errors and 500s
    if (process.env.NODE_ENV !== 'production') {
      return NextResponse.json({
        exists: false,
        functionName,
        message: 'Dev mode: skipping function existence check'
      });
    }

    console.log(`Checking function existence: ${functionName}`);

    // Create Supabase client
    const supabase = await createClientAsync();

    // Check if function exists by trying to call it with test parameters
    let exists = false;
    let errorMessage = '';

    try {
      switch (functionName) {
        case 'is_session_timed_out':
          // This function takes no parameters
          const { error: timeoutError } = await supabase.rpc('is_session_timed_out', {});
          exists = !timeoutError?.message.includes('Could not find the function');
          if (timeoutError && timeoutError.message.includes('Could not find the function')) {
            errorMessage = timeoutError.message;
          }
          break;

        case 'register_device':
          // This function requires parameters - test with dummy data
          const { error: registerError } = await supabase.rpc('register_device', {
            p_fingerprint: 'test-check',
            p_user_agent: 'test-agent',
            p_ip_address: undefined
          });
          exists = !registerError?.message.includes('Could not find the function');
          if (registerError && registerError.message.includes('Could not find the function')) {
            errorMessage = registerError.message;
          }
          break;

        case 'trust_device':
          // This function requires authentication - test with dummy data
          const { error: trustError } = await supabase.rpc('trust_device', {
            p_fingerprint: 'test-check'
          });
          exists = !trustError?.message.includes('Could not find the function');
          if (trustError && trustError.message.includes('Could not find the function')) {
            errorMessage = trustError.message;
          }
          break;

        case 'block_device':
          // This function requires authentication - test with dummy data
          const { error: blockError } = await supabase.rpc('block_device', {
            p_fingerprint: 'test-check'
          });
          exists = !blockError?.message.includes('Could not find the function');
          if (blockError && blockError.message.includes('Could not find the function')) {
            errorMessage = blockError.message;
          }
          break;

        default:
          // For unknown functions, assume they don't exist
          exists = false;
          errorMessage = 'Unknown function';
      }
    } catch (err: any) {
      console.error(`Error testing function ${functionName}:`, err);
      exists = false;
      errorMessage = err.message;
    }

    return NextResponse.json({
      exists,
      functionName,
      message: exists ? 'Function exists and is accessible' : 'Function not found or not accessible',
      error: errorMessage || undefined
    });

  } catch (err: any) {
    console.error(`Error in function-exists route:`, err);
    return NextResponse.json({
      error: 'Internal server error',
      details: err.message
    }, { status: 500 });
  }
}
