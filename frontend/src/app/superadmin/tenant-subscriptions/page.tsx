'use client';

import { useEffect, useState } from 'react';
import { useSupabase } from '@/lib/supabase/provider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Calendar, AlertCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface TenantSubscriptionInfo {
  id: string;
  tenant_id: string;
  tenant_name: string;
  plan_name: string;
  plan_code: string;
  status: string;
  billing_cycle: string;
  trial_end: string | null;
  current_period_end: string;
  addon_count: number;
}

export default function TenantSubscriptionsPage() {
  const { supabase } = useSupabase();
  const [subscriptions, setSubscriptions] = useState<TenantSubscriptionInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedSubscription, setSelectedSubscription] = useState<TenantSubscriptionInfo | null>(null);
  const [extendDays, setExtendDays] = useState(7);
  const [processing, setProcessing] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    async function fetchSubscriptions() {
      try {
        const { data, error } = await supabase.rpc('get_tenant_subscriptions_summary');

        if (error) throw error;

        setSubscriptions(data || []);
      } catch (error) {
        console.error('Error fetching subscriptions:', error);
        toast({
          title: 'Error',
          description: 'Failed to load tenant subscriptions',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }

    fetchSubscriptions();
  }, [supabase, toast]);

  const handleExtendTrial = async () => {
    if (!selectedSubscription) return;

    setProcessing(true);

    try {
      const { data, error } = await supabase.rpc('extend_subscription_trial', {
        p_subscription_id: selectedSubscription.id,
        p_days: extendDays
      });

      if (error) throw error;

      toast({
        title: 'Trial Extended',
        description: `Trial for ${selectedSubscription.tenant_name} extended by ${extendDays} days.`,
      });

      // Refresh subscriptions
      const { data: refreshedData, error: refreshError } = await supabase.rpc('get_tenant_subscriptions_summary');

      if (refreshError) throw refreshError;

      setSubscriptions(refreshedData || []);
    } catch (error) {
      console.error('Error extending trial:', error);
      toast({
        title: 'Error',
        description: 'Failed to extend trial period',
        variant: 'destructive',
      });
    } finally {
      setProcessing(false);
      setSelectedSubscription(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'trialing':
        return <Badge className="bg-blue-500">Trial</Badge>;
      case 'past_due':
        return <Badge className="bg-yellow-500">Past Due</Badge>;
      case 'canceled':
        return <Badge className="bg-red-500">Canceled</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Tenant Subscriptions</h1>

      <Card>
        <CardHeader>
          <CardTitle>Active Subscriptions</CardTitle>
          <CardDescription>
            View and manage tenant subscription details
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tenant</TableHead>
                <TableHead>Plan</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Billing Cycle</TableHead>
                <TableHead>Period End</TableHead>
                <TableHead>Add-ons</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {subscriptions.map(subscription => (
                <TableRow key={subscription.id}>
                  <TableCell className="font-medium">{subscription.tenant_name}</TableCell>
                  <TableCell>{subscription.plan_name}</TableCell>
                  <TableCell>{getStatusBadge(subscription.status)}</TableCell>
                  <TableCell className="capitalize">{subscription.billing_cycle}</TableCell>
                  <TableCell>
                    {subscription.status === 'trialing' && subscription.trial_end
                      ? format(new Date(subscription.trial_end), 'MMM d, yyyy')
                      : format(new Date(subscription.current_period_end), 'MMM d, yyyy')}
                  </TableCell>
                  <TableCell>{subscription.addon_count}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      {subscription.status === 'trialing' && (
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedSubscription(subscription)}
                            >
                              <Calendar className="h-4 w-4 mr-1" />
                              Extend Trial
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Extend Trial Period</DialogTitle>
                              <DialogDescription>
                                Extend the trial period for {selectedSubscription?.tenant_name}
                              </DialogDescription>
                            </DialogHeader>
                            <div className="py-4">
                              <div className="space-y-4">
                                <div className="space-y-2">
                                  <Label htmlFor="days">Additional Days</Label>
                                  <Input
                                    id="days"
                                    type="number"
                                    min="1"
                                    max="30"
                                    value={extendDays}
                                    onChange={(e) => setExtendDays(parseInt(e.target.value))}
                                  />
                                </div>

                                <div className="bg-muted p-3 rounded-md flex items-start space-x-3">
                                  <AlertCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                                  <div className="text-sm">
                                    <p>This will extend the trial period by {extendDays} days.</p>
                                    {selectedSubscription && selectedSubscription.trial_end && (
                                      <p className="mt-1">
                                        Current trial end: {format(new Date(selectedSubscription.trial_end), 'MMMM d, yyyy')}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                            <DialogFooter>
                              <Button variant="outline" onClick={() => setSelectedSubscription(null)}>Cancel</Button>
                              <Button onClick={handleExtendTrial} disabled={processing}>
                                {processing && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                                Extend Trial
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      )}

                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
