'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';

interface TenantQuota {
  id: string;
  tenant_id: string;
  tenant_name?: string;
  max_daily_uploads: number;
  max_monthly_uploads: number;
  max_document_size_mb: number;
  max_concurrent_processing: number;
  plan_tier: string;
  updated_at: string;
}

export default function TenantQuotasPage() {
  const { toast } = useToast();
  const [tenantQuotas, setTenantQuotas] = useState<TenantQuota[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingQuota, setEditingQuota] = useState<TenantQuota | null>(null);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchTenantQuotas();
  }, []);

  async function fetchTenantQuotas() {
    setLoading(true);
    try {
      // First get all tenant quotas
      const { data: quotas, error: quotasError } = await supabase
        .from('tenants.tenant_quotas' as any)
        .select('*')
        .order('updated_at', { ascending: false });

      if (quotasError) throw quotasError;

      // Get tenant names
      const { data: tenants, error: tenantsError } = await supabase
        .from('tenants.firms' as any)
        .select('tenant_id, name');

      if (tenantsError) throw tenantsError;

      // Map tenant names to quotas
      const quotasWithNames = quotas.map((quota: any) => {
        const tenant = tenants.find((t: any) => t.tenant_id === quota.tenant_id);
        return {
          ...quota,
          // @ts-expect-error - Supabase query result type
          tenant_name: tenant?.name || 'Unknown Tenant'
        };
      });

      setTenantQuotas(quotasWithNames);
    } catch (error: any) {
      console.error('Error fetching tenant quotas:', error);
      toast({
        title: 'Error',
        description: 'Failed to load tenant quotas',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }

  async function saveQuota() {
    if (!editingQuota) return;

    setSaving(true);
    try {
      const { error } = await supabase
        .from('tenants.tenant_quotas' as any)
        .update({
          max_daily_uploads: editingQuota.max_daily_uploads,
          max_monthly_uploads: editingQuota.max_monthly_uploads,
          max_document_size_mb: editingQuota.max_document_size_mb,
          max_concurrent_processing: editingQuota.max_concurrent_processing,
          plan_tier: editingQuota.plan_tier,
          updated_at: new Date().toISOString()
        })
        .eq('id', editingQuota.id);

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Tenant quota updated successfully',
      });

      // Refresh the list
      await fetchTenantQuotas();
      setEditingQuota(null);
    } catch (error: any) {
      console.error('Error updating tenant quota:', error);
      toast({
        title: 'Error',
        description: 'Failed to update tenant quota',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Tenant Quotas Management</h1>
      <p className="text-gray-500 mb-6">
        Manage resource allocation and rate limits for each tenant in the system.
      </p>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Tenant Quotas</CardTitle>
              <CardDescription>
                View and modify resource limits for each tenant
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tenant</TableHead>
                    <TableHead>Daily Uploads</TableHead>
                    <TableHead>Monthly Uploads</TableHead>
                    <TableHead>Max Size (MB)</TableHead>
                    <TableHead>Concurrent Processing</TableHead>
                    <TableHead>Plan Tier</TableHead>
                    <TableHead>Last Updated</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tenantQuotas.map((quota) => (
                    <TableRow key={quota.id}>
                      <TableCell className="font-medium">{quota.tenant_name}</TableCell>
                      <TableCell>{quota.max_daily_uploads}</TableCell>
                      <TableCell>{quota.max_monthly_uploads}</TableCell>
                      <TableCell>{quota.max_document_size_mb}</TableCell>
                      <TableCell>{quota.max_concurrent_processing}</TableCell>
                      <TableCell>{quota.plan_tier}</TableCell>
                      <TableCell>{new Date(quota.updated_at).toLocaleString()}</TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingQuota(quota)}
                        >
                          Edit
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {editingQuota && (
            <Card>
              <CardHeader>
                <CardTitle>Edit Tenant Quota</CardTitle>
                <CardDescription>
                  Modify resource limits for {editingQuota.tenant_name}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="daily-uploads">Daily Upload Limit</Label>
                      <Input
                        id="daily-uploads"
                        type="number"
                        value={editingQuota.max_daily_uploads}
                        onChange={(e) => setEditingQuota({
                          ...editingQuota,
                          max_daily_uploads: parseInt(e.target.value)
                        })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="monthly-uploads">Monthly Upload Limit</Label>
                      <Input
                        id="monthly-uploads"
                        type="number"
                        value={editingQuota.max_monthly_uploads}
                        onChange={(e) => setEditingQuota({
                          ...editingQuota,
                          max_monthly_uploads: parseInt(e.target.value)
                        })}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="max-size">Max Document Size (MB)</Label>
                      <Input
                        id="max-size"
                        type="number"
                        value={editingQuota.max_document_size_mb}
                        onChange={(e) => setEditingQuota({
                          ...editingQuota,
                          max_document_size_mb: parseInt(e.target.value)
                        })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="concurrent-processing">Max Concurrent Processing</Label>
                      <Input
                        id="concurrent-processing"
                        type="number"
                        value={editingQuota.max_concurrent_processing}
                        onChange={(e) => setEditingQuota({
                          ...editingQuota,
                          max_concurrent_processing: parseInt(e.target.value)
                        })}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="plan-tier">Plan Tier</Label>
                    <Select
                      value={editingQuota.plan_tier}
                      onValueChange={(value) => setEditingQuota({
                        ...editingQuota,
                        plan_tier: value
                      })}
                    >
                      <SelectTrigger id="plan-tier">
                        <SelectValue placeholder="Select a plan tier" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="free">Free</SelectItem>
                        <SelectItem value="standard">Standard</SelectItem>
                        <SelectItem value="premium">Premium</SelectItem>
                        <SelectItem value="enterprise">Enterprise</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setEditingQuota(null)}>
                  Cancel
                </Button>
                <Button onClick={saveQuota} disabled={saving}>
                  {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
