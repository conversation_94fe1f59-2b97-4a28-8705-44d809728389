'use client';

import { useEffect, useState } from 'react';
import { useSupabase } from '@/lib/supabase/provider';

import type { MultiCountryPlanDTO, MultiCountryAddonDTO } from '@/lib/services/multi-country-subscription-service';
import { MultiCountrySubscriptionService } from '@/lib/services/multi-country-subscription-service';
import type { PlanSaveData } from '@/components/superadmin/MultiCountryPlanForm';
import type { AddonSaveData } from '@/components/superadmin/MultiCountryAddonForm';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Plus, Edit, Trash2, RefreshCw, Globe, CheckCircle, Clock, AlertTriangle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import { Badge } from '@/components/ui/badge';
import MultiCountryPlanForm from '@/components/superadmin/MultiCountryPlanForm';
import MultiCountryAddonForm from '@/components/superadmin/MultiCountryAddonForm';

export default function SubscriptionManagementPage() {
  const { supabase } = useSupabase();

  const [multiCountryPlans, setMultiCountryPlans] = useState<MultiCountryPlanDTO[]>([]);
  const [multiCountryAddons, setMultiCountryAddons] = useState<MultiCountryAddonDTO[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [showPlanForm, setShowPlanForm] = useState(false);
  const [showAddonForm, setShowAddonForm] = useState(false);
  const [editingPlan, setEditingPlan] = useState<MultiCountryPlanDTO | null>(null);
  const [editingAddon, setEditingAddon] = useState<MultiCountryAddonDTO | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const multiCountryService = new MultiCountrySubscriptionService(supabase);

    async function fetchData() {
      try {
        const [multiCountryPlansData, multiCountryAddonsData] = await Promise.all([
          multiCountryService.getMultiCountryPlans(true),
          multiCountryService.getMultiCountryAddons(true)
        ]);

        setMultiCountryPlans(multiCountryPlansData);
        setMultiCountryAddons(multiCountryAddonsData);
      } catch (err) {
        console.error('Error fetching data:', err);
        toast({
          title: 'Error',
          description: 'Failed to load subscription data',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [supabase, toast]);

  // Helper functions
  const handleSyncPlan = async (planId: string) => {
    setSyncing(true);
    try {
      const multiCountryService = new MultiCountrySubscriptionService(supabase);
      const result = await multiCountryService.syncPlanToStripe(planId);

      if (result.success) {
        toast({
          title: 'Success',
          description: result.message,
        });
        // Refresh data
        window.location.reload();
      } else {
        toast({
          title: 'Error',
          description: result.message,
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        title: 'Error',
        description: 'Failed to sync plan',
        variant: 'destructive',
      });
    } finally {
      setSyncing(false);
    }
  };

  const handleSyncAll = async () => {
    setSyncing(true);
    try {
      const multiCountryService = new MultiCountrySubscriptionService(supabase);
      const result = await multiCountryService.syncAllToStripe();

      if (result.success) {
        toast({
          title: 'Success',
          description: result.message,
        });
        // Refresh data
        window.location.reload();
      } else {
        toast({
          title: 'Error',
          description: result.message,
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        title: 'Error',
        description: 'Failed to sync all plans',
        variant: 'destructive',
      });
    } finally {
      setSyncing(false);
    }
  };

  const getSyncStatusIcon = (plan: MultiCountryPlanDTO) => {
    if (plan.stripe_product_id && plan.last_synced_at) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    } else if (plan.stripe_product_id) {
      return <Clock className="h-4 w-4 text-yellow-500" />;
    } else {
      return <AlertTriangle className="h-4 w-4 text-red-500" />;
    }
  };

  const getSyncStatusText = (plan: MultiCountryPlanDTO) => {
    if (plan.stripe_product_id && plan.last_synced_at) {
      const syncDate = new Date(plan.last_synced_at);
      const now = new Date();
      const diffHours = Math.floor((now.getTime() - syncDate.getTime()) / (1000 * 60 * 60));

      if (diffHours < 1) {
        return 'Synced recently';
      } else if (diffHours < 24) {
        return `Synced ${diffHours}h ago`;
      } else {
        const diffDays = Math.floor(diffHours / 24);
        return `Synced ${diffDays}d ago`;
      }
    } else if (plan.stripe_product_id) {
      return 'Product created, prices pending';
    } else {
      return 'Not synced';
    }
  };

  const getPricingSyncStatus = (pricing: { stripe_price_id?: string }[]) => {
    const syncedPrices = pricing.filter(p => p.stripe_price_id).length;
    const totalPrices = pricing.length;

    if (syncedPrices === totalPrices && totalPrices > 0) {
      return { status: 'complete', text: `${syncedPrices}/${totalPrices} prices synced` };
    } else if (syncedPrices > 0) {
      return { status: 'partial', text: `${syncedPrices}/${totalPrices} prices synced` };
    } else {
      return { status: 'none', text: 'No prices synced' };
    }
  };

  const getCountryFlags = (countries: string[]) => {
    const flagMap: Record<string, string> = {
      'US': '🇺🇸',
      'BE': '🇧🇪',
    };
    return countries.map(country => flagMap[country] || country).join(' ');
  };



  // Form handlers
  const handleCreatePlan = (): void => {
    setEditingPlan(null);
    setShowPlanForm(true);
  };

  const handleEditPlan = (plan: MultiCountryPlanDTO): void => {
    setEditingPlan(plan);
    setShowPlanForm(true);
  };

  const handleCreateAddon = (): void => {
    setEditingAddon(null);
    setShowAddonForm(true);
  };

  const handleEditAddon = (addon: MultiCountryAddonDTO): void => {
    setEditingAddon(addon);
    setShowAddonForm(true);
  };

  const handleSavePlan = async (planData: PlanSaveData) => {
    try {
      // This would call the backend API to save the plan
      const response = await fetch('/api/admin/subscriptions/plans', {
        method: editingPlan ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add auth headers as needed
        },
        body: JSON.stringify(editingPlan ? { ...planData, id: editingPlan.id } : planData)
      });

      if (!response.ok) {
        throw new Error(`Failed to save plan: ${response.statusText}`);
      }

      // Refresh data
      window.location.reload();
    } catch (error) {
      console.error('Error saving plan:', error);
      throw error;
    }
  };

  const handleSaveAddon = async (addonData: AddonSaveData) => {
    try {
      // This would call the backend API to save the addon
      const response = await fetch('/api/admin/subscriptions/addons', {
        method: editingAddon ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add auth headers as needed
        },
        body: JSON.stringify(editingAddon ? { ...addonData, id: editingAddon.id } : addonData)
      });

      if (!response.ok) {
        throw new Error(`Failed to save add-on: ${response.statusText}`);
      }

      // Refresh data
      window.location.reload();
    } catch (error) {
      console.error('Error saving add-on:', error);
      throw error;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // Calculate health metrics from multi-country data
  const healthMetrics = {
    totalPlans: multiCountryPlans.length,
    activePlans: multiCountryPlans.filter(p => p.is_active).length,
    totalAddons: multiCountryAddons.length,
    activeAddons: multiCountryAddons.filter(a => a.is_active).length,
    countriesSupported: [...new Set(multiCountryPlans.flatMap(p => p.available_countries))].length,
    syncedPlans: multiCountryPlans.filter(p => p.stripe_product_id).length,
    syncedAddons: multiCountryAddons.filter(a => a.stripe_product_id).length
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Subscription Management</h1>

      {/* Multi-Country Health Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Plans</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{healthMetrics.activePlans}</div>
            <p className="text-xs text-muted-foreground">
              of {healthMetrics.totalPlans} total plans
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Countries Supported</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{healthMetrics.countriesSupported}</div>
            <p className="text-xs text-muted-foreground">
              Multi-country coverage
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stripe Sync Status</CardTitle>
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {healthMetrics.syncedPlans + healthMetrics.syncedAddons}
            </div>
            <p className="text-xs text-muted-foreground">
              of {healthMetrics.totalPlans + healthMetrics.totalAddons} items synced
            </p>
            <div className="mt-2 flex items-center gap-2">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-xs">Synced</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span className="text-xs">Partial</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-xs">Not synced</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Add-ons</CardTitle>
            <Plus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{healthMetrics.activeAddons}</div>
            <p className="text-xs text-muted-foreground">
              of {healthMetrics.totalAddons} total add-ons
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="plans">
        <TabsList>
          <TabsTrigger value="plans">Plans</TabsTrigger>
          <TabsTrigger value="addons">Add-ons</TabsTrigger>
        </TabsList>

        <TabsContent value="plans" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Subscription Plans</h2>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handleSyncAll}
                disabled={syncing}
              >
                {syncing ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Sync All to Stripe
              </Button>
              <Button onClick={handleCreatePlan}>
                <Plus className="h-4 w-4 mr-2" />
                Add Plan
              </Button>
            </div>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Code</TableHead>
                    <TableHead>Countries</TableHead>
                    <TableHead>Multi-Currency Pricing</TableHead>
                    <TableHead>Stripe Sync</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {multiCountryPlans.map(plan => (
                    <TableRow key={plan.id}>
                      <TableCell className="font-medium">{plan.name}</TableCell>
                      <TableCell>{plan.code}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Globe className="h-4 w-4 text-gray-500" />
                          <span>{getCountryFlags(plan.available_countries)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {plan.country_pricing.map(pricing => (
                            <div key={pricing.country_code} className="flex justify-between">
                              <span className="font-mono">
                                {pricing.currency === 'USD' ? '$' : '€'}{pricing.price_monthly}
                                {pricing.tax_inclusive ? ' (incl. tax)' : ' (excl. tax)'}
                              </span>
                            </div>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            {getSyncStatusIcon(plan)}
                            <span className="text-xs text-muted-foreground">
                              {getSyncStatusText(plan)}
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleSyncPlan(plan.id)}
                              disabled={syncing}
                              title="Sync to Stripe"
                            >
                              {syncing ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                <RefreshCw className="h-3 w-3" />
                              )}
                            </Button>
                          </div>
                          <div className="text-xs">
                            {(() => {
                              const pricingStatus = getPricingSyncStatus(plan.country_pricing);
                              return (
                                <span className={
                                  pricingStatus.status === 'complete' ? 'text-green-600' :
                                  pricingStatus.status === 'partial' ? 'text-yellow-600' :
                                  'text-red-600'
                                }>
                                  {pricingStatus.text}
                                </span>
                              );
                            })()}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {plan.is_active ? (
                          <Badge className="bg-green-500">Active</Badge>
                        ) : (
                          <Badge variant="outline">Inactive</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => handleEditPlan(plan)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="addons" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Subscription Add-ons</h2>
            <Button onClick={handleCreateAddon}>
              <Plus className="h-4 w-4 mr-2" />
              Add Add-on
            </Button>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Code</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Countries</TableHead>
                    <TableHead>Multi-Currency Pricing</TableHead>
                    <TableHead>Stripe Sync</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {multiCountryAddons.map(addon => (
                    <TableRow key={addon.id}>
                      <TableCell className="font-medium">{addon.name}</TableCell>
                      <TableCell>{addon.code}</TableCell>
                      <TableCell>{addon.category}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Globe className="h-4 w-4 text-gray-500" />
                          <span>{getCountryFlags(addon.available_countries)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {addon.country_pricing.map(pricing => (
                            <div key={pricing.country_code} className="flex justify-between">
                              <span className="font-mono">
                                {pricing.currency === 'USD' ? '$' : '€'}{pricing.price_monthly}
                                {pricing.tax_inclusive ? ' (incl. tax)' : ' (excl. tax)'}
                              </span>
                            </div>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            {addon.stripe_product_id ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <AlertTriangle className="h-4 w-4 text-red-500" />
                            )}
                            <span className="text-xs text-muted-foreground">
                              {addon.stripe_product_id ?
                                (addon.last_synced_at ? 'Synced' : 'Product created') :
                                'Not synced'
                              }
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleSyncPlan(addon.id)}
                              disabled={syncing}
                              title="Sync to Stripe"
                            >
                              {syncing ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                <RefreshCw className="h-3 w-3" />
                              )}
                            </Button>
                          </div>
                          <div className="text-xs">
                            {(() => {
                              const pricingStatus = getPricingSyncStatus(addon.country_pricing);
                              return (
                                <span className={
                                  pricingStatus.status === 'complete' ? 'text-green-600' :
                                  pricingStatus.status === 'partial' ? 'text-yellow-600' :
                                  'text-red-600'
                                }>
                                  {pricingStatus.text}
                                </span>
                              );
                            })()}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {addon.is_active ? (
                          <Badge className="bg-green-500">Active</Badge>
                        ) : (
                          <Badge variant="outline">Inactive</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => handleEditAddon(addon)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Multi-Country Plan Form */}
      <MultiCountryPlanForm
        open={showPlanForm}
        onClose={() => setShowPlanForm(false)}
        plan={editingPlan}
        onSave={handleSavePlan}
      />

      {/* Multi-Country Add-on Form */}
      <MultiCountryAddonForm
        open={showAddonForm}
        onClose={() => setShowAddonForm(false)}
        addon={editingAddon}
        onSave={handleSaveAddon}
      />
    </div>
  );
}
