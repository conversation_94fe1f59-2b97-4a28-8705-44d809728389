'use client';

import { useEffect, useState } from 'react';

export default function DebugEnvPage() {
  const [envVars, setEnvVars] = useState<Record<string, string>>({});

  useEffect(() => {
    // Check all NEXT_PUBLIC_ environment variables
    const vars: Record<string, string> = {};
    
    // These should be available on client-side
    vars.NEXT_PUBLIC_DISABLE_TEST_ENDPOINTS = process.env.NEXT_PUBLIC_DISABLE_TEST_ENDPOINTS || 'undefined';
    vars.NODE_ENV = process.env.NODE_ENV || 'undefined';
    vars.VERCEL_ENV = process.env.VERCEL_ENV || 'undefined';
    vars.NEXT_PUBLIC_SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'undefined';
    
    setEnvVars(vars);
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Environment Variables Debug</h1>
      <div className="space-y-2">
        {Object.entries(envVars).map(([key, value]) => (
          <div key={key} className="flex gap-4">
            <span className="font-mono font-bold">{key}:</span>
            <span className="font-mono">{value}</span>
          </div>
        ))}
      </div>
      
      <div className="mt-8">
        <h2 className="text-xl font-bold mb-2">Test Logic</h2>
        <div className="space-y-2">
          <div>
            <span className="font-bold">disableTestEndpoints check:</span>
            <span className="ml-2 font-mono">
              {process.env.NEXT_PUBLIC_DISABLE_TEST_ENDPOINTS === 'true' ? 'TRUE' : 'FALSE'}
            </span>
          </div>
          <div>
            <span className="font-bold">Raw value:</span>
            <span className="ml-2 font-mono">
              "{process.env.NEXT_PUBLIC_DISABLE_TEST_ENDPOINTS}"
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
