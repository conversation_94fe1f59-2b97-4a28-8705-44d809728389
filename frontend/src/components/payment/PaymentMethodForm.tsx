/**
 * Payment Method Form Component
 * 
 * React component for collecting payment method details with country-specific
 * validation and formatting for multi-country operations.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
// import { Button } from '@/components/ui/button'; // Unused import
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CreditCard, Building2, AlertCircle, CheckCircle } from 'lucide-react';
import type {
  PaymentMethodAvailability,
  PaymentMethodValidationRequest,
  PaymentMethodValidationResponse,
  SupportedRegion
} from '@/lib/types/payment-methods';
import { RegionalPaymentMethodService } from '@/lib/services/regional-payment-method-service';

interface PaymentMethodFormProps {
  paymentMethod: PaymentMethodAvailability;
  countryCode: SupportedRegion;
  onFormValid: (isValid: boolean, data: Record<string, unknown>) => void;
  onValidationComplete?: (result: PaymentMethodValidationResponse) => void;
  className?: string;
}

type FormData = Record<string, string>;

interface ValidationError {
  field: string;
  message: string;
}

/**
 * Payment Method Form Component
 */
export function PaymentMethodForm({
  paymentMethod,
  countryCode,
  onFormValid,
  onValidationComplete,
  className = ''
}: PaymentMethodFormProps): React.ReactElement {
  const [formData, setFormData] = useState<FormData>({});
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [isValid, setIsValid] = useState(false);
  
  const paymentService = new RegionalPaymentMethodService();
  
  useEffect(() => {
    // Reset form when payment method changes
    setFormData({});
    setValidationErrors([]);
    setIsValid(false);
    onFormValid(false, {});
  }, [paymentMethod.payment_method_type.id]);
  
  /**
   * Handle form field changes
   */
  const handleFieldChange = (field: string, value: string): void => {
    const newFormData = { ...formData, [field]: value };
    setFormData(newFormData);
    
    // Clear validation errors for this field
    setValidationErrors(prev => prev.filter(error => error.field !== field));
    
    // Trigger validation if form is complete
    if (isFormComplete(newFormData)) {
      validateForm(newFormData);
    } else {
      setIsValid(false);
      onFormValid(false, newFormData);
    }
  };
  
  /**
   * Check if form is complete based on payment method type
   */
  const isFormComplete = (data: FormData): boolean => {
    const code = paymentMethod.payment_method_type.code;
    
    switch (code) {
      case 'card':
        return !!(data.number && data.exp_month && data.exp_year && data.cvc);
      case 'ach':
        return !!(data.routing_number && data.account_number && data.account_holder_type && data.account_type);
      case 'sepa_debit':
        return !!(data.iban);
      case 'ideal':
        return !!(data.bank);
      case 'bancontact':
      case 'sofort':
        return true; // These don't require additional data for setup
      default:
        return false;
    }
  };
  
  /**
   * Validate form data
   */
  const validateForm = async (data: FormData) => {
    try {
      setIsValidating(true);
      
      const request: PaymentMethodValidationRequest = {
        payment_method_code: paymentMethod.payment_method_type.code,
        country_code: countryCode,
        payment_data: data
      };
      
      const result = await paymentService.validatePaymentMethod(request);
      
      if (result.is_valid) {
        setValidationErrors([]);
        setIsValid(true);
        onFormValid(true, result.formatted_data || data);
      } else {
        setValidationErrors(result.validation_errors);
        setIsValid(false);
        onFormValid(false, data);
      }
      
      onValidationComplete?.(result);
      
    } catch (error) {
      console.error('Validation error:', error);
      setValidationErrors([{
        field: 'general',
        message: 'Validation failed. Please check your information and try again.'
      }]);
      setIsValid(false);
      onFormValid(false, data);
    } finally {
      setIsValidating(false);
    }
  };
  
  /**
   * Get validation error for a field
   */
  const getFieldError = (field: string): string | undefined => {
    return validationErrors.find(error => error.field === field)?.message;
  };
  
  /**
   * Render card form fields
   */
  const renderCardForm = () => (
    <div className="space-y-4">
      <div>
        <Label htmlFor="card-number">Card Number</Label>
        <Input
          id="card-number"
          type="text"
          placeholder="1234 5678 9012 3456"
          value={formData.number || ''}
          onChange={(e) => handleFieldChange('number', e.target.value)}
          className={getFieldError('number') ? 'border-red-500' : ''}
        />
        {getFieldError('number') && (
          <p className="text-sm text-red-500 mt-1">{getFieldError('number')}</p>
        )}
      </div>
      
      <div className="grid grid-cols-3 gap-4">
        <div>
          <Label htmlFor="exp-month">Month</Label>
          <Select onValueChange={(value) => handleFieldChange('exp_month', value)}>
            <SelectTrigger>
              <SelectValue placeholder="MM" />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 12 }, (_, i) => (
                <SelectItem key={i + 1} value={String(i + 1).padStart(2, '0')}>
                  {String(i + 1).padStart(2, '0')}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="exp-year">Year</Label>
          <Select onValueChange={(value) => handleFieldChange('exp_year', value)}>
            <SelectTrigger>
              <SelectValue placeholder="YYYY" />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 10 }, (_, i) => {
                const year = new Date().getFullYear() + i;
                return (
                  <SelectItem key={year} value={String(year)}>
                    {year}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="cvc">CVC</Label>
          <Input
            id="cvc"
            type="text"
            placeholder="123"
            maxLength={4}
            value={formData.cvc || ''}
            onChange={(e) => handleFieldChange('cvc', e.target.value)}
            className={getFieldError('cvc') ? 'border-red-500' : ''}
          />
          {getFieldError('cvc') && (
            <p className="text-sm text-red-500 mt-1">{getFieldError('cvc')}</p>
          )}
        </div>
      </div>
    </div>
  );
  
  /**
   * Render ACH form fields
   */
  const renderACHForm = () => (
    <div className="space-y-4">
      <div>
        <Label htmlFor="routing-number">Routing Number</Label>
        <Input
          id="routing-number"
          type="text"
          placeholder="*********"
          maxLength={9}
          value={formData.routing_number || ''}
          onChange={(e) => handleFieldChange('routing_number', e.target.value)}
          className={getFieldError('routing_number') ? 'border-red-500' : ''}
        />
        {getFieldError('routing_number') && (
          <p className="text-sm text-red-500 mt-1">{getFieldError('routing_number')}</p>
        )}
      </div>
      
      <div>
        <Label htmlFor="account-number">Account Number</Label>
        <Input
          id="account-number"
          type="text"
          placeholder="Account number"
          value={formData.account_number || ''}
          onChange={(e) => handleFieldChange('account_number', e.target.value)}
          className={getFieldError('account_number') ? 'border-red-500' : ''}
        />
        {getFieldError('account_number') && (
          <p className="text-sm text-red-500 mt-1">{getFieldError('account_number')}</p>
        )}
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="account-holder-type">Account Holder</Label>
          <Select onValueChange={(value) => handleFieldChange('account_holder_type', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="individual">Individual</SelectItem>
              <SelectItem value="company">Company</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="account-type">Account Type</Label>
          <Select onValueChange={(value) => handleFieldChange('account_type', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="checking">Checking</SelectItem>
              <SelectItem value="savings">Savings</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
  
  /**
   * Render SEPA form fields
   */
  const renderSEPAForm = () => (
    <div className="space-y-4">
      <div>
        <Label htmlFor="iban">IBAN</Label>
        <Input
          id="iban"
          type="text"
          placeholder="DE89 3704 0044 0532 0130 00"
          value={formData.iban || ''}
          onChange={(e) => handleFieldChange('iban', e.target.value)}
          className={getFieldError('iban') ? 'border-red-500' : ''}
        />
        {getFieldError('iban') && (
          <p className="text-sm text-red-500 mt-1">{getFieldError('iban')}</p>
        )}
        <p className="text-sm text-muted-foreground mt-1">
          Enter your International Bank Account Number
        </p>
      </div>
    </div>
  );
  
  /**
   * Render iDEAL form fields
   */
  const renderIDEALForm = () => (
    <div className="space-y-4">
      <div>
        <Label htmlFor="bank">Select Bank</Label>
        <Select onValueChange={(value) => handleFieldChange('bank', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Choose your bank" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="abn_amro">ABN AMRO</SelectItem>
            <SelectItem value="ing">ING</SelectItem>
            <SelectItem value="rabobank">Rabobank</SelectItem>
            <SelectItem value="sns_bank">SNS Bank</SelectItem>
            <SelectItem value="asn_bank">ASN Bank</SelectItem>
            <SelectItem value="bunq">bunq</SelectItem>
            <SelectItem value="handelsbanken">Handelsbanken</SelectItem>
            <SelectItem value="knab">Knab</SelectItem>
            <SelectItem value="moneyou">Moneyou</SelectItem>
            <SelectItem value="regiobank">RegioBank</SelectItem>
            <SelectItem value="revolut">Revolut</SelectItem>
            <SelectItem value="triodos_bank">Triodos Bank</SelectItem>
            <SelectItem value="van_lanschot">Van Lanschot</SelectItem>
          </SelectContent>
        </Select>
        {getFieldError('bank') && (
          <p className="text-sm text-red-500 mt-1">{getFieldError('bank')}</p>
        )}
      </div>
    </div>
  );
  
  /**
   * Render form based on payment method type
   */
  const renderFormFields = () => {
    const code = paymentMethod.payment_method_type.code;
    
    switch (code) {
      case 'card':
        return renderCardForm();
      case 'ach':
        return renderACHForm();
      case 'sepa_debit':
        return renderSEPAForm();
      case 'ideal':
        return renderIDEALForm();
      case 'bancontact':
      case 'sofort':
        return (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              No additional information required. You&apos;ll be redirected to complete the payment.
            </AlertDescription>
          </Alert>
        );
      default:
        return (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Payment method configuration not available.
            </AlertDescription>
          </Alert>
        );
    }
  };
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {paymentMethod.payment_method_type.code === 'card' ? (
            <CreditCard className="h-5 w-5" />
          ) : (
            <Building2 className="h-5 w-5" />
          )}
          {paymentMethod.payment_method_type.name} Details
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          {paymentMethod.payment_method_type.description}
        </p>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {renderFormFields()}
        
        {/* General validation errors */}
        {validationErrors.filter(error => error.field === 'general').map((error, index) => (
          <Alert key={index} variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error.message}</AlertDescription>
          </Alert>
        ))}
        
        {/* Validation status */}
        {isValidating && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            Validating payment information...
          </div>
        )}
        
        {isValid && !isValidating && (
          <div className="flex items-center gap-2 text-sm text-green-600">
            <CheckCircle className="h-4 w-4" />
            Payment information is valid
          </div>
        )}
        
        {/* Verification notice */}
        {paymentMethod.payment_method_type.requires_verification && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              This payment method requires verification which may take{' '}
              {paymentMethod.payment_method_type.processing_time_days} business day(s).
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}
