'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Database, 
  Globe, 
  RefreshCw, 
  Server,
  TrendingUp,
  Users,
  Zap,
  Bug
} from 'lucide-react';

interface SystemMetrics {
  total_webhooks: number;
  successful_deliveries: number;
  failed_deliveries: number;
  pending_webhooks: number;
  dead_letter_webhooks: number;
  success_rate: number;
  failure_rate: number;
  oldest_pending_hours: number;
}

interface WebhookStats {
  pending: number;
  processing: number;
  success: number;
  failed: number;
  dead_letter: number;
  cancelled: number;
  total: number;
}

interface Alert {
  id: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  message: string;
  created_at: string;
}

interface StripeHealthMetrics {
  status: 'healthy' | 'degraded' | 'unhealthy';
  api_connectivity: boolean;
  webhook_endpoint_status: boolean;
  products_synced: number;
  total_products: number;
  sync_percentage: number;
  last_sync_time?: string;
  recent_errors: number;
}

interface SubscriptionMetrics {
  total_active_subscriptions: number;
  new_subscriptions_today: number;
  new_subscriptions_this_week: number;
  total_mrr: number;
  mrr_growth_rate: number;
  payment_failures_this_week: number;
  subscription_by_country: Record<string, number>;
}

export default function MonitoringDashboard() {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [webhookStats, setWebhookStats] = useState<WebhookStats | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [stripeHealth, setStripeHealth] = useState<StripeHealthMetrics | null>(null);
  const [subscriptionMetrics, setSubscriptionMetrics] = useState<SubscriptionMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      
      // Fetch webhook metrics
      const metricsResponse = await fetch('/api/webhook-monitoring/metrics?hours_back=24');
      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json();
        setMetrics(metricsData);
      }

      // Fetch webhook stats
      const statsResponse = await fetch('/api/webhook-monitoring/stats');
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setWebhookStats(statsData);
      }

      // Fetch current alerts
      const alertsResponse = await fetch('/api/webhook-monitoring/health-check');
      if (alertsResponse.ok) {
        const alertsData = await alertsResponse.json();
        setAlerts(alertsData);
      }

      // Fetch Stripe health metrics
      const stripeHealthResponse = await fetch('/api/admin/monitoring/health');
      if (stripeHealthResponse.ok) {
        const stripeHealthData = await stripeHealthResponse.json();
        setStripeHealth({
          status: stripeHealthData.database_status === 'healthy' && stripeHealthData.stripe_status === 'healthy' ? 'healthy' : 'degraded',
          api_connectivity: stripeHealthData.stripe_status === 'healthy',
          webhook_endpoint_status: stripeHealthData.webhook_status === 'healthy',
          products_synced: 0, // Would be populated from actual data
          total_products: 0,
          sync_percentage: 0,
          recent_errors: 0
        });
      }

      // Fetch subscription metrics
      const subscriptionMetricsResponse = await fetch('/api/admin/monitoring/subscriptions');
      if (subscriptionMetricsResponse.ok) {
        const subscriptionData = await subscriptionMetricsResponse.json();
        setSubscriptionMetrics(subscriptionData);
      }

      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching monitoring data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  const getHealthStatus = () => {
    if (!metrics || !webhookStats) return 'unknown';

    const criticalAlerts = alerts.filter(a => a.severity === 'critical').length;
    const warningAlerts = alerts.filter(a => a.severity === 'warning').length;

    // Include Stripe health in overall status
    const stripeIssues = stripeHealth && (
      !stripeHealth.api_connectivity ||
      !stripeHealth.webhook_endpoint_status ||
      stripeHealth.sync_percentage < 80
    );

    if (criticalAlerts > 0 || (stripeHealth && stripeHealth.status === 'unhealthy')) return 'critical';
    if (warningAlerts > 0 || metrics.failure_rate > 10 || stripeIssues) return 'warning';
    return 'healthy';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-50';
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      case 'critical': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'destructive';
      case 'error': return 'destructive';
      case 'warning': return 'secondary';
      default: return 'outline';
    }
  };

  if (loading && !metrics) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading monitoring data...</span>
      </div>
    );
  }

  const healthStatus = getHealthStatus();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">System Monitoring</h2>
          <p className="text-muted-foreground">
            Real-time system health and performance metrics
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-muted-foreground">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </div>
          <Button onClick={fetchMetrics} disabled={loading} size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor(healthStatus)}`}>
              {healthStatus.charAt(0).toUpperCase() + healthStatus.slice(1)}
            </div>
            <p className="text-xs text-muted-foreground">
              {alerts.length} active alerts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Webhook Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics ? `${metrics.success_rate.toFixed(1)}%` : 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">
              Last 24 hours
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Webhooks</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {webhookStats ? webhookStats.pending : 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">
              In queue
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Webhooks</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics ? metrics.total_webhooks : 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">
              Last 24 hours
            </p>
          </CardContent>
        </Card>

        {/* Stripe Health Cards */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stripe Status</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${
              stripeHealth?.api_connectivity ? 'text-green-600' : 'text-red-600'
            }`}>
              {stripeHealth?.api_connectivity ? 'Connected' : 'Disconnected'}
            </div>
            <p className="text-xs text-muted-foreground">
              API connectivity
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {subscriptionMetrics ? subscriptionMetrics.total_active_subscriptions : 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">
              Current subscribers
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Monitoring */}
      <Tabs defaultValue="webhooks" className="space-y-4">
        <TabsList>
          <TabsTrigger value="webhooks">Webhook Monitoring</TabsTrigger>
          <TabsTrigger value="alerts">Active Alerts</TabsTrigger>
          <TabsTrigger value="system">System Metrics</TabsTrigger>
          <TabsTrigger value="stripe">Stripe Health</TabsTrigger>
          <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
          <TabsTrigger value="errors">Error Reporting</TabsTrigger>
        </TabsList>

        <TabsContent value="webhooks" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Webhook Queue Status</CardTitle>
                <CardDescription>Current webhook delivery queue</CardDescription>
              </CardHeader>
              <CardContent>
                {webhookStats && (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Pending:</span>
                      <Badge variant="outline">{webhookStats.pending}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Processing:</span>
                      <Badge variant="outline">{webhookStats.processing}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Successful:</span>
                      <Badge variant="outline">{webhookStats.success}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Failed:</span>
                      <Badge variant="destructive">{webhookStats.failed}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Dead Letter:</span>
                      <Badge variant="destructive">{webhookStats.dead_letter}</Badge>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>24-hour performance summary</CardDescription>
              </CardHeader>
              <CardContent>
                {metrics && (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Success Rate:</span>
                      <Badge variant="outline">{metrics.success_rate.toFixed(1)}%</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Failure Rate:</span>
                      <Badge variant={metrics.failure_rate > 5 ? "destructive" : "outline"}>
                        {metrics.failure_rate.toFixed(1)}%
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Oldest Pending:</span>
                      <Badge variant={metrics.oldest_pending_hours > 2 ? "destructive" : "outline"}>
                        {metrics.oldest_pending_hours.toFixed(1)}h
                      </Badge>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          {alerts.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center text-muted-foreground">
                  <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                  <p>No active alerts. System is healthy!</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {alerts.map((alert) => (
                <Alert key={alert.id}>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="flex items-center justify-between">
                      <div>
                        <strong>{alert.title}</strong>
                        <p className="text-sm mt-1">{alert.message}</p>
                      </div>
                      <Badge variant={getSeverityColor(alert.severity)}>
                        {alert.severity}
                      </Badge>
                    </div>
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Server className="h-4 w-4 mr-2" />
                  API Health
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">Online</div>
                <p className="text-xs text-muted-foreground">All endpoints responding</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Database className="h-4 w-4 mr-2" />
                  Database
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">Connected</div>
                <p className="text-xs text-muted-foreground">Supabase operational</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Zap className="h-4 w-4 mr-2" />
                  Redis Cache
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">Active</div>
                <p className="text-xs text-muted-foreground">Cache operational</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="stripe" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Stripe Integration Health</CardTitle>
                <CardDescription>Stripe API and webhook status</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span>API Connectivity:</span>
                    <Badge variant={stripeHealth?.api_connectivity ? "default" : "destructive"}>
                      {stripeHealth?.api_connectivity ? 'Connected' : 'Disconnected'}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Webhook Endpoint:</span>
                    <Badge variant={stripeHealth?.webhook_endpoint_status ? "default" : "destructive"}>
                      {stripeHealth?.webhook_endpoint_status ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Products Synced:</span>
                    <Badge variant="outline">
                      {stripeHealth ? `${stripeHealth.products_synced}/${stripeHealth.total_products}` : 'N/A'}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Sync Percentage:</span>
                    <Badge variant={stripeHealth && stripeHealth.sync_percentage > 80 ? "default" : "secondary"}>
                      {stripeHealth ? `${stripeHealth.sync_percentage.toFixed(1)}%` : 'N/A'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Stripe Activity</CardTitle>
                <CardDescription>Last 24 hours</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span>Recent Errors:</span>
                    <Badge variant={stripeHealth && stripeHealth.recent_errors > 0 ? "destructive" : "default"}>
                      {stripeHealth ? stripeHealth.recent_errors : 'N/A'}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Last Sync:</span>
                    <span className="text-sm text-muted-foreground">
                      {stripeHealth?.last_sync_time ? new Date(stripeHealth.last_sync_time).toLocaleString() : 'Never'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Overall Status:</span>
                    <Badge variant={
                      stripeHealth?.status === 'healthy' ? "default" :
                      stripeHealth?.status === 'degraded' ? "secondary" : "destructive"
                    }>
                      {stripeHealth?.status || 'Unknown'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="subscriptions" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Subscription Growth</CardTitle>
                <CardDescription>Recent subscription activity</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span>New Today:</span>
                    <Badge variant="outline">
                      {subscriptionMetrics ? subscriptionMetrics.new_subscriptions_today : 'N/A'}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>New This Week:</span>
                    <Badge variant="outline">
                      {subscriptionMetrics ? subscriptionMetrics.new_subscriptions_this_week : 'N/A'}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Growth Rate:</span>
                    <Badge variant={subscriptionMetrics && subscriptionMetrics.mrr_growth_rate > 0 ? "default" : "secondary"}>
                      {subscriptionMetrics ? `${subscriptionMetrics.mrr_growth_rate.toFixed(1)}%` : 'N/A'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenue Metrics</CardTitle>
                <CardDescription>Monthly recurring revenue</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span>Total MRR:</span>
                    <Badge variant="default">
                      ${subscriptionMetrics ? subscriptionMetrics.total_mrr.toLocaleString() : 'N/A'}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Payment Failures:</span>
                    <Badge variant={subscriptionMetrics && subscriptionMetrics.payment_failures_this_week > 0 ? "destructive" : "default"}>
                      {subscriptionMetrics ? subscriptionMetrics.payment_failures_this_week : 'N/A'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Geographic Distribution</CardTitle>
                <CardDescription>Subscriptions by country</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {subscriptionMetrics && Object.entries(subscriptionMetrics.subscription_by_country).map(([country, count]) => (
                    <div key={country} className="flex justify-between items-center">
                      <span className="flex items-center gap-1">
                        <span>{country === 'US' ? '🇺🇸' : country === 'BE' ? '🇧🇪' : '🌍'}</span>
                        <span>{country}</span>
                      </span>
                      <Badge variant="outline">{count}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="errors" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-2 text-red-500" />
                  Error Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">0.2%</div>
                <p className="text-xs text-muted-foreground">Last 24 hours</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bug className="h-4 w-4 mr-2" />
                  Total Errors
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">42</div>
                <p className="text-xs text-muted-foreground">Last 24 hours</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-4 w-4 mr-2" />
                  Affected Users
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">8</div>
                <p className="text-xs text-muted-foreground">Unique users impacted</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="h-4 w-4 mr-2" />
                  Sentry Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">Active</div>
                <p className="text-xs text-muted-foreground">Error tracking enabled</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Error Reporting Integration</CardTitle>
              <CardDescription>
                Comprehensive error tracking and monitoring system status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="font-medium">Sentry Integration</p>
                      <p className="text-sm text-muted-foreground">Real-time error tracking and alerting</p>
                    </div>
                  </div>
                  <Badge variant="secondary">Active</Badge>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="font-medium">Database Logging</p>
                      <p className="text-sm text-muted-foreground">Error aggregation and analysis</p>
                    </div>
                  </div>
                  <Badge variant="secondary">Active</Badge>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="font-medium">Performance Monitoring</p>
                      <p className="text-sm text-muted-foreground">API response time tracking</p>
                    </div>
                  </div>
                  <Badge variant="secondary">Active</Badge>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <div>
                      <p className="font-medium">Error Dashboard</p>
                      <p className="text-sm text-muted-foreground">Dedicated error reporting interface</p>
                    </div>
                  </div>
                  <Button variant="outline" size="sm" asChild>
                    <a href="/superadmin/error-reporting">View Dashboard</a>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
