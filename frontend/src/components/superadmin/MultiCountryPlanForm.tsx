'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Loader2, DollarSign, Euro } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import type { MultiCountryPlanDTO, CountryPricing } from '@/lib/services/multi-country-subscription-service';

export interface PlanSaveData {
  name: string;
  code: string;
  description: string;
  is_active: boolean;
  is_public: boolean;
  features: {
    max_users: number;
    max_storage: number;
    max_documents: number;
    max_concurrent_processing: number;
    practice_areas: string[];
    ai_features: Record<string, boolean>;
    compliance_level: string;
  };
  available_countries: string[];
  country_pricing: CountryPricing[];
}

interface MultiCountryPlanFormProps {
  open: boolean;
  onClose: () => void;
  plan?: MultiCountryPlanDTO | null;
  onSave: (planData: PlanSaveData) => Promise<void>;
}

interface PlanFormData {
  name: string;
  code: string;
  description: string;
  is_active: boolean;
  is_public: boolean;
  features: {
    max_users: number;
    max_storage: number;
    max_documents: number;
    max_concurrent_processing: number;
    practice_areas: string[];
    ai_features: Record<string, boolean>;
    compliance_level: string;
  };
  available_countries: string[];
  country_pricing: CountryPricing[];
}

const AVAILABLE_COUNTRIES = [
  { code: 'US', name: 'United States', currency: 'USD', flag: '🇺🇸', tax_exclusive: true },
  { code: 'BE', name: 'Belgium', currency: 'EUR', flag: '🇧🇪', tax_exclusive: false }
];

const PRACTICE_AREAS = [
  'Personal Injury & Medical Malpractice',
  'Criminal Defense',
  'Family Law',
  'Estate Planning & Probate',
  'Immigration Law',
  'Real Estate (Residential/Landlord-Tenant)',
  'Bankruptcy'
];

const AI_FEATURES = [
  { key: 'document_analysis', label: 'Document Analysis' },
  { key: 'legal_research', label: 'Legal Research' },
  { key: 'case_summarization', label: 'Case Summarization' },
  { key: 'contract_review', label: 'Contract Review' },
  { key: 'ai_receptionist', label: 'AI Receptionist' },
  { key: 'voice_agents', label: 'Voice Agents' }
];

export default function MultiCountryPlanForm({ open, onClose, plan, onSave }: MultiCountryPlanFormProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<PlanFormData>({
    name: '',
    code: '',
    description: '',
    is_active: true,
    is_public: true,
    features: {
      max_users: 1,
      max_storage: 10,
      max_documents: 1000,
      max_concurrent_processing: 2,
      practice_areas: [],
      ai_features: {},
      compliance_level: 'standard'
    },
    available_countries: ['US'],
    country_pricing: [
      {
        country_code: 'US',
        currency: 'USD',
        price_monthly: 99,
        price_yearly: 990,
        tax_inclusive: false,
        is_active: true
      }
    ]
  });

  // Initialize form data when plan changes
  useEffect(() => {
    if (plan) {
      setFormData({
        name: plan.name,
        code: plan.code,
        description: plan.description || '',
        is_active: plan.is_active,
        is_public: plan.is_public,
        features: (plan.features as {
          max_users: number;
          max_storage: number;
          max_documents: number;
          max_concurrent_processing: number;
          practice_areas: string[];
          ai_features: Record<string, boolean>;
          compliance_level: string;
        }) || {
          max_users: 1,
          max_storage: 10,
          max_documents: 1000,
          max_concurrent_processing: 2,
          practice_areas: [],
          ai_features: {},
          compliance_level: 'standard'
        },
        available_countries: plan.available_countries,
        country_pricing: plan.country_pricing
      });
    } else {
      // Reset to default for new plan
      setFormData({
        name: '',
        code: '',
        description: '',
        is_active: true,
        is_public: true,
        features: {
          max_users: 1,
          max_storage: 10,
          max_documents: 1000,
          max_concurrent_processing: 2,
          practice_areas: [],
          ai_features: {},
          compliance_level: 'standard'
        },
        available_countries: ['US'],
        country_pricing: [
          {
            country_code: 'US',
            currency: 'USD',
            price_monthly: 99,
            price_yearly: 990,
            tax_inclusive: false,
            is_active: true
          }
        ]
      });
    }
  }, [plan]);

  const handleCountryToggle = (countryCode: string, enabled: boolean): void => {
    if (enabled) {
      // Add country
      const country = AVAILABLE_COUNTRIES.find(c => c.code === countryCode);
      if (country) {
        setFormData(prev => ({
          ...prev,
          available_countries: [...prev.available_countries, countryCode],
          country_pricing: [
            ...prev.country_pricing,
            {
              country_code: countryCode,
              currency: country.currency,
              price_monthly: 99,
              price_yearly: 990,
              tax_inclusive: !country.tax_exclusive,
              is_active: true
            }
          ]
        }));
      }
    } else {
      // Remove country
      setFormData(prev => ({
        ...prev,
        available_countries: prev.available_countries.filter(c => c !== countryCode),
        country_pricing: prev.country_pricing.filter(p => p.country_code !== countryCode)
      }));
    }
  };

  const handlePricingChange = (countryCode: string, field: keyof CountryPricing, value: string | number | boolean): void => {
    setFormData(prev => ({
      ...prev,
      country_pricing: prev.country_pricing.map(pricing =>
        pricing.country_code === countryCode
          ? { ...pricing, [field]: value }
          : pricing
      )
    }));
  };

  const handleFeatureChange = (field: string, value: string | number | string[]): void => {
    setFormData(prev => ({
      ...prev,
      features: {
        ...prev.features,
        [field]: value
      }
    }));
  };

  const handlePracticeAreaToggle = (area: string, enabled: boolean): void => {
    setFormData(prev => ({
      ...prev,
      features: {
        ...prev.features,
        practice_areas: enabled
          ? [...prev.features.practice_areas, area]
          : prev.features.practice_areas.filter(a => a !== area)
      }
    }));
  };

  const handleAIFeatureToggle = (feature: string, enabled: boolean): void => {
    setFormData(prev => ({
      ...prev,
      features: {
        ...prev.features,
        ai_features: {
          ...prev.features.ai_features,
          [feature]: enabled
        }
      }
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Validate form
      if (!formData.name || !formData.code) {
        toast({
          title: 'Validation Error',
          description: 'Name and code are required',
          variant: 'destructive',
        });
        return;
      }

      if (formData.available_countries.length === 0) {
        toast({
          title: 'Validation Error',
          description: 'At least one country must be selected',
          variant: 'destructive',
        });
        return;
      }

      await onSave(formData);
      onClose();
      
      toast({
        title: 'Success',
        description: plan ? 'Plan updated successfully' : 'Plan created successfully',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save plan',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {plan ? 'Edit Subscription Plan' : 'Create New Subscription Plan'}
          </DialogTitle>
          <DialogDescription>
            Configure multi-country pricing and features for your subscription plan.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="features">Features</TabsTrigger>
              <TabsTrigger value="countries">Countries</TabsTrigger>
              <TabsTrigger value="pricing">Pricing</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Plan Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Team Plan"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="code">Plan Code</Label>
                  <Input
                    id="code"
                    value={formData.code}
                    onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
                    placeholder="e.g., team"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe the plan features and benefits"
                  rows={3}
                />
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                  />
                  <Label htmlFor="is_active">Active</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_public"
                    checked={formData.is_public}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_public: checked }))}
                  />
                  <Label htmlFor="is_public">Public</Label>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="features" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="max_users">Max Users</Label>
                  <Input
                    id="max_users"
                    type="number"
                    value={formData.features.max_users}
                    onChange={(e) => handleFeatureChange('max_users', parseInt(e.target.value))}
                    min="1"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max_storage">Max Storage (GB)</Label>
                  <Input
                    id="max_storage"
                    type="number"
                    value={formData.features.max_storage}
                    onChange={(e) => handleFeatureChange('max_storage', parseInt(e.target.value))}
                    min="1"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max_documents">Max Documents</Label>
                  <Input
                    id="max_documents"
                    type="number"
                    value={formData.features.max_documents}
                    onChange={(e) => handleFeatureChange('max_documents', parseInt(e.target.value))}
                    min="1"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max_concurrent_processing">Max Concurrent Processing</Label>
                  <Input
                    id="max_concurrent_processing"
                    type="number"
                    value={formData.features.max_concurrent_processing}
                    onChange={(e) => handleFeatureChange('max_concurrent_processing', parseInt(e.target.value))}
                    min="1"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Practice Areas</Label>
                <div className="grid grid-cols-2 gap-2">
                  {PRACTICE_AREAS.map(area => (
                    <div key={area} className="flex items-center space-x-2">
                      <Checkbox
                        id={area}
                        checked={formData.features.practice_areas.includes(area)}
                        onCheckedChange={(checked) => handlePracticeAreaToggle(area, checked as boolean)}
                      />
                      <Label htmlFor={area} className="text-sm">{area}</Label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label>AI Features</Label>
                <div className="grid grid-cols-2 gap-2">
                  {AI_FEATURES.map(feature => (
                    <div key={feature.key} className="flex items-center space-x-2">
                      <Checkbox
                        id={feature.key}
                        checked={formData.features.ai_features[feature.key] || false}
                        onCheckedChange={(checked) => handleAIFeatureToggle(feature.key, checked as boolean)}
                      />
                      <Label htmlFor={feature.key} className="text-sm">{feature.label}</Label>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="countries" className="space-y-4">
              <div className="space-y-4">
                <Label>Available Countries</Label>
                {AVAILABLE_COUNTRIES.map(country => (
                  <Card key={country.code}>
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-2xl">{country.flag}</span>
                          <div>
                            <CardTitle className="text-base">{country.name}</CardTitle>
                            <CardDescription>Currency: {country.currency}</CardDescription>
                          </div>
                        </div>
                        <Switch
                          checked={formData.available_countries.includes(country.code)}
                          onCheckedChange={(checked) => handleCountryToggle(country.code, checked)}
                        />
                      </div>
                    </CardHeader>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="pricing" className="space-y-4">
              <div className="space-y-4">
                <Label>Multi-Country Pricing</Label>
                {formData.country_pricing.map(pricing => {
                  const country = AVAILABLE_COUNTRIES.find(c => c.code === pricing.country_code);
                  return (
                    <Card key={pricing.country_code}>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <span>{country?.flag}</span>
                          <span>{country?.name}</span>
                          <Badge variant="outline">{pricing.currency}</Badge>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label>Monthly Price ({pricing.currency})</Label>
                            <div className="relative">
                              {pricing.currency === 'USD' ? (
                                <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              ) : (
                                <Euro className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              )}
                              <Input
                                type="number"
                                value={pricing.price_monthly}
                                onChange={(e) => handlePricingChange(pricing.country_code, 'price_monthly', parseFloat(e.target.value))}
                                className="pl-10"
                                step="0.01"
                                min="0"
                              />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label>Yearly Price ({pricing.currency})</Label>
                            <div className="relative">
                              {pricing.currency === 'USD' ? (
                                <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              ) : (
                                <Euro className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              )}
                              <Input
                                type="number"
                                value={pricing.price_yearly}
                                onChange={(e) => handlePricingChange(pricing.country_code, 'price_yearly', parseFloat(e.target.value))}
                                className="pl-10"
                                step="0.01"
                                min="0"
                              />
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={pricing.tax_inclusive}
                            onCheckedChange={(checked) => handlePricingChange(pricing.country_code, 'tax_inclusive', checked)}
                          />
                          <Label>Tax Inclusive</Label>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {plan ? 'Update Plan' : 'Create Plan'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
