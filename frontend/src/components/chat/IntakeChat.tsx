'use client';

import "@copilotkit/react-ui/styles.css";
import { CompatCopilotChat } from "@/lib/utils/chat-compat";
import { useSubmitIntakeAction } from "@/lib/actions/intake-actions";
import { useState, useCallback, useEffect } from "react";
import { ErrorBoundary } from 'react-error-boundary';
import { useToast } from "@/components/ui/use-toast";
import { useCopilotContext } from "@copilotkit/react-core";

export default function IntakeChat() {
  // Register the intake submission action with CopilotKit
  useSubmitIntakeAction();

  // Get the copilot context for potential customization
  useCopilotContext();

  // Toast for user feedback
  const { toast } = useToast();

  // State for tracking submission status
  const [submissionResult, setSubmissionResult] = useState<{
    success?: boolean;
    clientId?: string;
    intakeId?: string;
    error?: string;
  } | null>(null);

  // Listen for successful intake submissions
  useEffect(() => {
    const handleIntakeSubmission = (event: CustomEvent): void => {
      if (event.detail?.data) {
        const { success, client_id, intake_id, error } = event.detail.data;
        setSubmissionResult({
          success,
          clientId: client_id,
          intakeId: intake_id,
          error
        });

        if (success) {
          toast({
            title: "Success",
            description: "Your intake information has been submitted successfully!",
            variant: "default",
          });
        } else {
          toast({
            title: "Error",
            description: error || "Failed to submit intake information",
            variant: "destructive",
          });
        }
      }
    };

    // Add event listener for intake submission events
    window.addEventListener('intakeSubmitted', handleIntakeSubmission as EventListener);

    return () => {
      window.removeEventListener('intakeSubmitted', handleIntakeSubmission as EventListener);
    };
  }, [toast]);

  // Handle errors in the CopilotChat component
  const handleError = useCallback((error: Error) => {
    console.error('IntakeChat Error:', error);
    toast({
      title: "Error",
      description: "There was an error processing your request. Please try again.",
      variant: "destructive",
    });
  }, [toast]);

  // Legal intake prompts for different practice areas
  const legalPrompts = [
    "I was in a car accident recently",
    "I need help with a criminal charge",
    "I'm going through a divorce",
    "I was injured at work",
    "I need help with child custody",
    "I was arrested for DUI"
  ];

  return (
    <div className="h-[600px] relative flex flex-col">
      <ErrorBoundary
        fallback={<div className="p-4 text-red-600">Something went wrong with the intake chat. Please refresh the page and try again.</div>}
        onError={handleError}
      >
        <CompatCopilotChat
          className="w-full h-full"
          placeholder="Tell me about your legal matter..."
          suggestions={legalPrompts}
          context={{
            currentView: "legal_intake",
            userRole: "client",
            jurisdiction: "Texas",
            practiceAreas: ["Personal Injury & Civil Litigation", "Criminal Defense", "Family Law"],
          }}
          initialMessage="Hello! I am AiLex, your digital assistant for legal intake. I can help with personal injury, criminal defense, and family law matters. How can I help you today?"
          onError={handleError}
          agent="intake_agent"
        />
      </ErrorBoundary>

      {submissionResult && (
        <div className="p-4 mt-2 border rounded-md bg-background shadow">
          {submissionResult.success ? (
            <div className="text-sm">
              <p className="font-medium text-green-600">Intake submitted successfully!</p>
              <p>Client ID: {submissionResult.clientId}</p>
              <p>Intake ID: {submissionResult.intakeId}</p>
            </div>
          ) : (
            <div className="text-sm">
              <p className="font-medium text-red-600">Submission failed</p>
              <p>{submissionResult.error}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
