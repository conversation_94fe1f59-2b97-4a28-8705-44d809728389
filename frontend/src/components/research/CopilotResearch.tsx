/**
 * Custom research components that wrap CopilotKit's UI components for legal research
 * This file handles compatibility issues with TypeScript and component props.
 */

import React, { useState } from 'react';
import { CopilotChat } from "@copilotkit/react-ui";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ErrorBoundary } from 'react-error-boundary';
import { ResearchProgressWidget } from './ResearchProgressWidget';

// For TypeScript, we use the 'any' type to bypass type checking for props
// that are dynamically supported by CopilotKit but not recognized by TypeScript
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const LegalResearchChat: React.FC<any> = (props) => {
  return <CopilotChat {...props} />;
};

// Sidebar research component
export function ResearchSidebar(): React.ReactElement {
  const [isVisible, setIsVisible] = useState(false);
  const [currentThreadId, setCurrentThreadId] = useState<string>('default-thread');

  const legalResearchPrompts = [
    "What's the statute of limitations for personal injury in Texas?",
    "Summarize recent Texas case law on premises liability",
    "What are the damages caps for medical malpractice in Texas?",
    "Explain the comparative negligence laws in Texas",
    "What evidence is needed to prove negligence in a car accident case?"
  ];

  return (
    <div className="relative">
      {/* Toggle button for mobile/responsive design */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 md:hidden bg-primary text-white rounded-full p-4 shadow-lg z-50"
      >
        {isVisible ? "Close Research" : "Legal Research"}
      </button>

      {/* Sidebar container with responsive visibility */}
      <div className={`${
        isVisible ? 'translate-x-0' : 'translate-x-full md:translate-x-0'
      } fixed right-0 top-0 bottom-0 z-40 transition-transform duration-300 ease-in-out flex`}>

        {/* Progress Widget */}
        <div className="w-80 border-l bg-background p-4 overflow-y-auto">
          <ResearchProgressWidget
            threadId={currentThreadId}
            showConnectionStatus={true}
            showCitations={true}
            onNotificationClick={(notification) => {
              console.log('Notification clicked:', notification);
            }}
          />
        </div>

        {/* Chat Interface */}
        <ErrorBoundary
          fallback={<div className="p-4 text-red-500">Failed to load research assistant</div>}
          onError={(error: Error) => console.error('Research sidebar error:', error)}
        >
          <LegalResearchChat
            className="h-full w-[350px] md:w-[400px] border-l shadow-lg bg-background"
            displayName="Legal Research Assistant"
            placeholder="Ask me a legal research question..."
            suggestions={legalResearchPrompts}
            context={{
              jurisdiction: "Texas",
              practiceArea: "Personal Injury",
              userRole: "attorney",
              agentType: "research_agent",
              threadId: currentThreadId
            }}
            initialMessage="I can help with Texas legal research questions related to personal injury law. I'll search relevant statutes, case law, and legal principles to provide accurate answers with proper citations."
          />
        </ErrorBoundary>
      </div>
    </div>
  );
}

// Case-integrated research component
interface CaseContextResearchProps {
  caseId: string;
  caseTitle: string;
  caseType?: string;
  jurisdiction?: string;
  documents?: {id: string; title: string; type: string}[];
}

export function CaseContextResearch({
  caseId,
  caseTitle,
  caseType = "Personal Injury",
  jurisdiction = "Texas",
  documents = []
}: CaseContextResearchProps): React.ReactElement {
  const [query, setQuery] = useState("");
  const [isResearching, setIsResearching] = useState(false);
  const [results, setResults] = useState<string | null>(null);
  const [threadId, setThreadId] = useState<string>(`case-${caseId}-${Date.now()}`);

  const handleResearch = async () => {
    if (!query.trim()) return;

    setIsResearching(true);
    setResults(null);

    try {
      // This will be handled by CopilotKit and our FastAPI backend
      // The UI will update automatically via CopilotKit
      setIsResearching(false);
    } catch (error) {
      console.error("Research error:", error);
      setIsResearching(false);
      setResults("An error occurred during research. Please try again.");
    }
  };

  const handleClear = (): void => {
    setQuery("");
    setResults(null);
  };

  return (
    <Card className="w-full shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium flex items-center justify-between">
          <span>Case-Specific Legal Research</span>
          {results && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClear}
              className="h-8 px-2 text-muted-foreground"
            >
              New Research
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Progress Widget */}
          <ResearchProgressWidget
            threadId={threadId}
            className="mb-4"
            showConnectionStatus={true}
            showCitations={true}
            onNotificationClick={(notification) => {
              console.log('Case research notification:', notification);
            }}
          />

          <div className="relative">
            <ErrorBoundary
              fallback={<div className="p-4 text-red-500">Failed to load research component</div>}
            >
              <LegalResearchChat
                className="min-h-[300px] w-full border rounded-md"
                placeholder="Ask a legal question related to this case..."
                context={{
                  caseId,
                  caseTitle,
                  caseType,
                  jurisdiction,
                  documentCount: documents.length,
                  agentType: "research_agent",
                  threadId: threadId
                }}
                initialMessage={`I'm ready to help with legal research for your case "${caseTitle}". This is a ${caseType} case in ${jurisdiction} jurisdiction. Ask me any legal questions related to this case.`}
              />
            </ErrorBoundary>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
