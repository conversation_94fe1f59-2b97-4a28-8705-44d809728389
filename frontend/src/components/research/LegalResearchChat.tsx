/**
 * Research components that integrate with your existing CopilotKit implementation
 */
import React, { useState } from 'react';
import { CopilotChat } from "@copilotkit/react-ui";
import { ErrorBoundary } from 'react-error-boundary';

// This helps bypass TypeScript errors by allowing any props
type CopilotChatAnyProps = React.ComponentProps<typeof CopilotChat> & Record<string, any>;

/**
 * Legal Research Chat Component
 * Uses the same approach as your existing CopilotChatComponent
 */
export function LegalResearchChat(props: CopilotChatAnyProps): React.ReactElement {
  // Default research-specific prompts
  const defaultPrompts = [
    "What's the statute of limitations for personal injury in Texas?",
    "Summarize recent Texas case law on premises liability",
    "What are the damages caps for medical malpractice in Texas?",
    "Explain the comparative negligence laws in Texas",
    "What evidence is needed to prove negligence in a car accident case?"
  ];

  // Default context for research
  const defaultContext = {
    agentType: "research_agent",
    jurisdiction: "Texas",
    practiceArea: "Personal Injury",
    userRole: "attorney"
  };

  // Default welcome message
  const defaultMessage = "I can help with Texas legal research questions related to personal injury law. I'll search relevant statutes, case law, and legal principles to provide accurate answers with proper citations.";

  // Combine provided props with defaults
  const combinedProps = {
    displayName: "Legal Research Assistant",
    placeholder: "Ask me a legal research question...",
    suggestions: defaultPrompts,
    context: { ...defaultContext, ...props.context },
    initialMessage: defaultMessage,
    ...props
  };

  return <CopilotChat {...combinedProps as any} />;
}

/**
 * Sidebar research component for standalone research
 */
export function ResearchSidebar(): React.ReactElement {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div className="relative">
      {/* Toggle button for mobile/responsive design */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 md:hidden bg-primary text-white rounded-full p-4 shadow-lg z-50"
      >
        {isVisible ? "Close Research" : "Legal Research"}
      </button>

      {/* Sidebar container with responsive visibility */}
      <div className={`${
        isVisible ? 'translate-x-0' : 'translate-x-full md:translate-x-0'
      } fixed right-0 top-0 bottom-0 z-40 transition-transform duration-300 ease-in-out`}>
        <ErrorBoundary
          fallback={<div className="p-4 text-red-500">Failed to load research assistant</div>}
          onError={(error: Error) => console.error('Research sidebar error:', error)}
        >
          <LegalResearchChat
            className="h-full w-[350px] md:w-[400px] border-l shadow-lg bg-background"
          />
        </ErrorBoundary>
      </div>
    </div>
  );
}

/**
 * Case-integrated research component
 */
interface CaseContextResearchProps {
  caseId: string;
  caseTitle: string;
  caseType?: string;
  jurisdiction?: string;
  documents?: {id: string; title: string; type: string}[];
}

export function CaseContextResearch({
  caseId,
  caseTitle,
  caseType = "Personal Injury",
  jurisdiction = "Texas",
  documents = []
}: CaseContextResearchProps): React.ReactElement {

  return (
    <div className="border rounded-md overflow-hidden shadow-sm">
      <div className="p-3 border-b bg-muted/20">
        <h3 className="font-medium">Case-Specific Research: {caseTitle}</h3>
      </div>
      <div className="min-h-[350px]">
        <ErrorBoundary
          fallback={<div className="p-4 text-red-500">Failed to load research component</div>}
        >
          <LegalResearchChat
            className="h-full w-full"
            context={{
              caseId,
              caseTitle,
              caseType,
              jurisdiction,
              documentCount: documents.length,
              agentType: "research_agent"
            }}
            initialMessage={`I'm ready to help with legal research for your case "${caseTitle}". This is a ${caseType} case in ${jurisdiction} jurisdiction with ${documents.length} associated documents. How can I assist you?`}
          />
        </ErrorBoundary>
      </div>
    </div>
  );
}
