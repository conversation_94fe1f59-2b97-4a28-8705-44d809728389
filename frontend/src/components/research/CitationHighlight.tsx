/**
 * Citation Highlight Component
 * 
 * Provides visual highlighting for citations with confidence indicators,
 * tooltips, and interactive features for the Research Agent.
 * 
 * Features:
 * - Confidence-based color coding
 * - Interactive tooltips with citation details
 * - Click-to-view source functionality
 * - Accessibility support
 * - Responsive design
 */

import React, { useState } from 'react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { 
  ExternalLink, 
  BookOpen, 
  Scale, 
  MapPin,
  Calendar,
  AlertTriangle,
  CheckCircle2
} from "lucide-react";

export interface Citation {
  id: string;
  text: string;
  source: string;
  confidence: number;
  type: string;
  jurisdiction?: string;
  url?: string;
  date?: string;
  court?: string;
  case_number?: string;
  summary?: string;
}

interface CitationHighlightProps {
  citation: Citation;
  children: React.ReactNode;
  className?: string;
  showTooltip?: boolean;
  showPopover?: boolean;
  onCitationClick?: (citation: Citation) => void;
}

export function CitationHighlight({
  citation,
  children,
  className = "",
  showTooltip = true,
  showPopover = false,
  onCitationClick
}: CitationHighlightProps): React.ReactElement {
  
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);

  // Get confidence-based styling
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'border-green-500 bg-green-50 text-green-900';
    if (confidence >= 0.6) return 'border-yellow-500 bg-yellow-50 text-yellow-900';
    return 'border-red-500 bg-red-50 text-red-900';
  };

  const getConfidenceBadgeColor = (confidence: number) => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-800 border-green-300';
    if (confidence >= 0.6) return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    return 'bg-red-100 text-red-800 border-red-300';
  };

  const getConfidenceIcon = (confidence: number) => {
    if (confidence >= 0.8) return <CheckCircle2 className="w-3 h-3" />;
    if (confidence >= 0.6) return <AlertTriangle className="w-3 h-3" />;
    return <AlertTriangle className="w-3 h-3" />;
  };

  // Get citation type icon
  const getCitationTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'case':
      case 'court':
        return <Scale className="w-4 h-4" />;
      case 'statute':
      case 'law':
        return <BookOpen className="w-4 h-4" />;
      default:
        return <BookOpen className="w-4 h-4" />;
    }
  };

  // Handle citation click
  const handleCitationClick = (): void => {
    if (onCitationClick) {
      onCitationClick(citation);
    }
    if (citation.url) {
      window.open(citation.url, '_blank', 'noopener,noreferrer');
    }
  };

  // Tooltip content
  const tooltipContent = (
    <div className="max-w-xs space-y-2">
      <div className="flex items-center gap-2">
        {getCitationTypeIcon(citation.type)}
        <span className="font-medium">{citation.source}</span>
      </div>
      
      <div className="flex items-center gap-2">
        {getConfidenceIcon(citation.confidence)}
        <span className="text-sm">
          Confidence: {Math.round(citation.confidence * 100)}%
        </span>
      </div>
      
      {citation.jurisdiction && (
        <div className="flex items-center gap-2">
          <MapPin className="w-3 h-3" />
          <span className="text-sm">{citation.jurisdiction}</span>
        </div>
      )}
      
      {citation.date && (
        <div className="flex items-center gap-2">
          <Calendar className="w-3 h-3" />
          <span className="text-sm">{citation.date}</span>
        </div>
      )}
      
      <p className="text-xs text-muted-foreground">
        Click to view source
      </p>
    </div>
  );

  // Popover content
  const popoverContent = (
    <div className="w-80 space-y-3">
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-2">
          {getCitationTypeIcon(citation.type)}
          <div>
            <h4 className="font-medium">{citation.source}</h4>
            <p className="text-sm text-muted-foreground">{citation.type}</p>
          </div>
        </div>
        
        <Badge 
          variant="outline" 
          className={getConfidenceBadgeColor(citation.confidence)}
        >
          {Math.round(citation.confidence * 100)}%
        </Badge>
      </div>

      {citation.summary && (
        <p className="text-sm">{citation.summary}</p>
      )}

      <div className="grid grid-cols-2 gap-2 text-sm">
        {citation.jurisdiction && (
          <div className="flex items-center gap-1">
            <MapPin className="w-3 h-3" />
            <span>{citation.jurisdiction}</span>
          </div>
        )}
        
        {citation.date && (
          <div className="flex items-center gap-1">
            <Calendar className="w-3 h-3" />
            <span>{citation.date}</span>
          </div>
        )}
        
        {citation.court && (
          <div className="flex items-center gap-1">
            <Scale className="w-3 h-3" />
            <span>{citation.court}</span>
          </div>
        )}
        
        {citation.case_number && (
          <div className="flex items-center gap-1">
            <BookOpen className="w-3 h-3" />
            <span>{citation.case_number}</span>
          </div>
        )}
      </div>

      <div className="flex gap-2">
        <Button
          size="sm"
          variant="outline"
          onClick={handleCitationClick}
          className="flex-1"
        >
          <ExternalLink className="w-3 h-3 mr-1" />
          View Source
        </Button>
        
        <Button
          size="sm"
          variant="ghost"
          onClick={() => setIsPopoverOpen(false)}
        >
          Close
        </Button>
      </div>
    </div>
  );

  // Base citation element
  const citationElement = (
    <span
      className={`
        inline-block px-1 py-0.5 rounded border-b-2 cursor-pointer
        transition-all duration-200 hover:shadow-sm
        ${getConfidenceColor(citation.confidence)}
        ${className}
      `}
      onClick={handleCitationClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleCitationClick();
        }
      }}
      aria-label={`Citation: ${citation.source} (${Math.round(citation.confidence * 100)}% confidence)`}
    >
      {children}
    </span>
  );

  // Wrap with tooltip if enabled
  if (showTooltip && !showPopover) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {citationElement}
          </TooltipTrigger>
          <TooltipContent side="top" className="max-w-xs">
            {tooltipContent}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Wrap with popover if enabled
  if (showPopover) {
    return (
      <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
        <PopoverTrigger asChild>
          {citationElement}
        </PopoverTrigger>
        <PopoverContent side="top" className="w-80">
          {popoverContent}
        </PopoverContent>
      </Popover>
    );
  }

  // Return plain citation element
  return citationElement;
}

// Helper component for rendering text with citations
interface TextWithCitationsProps {
  text: string;
  citations: Citation[];
  className?: string;
  onCitationClick?: (citation: Citation) => void;
}

export function TextWithCitations({
  text,
  citations,
  className = "",
  onCitationClick
}: TextWithCitationsProps): React.ReactElement {
  
  // Simple implementation - in a real app, you'd want more sophisticated text parsing
  // to identify citation markers and replace them with highlighted components
  
  let processedText = text;
  const citationElements: React.ReactNode[] = [];
  
  citations.forEach((citation, index) => {
    // Look for citation markers like [1], [2], etc.
    const marker = `[${index + 1}]`;
    if (processedText.includes(marker)) {
      const parts = processedText.split(marker);
      processedText = parts.join(`__CITATION_${index}__`);
    }
  });
  
  // Split text and insert citation components
  const textParts = processedText.split(/__CITATION_(\d+)__/);
  
  return (
    <span className={className}>
      {textParts.map((part, index) => {
        if (/^\d+$/.exec(part)) {
          const citationIndex = parseInt(part);
          const citation = citations[citationIndex];
          if (citation) {
            return (
              <CitationHighlight
                key={`citation-${citationIndex}`}
                citation={citation}
                onCitationClick={onCitationClick}
              >
                [{citationIndex + 1}]
              </CitationHighlight>
            );
          }
        }
        return part;
      })}
    </span>
  );
}
