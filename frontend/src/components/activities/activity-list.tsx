import React, { useState, useEffect } from 'react';
import { ActivityCard } from './activity-card';
import type { Activity } from '@/lib/ml/features';
import { activityPriorityService, PriorityLevel } from '@/lib/services/activity-priority-service';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
// import { PriorityBadge } from '@/components/ui/priority-badge'; // Unused import
import { Skeleton } from '@/components/ui/skeleton';

interface ActivityListProps {
  activities: Activity[];
  onViewActivity?: (activity: Activity) => void;
  onCompleteActivity?: (activity: Activity) => void;
  loading?: boolean;
  className?: string;
}

/**
 * Activity List Component
 *
 * Displays a list of activities sorted by priority with filtering options.
 */
export function ActivityList({
  activities,
  onViewActivity,
  onCompleteActivity,
  loading = false,
  className
}: ActivityListProps): React.ReactElement {
  const [prioritizedActivities, setPrioritizedActivities] = useState<(Activity & { priority: PriorityLevel })[]>([]);
  const [activeFilter, setActiveFilter] = useState<string>("all");
  const [processingActivities, setProcessingActivities] = useState<boolean>(true);

  // Process activities and get priorities
  useEffect(() => {
    const processActivities = async () => {
      if (loading || !activities.length) {
        setProcessingActivities(false);
        return;
      }

      setProcessingActivities(true);

      try {
        // Initialize model in advance
        await activityPriorityService.initialize();

        // Process activities in batches to avoid UI freezing
        const batchSize = 5;
        const result: (Activity & { priority: PriorityLevel })[] = [];

        for (let i = 0; i < activities.length; i += batchSize) {
          const batch = activities.slice(i, i + batchSize);

          // Process batch in parallel
          const batchResults = await Promise.all(
            batch.map(async (activity) => {
              try {
                const priority = await activityPriorityService.predictPriority(activity);
                return { ...activity, priority };
              } catch (error) {
                console.error(`Error processing activity ${activity.id}:`, error);
                return { ...activity, priority: PriorityLevel.MEDIUM };
              }
            })
          );

          result.push(...batchResults);

          // Allow UI to update between batches
          if (i + batchSize < activities.length) {
            await new Promise(resolve => setTimeout(resolve, 0));
          }
        }

        // Sort by priority (high to low)
        result.sort((a, b) => {
          const scoreA = activityPriorityService.getPriorityScore(a.priority);
          const scoreB = activityPriorityService.getPriorityScore(b.priority);
          return scoreB - scoreA;
        });

        setPrioritizedActivities(result);
      } catch (error) {
        console.error('Error processing activities:', error);

        // Fallback: just use activities without priority sorting
        const fallback = activities.map(activity => ({
          ...activity,
          priority: PriorityLevel.MEDIUM
        }));

        setPrioritizedActivities(fallback);
      } finally {
        setProcessingActivities(false);
      }
    };

    processActivities();
  }, [activities, loading]);

  // Filter activities based on selected tab
  const filteredActivities = prioritizedActivities.filter(activity => {
    if (activeFilter === "all") return true;
    return activity.priority === activeFilter;
  });

  // Count activities by priority
  const countByPriority = {
    [PriorityLevel.HIGH]: prioritizedActivities.filter(a => a.priority === PriorityLevel.HIGH).length,
    [PriorityLevel.MEDIUM]: prioritizedActivities.filter(a => a.priority === PriorityLevel.MEDIUM).length,
    [PriorityLevel.LOW]: prioritizedActivities.filter(a => a.priority === PriorityLevel.LOW).length,
  };

  // Render loading skeletons
  if (loading) {
    return (
      <div className={className}>
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value={PriorityLevel.HIGH}>High</TabsTrigger>
            <TabsTrigger value={PriorityLevel.MEDIUM}>Medium</TabsTrigger>
            <TabsTrigger value={PriorityLevel.LOW}>Low</TabsTrigger>
          </TabsList>

          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex flex-col space-y-3">
                <Skeleton className="h-[125px] w-full rounded-lg" />
              </div>
            ))}
          </div>
        </Tabs>
      </div>
    );
  }

  // Render empty state
  if (!loading && !processingActivities && prioritizedActivities.length === 0) {
    return (
      <div className={`flex flex-col items-center justify-center p-8 border rounded-lg ${className}`}>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-12 w-12 text-muted-foreground mb-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
          />
        </svg>
        <h3 className="text-lg font-medium">No Activities</h3>
        <p className="text-sm text-muted-foreground text-center mt-2">
          There are no activities to display at this time.
        </p>
      </div>
    );
  }

  return (
    <div className={className}>
      <Tabs
        defaultValue="all"
        value={activeFilter}
        onValueChange={setActiveFilter}
        className="w-full"
      >
        <TabsList className="grid grid-cols-4 mb-4">
          <TabsTrigger value="all">
            All ({prioritizedActivities.length})
          </TabsTrigger>
          <TabsTrigger value={PriorityLevel.HIGH}>
            <span className="flex items-center">
              High ({countByPriority[PriorityLevel.HIGH]})
            </span>
          </TabsTrigger>
          <TabsTrigger value={PriorityLevel.MEDIUM}>
            <span className="flex items-center">
              Medium ({countByPriority[PriorityLevel.MEDIUM]})
            </span>
          </TabsTrigger>
          <TabsTrigger value={PriorityLevel.LOW}>
            <span className="flex items-center">
              Low ({countByPriority[PriorityLevel.LOW]})
            </span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4 mt-0">
          {processingActivities ? (
            // Show processing skeletons
            [1, 2, 3].map((i) => (
              <div key={i} className="flex flex-col space-y-3">
                <Skeleton className="h-[125px] w-full rounded-lg" />
              </div>
            ))
          ) : (
            filteredActivities.map((activity) => (
              <ActivityCard
                key={activity.id}
                activity={activity}
                onView={onViewActivity}
                onComplete={onCompleteActivity}
              />
            ))
          )}
        </TabsContent>

        <TabsContent value={PriorityLevel.HIGH} className="space-y-4 mt-0">
          {processingActivities ? (
            [1, 2].map((i) => (
              <div key={i} className="flex flex-col space-y-3">
                <Skeleton className="h-[125px] w-full rounded-lg" />
              </div>
            ))
          ) : (
            filteredActivities.map((activity) => (
              <ActivityCard
                key={activity.id}
                activity={activity}
                onView={onViewActivity}
                onComplete={onCompleteActivity}
              />
            ))
          )}
        </TabsContent>

        <TabsContent value={PriorityLevel.MEDIUM} className="space-y-4 mt-0">
          {processingActivities ? (
            [1, 2].map((i) => (
              <div key={i} className="flex flex-col space-y-3">
                <Skeleton className="h-[125px] w-full rounded-lg" />
              </div>
            ))
          ) : (
            filteredActivities.map((activity) => (
              <ActivityCard
                key={activity.id}
                activity={activity}
                onView={onViewActivity}
                onComplete={onCompleteActivity}
              />
            ))
          )}
        </TabsContent>

        <TabsContent value={PriorityLevel.LOW} className="space-y-4 mt-0">
          {processingActivities ? (
            [1].map((i) => (
              <div key={i} className="flex flex-col space-y-3">
                <Skeleton className="h-[125px] w-full rounded-lg" />
              </div>
            ))
          ) : (
            filteredActivities.map((activity) => (
              <ActivityCard
                key={activity.id}
                activity={activity}
                onView={onViewActivity}
                onComplete={onCompleteActivity}
              />
            ))
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
