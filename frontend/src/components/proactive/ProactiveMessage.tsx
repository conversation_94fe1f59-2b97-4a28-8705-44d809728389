import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import type { Activity, Insight } from '@/lib/types';
import { InsightFeedback } from './InsightFeedback';
import { AlertCircle, Sparkles } from 'lucide-react';
import { useMattersApi } from '@/hooks/useMattersApi';
import { WorkType } from '@/types/domain/tenants/Matter';

interface ProactiveMessageProps {
  userId: string;
  userName?: string;
  activities: Activity[];
  insights?: Insight[];
  isLoading: boolean;
  onFeedbackSubmit?: (feedbackData: {
    feedbackId: string;
    insightId: string;
    action: string;
    rating?: number;
    comment?: string;
  }) => void;
}

/**
 * ProactiveMessage component
 *
 * Displays proactive insights and suggestions based on recent user activities.
 */
/**
 * Hook to determine the appropriate navigation label for matters/cases
 */
function useNavigationLabel() {
  const { getAllMatters } = useMattersApi()
  const [navigationLabel, setNavigationLabel] = useState('Cases') // Default to Cases for new users
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const determineLabel = async () => {
      try {
        const matters = await getAllMatters({ limit: 50 }) // Get a reasonable sample

        if (matters.length === 0) {
          setNavigationLabel('Cases') // Default for new users
          return
        }

        // Check if all matters are litigation
        const allLitigation = matters.every(matter => matter.workType === WorkType.LITIGATION)
        setNavigationLabel(allLitigation ? 'Cases' : 'Matters')
      } catch (error) {
        console.error('Error determining navigation label:', error)
        setNavigationLabel('Cases') // Fallback to Cases on error
      } finally {
        setIsLoading(false)
      }
    }

    determineLabel()
  }, [getAllMatters])

  return { navigationLabel, isLoading }
}

export default function ProactiveMessage({
  userId,
  userName,
  activities,
  insights = [],
  isLoading,
  onFeedbackSubmit
}: ProactiveMessageProps) {
  const [dismissed, setDismissed] = useState(false);
  const { toast } = useToast();
  const router = useRouter();
  const [activeInsight, setActiveInsight] = useState<Insight | null>(null);
  const { navigationLabel } = useNavigationLabel();

  useEffect(() => {
    // Reset dismissed state if the activities prop changes (e.g., new fetch)
    const lastMessageDate = localStorage.getItem('ailexLastProactiveMessageDate');
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

    if (lastMessageDate === today) {
      setDismissed(true);
    } else {
      setDismissed(false); // Show if it's a new day
    }

    // Set the active insight when insights change
    if (insights && insights.length > 0) {
      setActiveInsight(insights[0]); // Use the highest priority insight
    } else {
      setActiveInsight(null);
    }
  }, [activities, insights]); // Re-evaluate when activities or insights change

  const handleSuggestionClick = (suggestion: string): void => {
    // Handle different suggestion types with dynamic terminology
    const lowerSuggestion = suggestion.toLowerCase();

    if (lowerSuggestion.includes('case') || lowerSuggestion.includes('matter')) {
      router.push('/matters');
    } else if (lowerSuggestion.includes('intake')) {
      router.push('/intake');
    } else if (lowerSuggestion.includes('deadline')) {
      router.push('/deadlines');
    } else if (lowerSuggestion.includes('document')) {
      router.push('/documents');
    } else {
      // Generic navigation based on suggestion text
      const path = `/${suggestion.toLowerCase().replace(/\s+/g, '-')}`;
      router.push(path);
    }

    setDismissed(true);
    toast({
      title: "Navigating",
      description: `Taking you to ${suggestion}`,
    });
  };

  const handleDismiss = (): void => {
    setDismissed(true);
    // Mark dismissed for today
    const today = new Date().toISOString().split('T')[0];
    localStorage.setItem('ailexLastProactiveMessageDate', today);

    // If we have an active insight with a feedback ID, log the dismissal
    if (activeInsight?.feedbackId && onFeedbackSubmit) {
      onFeedbackSubmit({
        feedbackId: activeInsight.feedbackId,
        insightId: activeInsight.id,
        action: 'dismissed'
      });
    }
  };

  const handleFeedbackSubmit = (feedbackData: {
    feedbackId: string;
    insightId: string;
    action: string;
    rating?: number;
    comment?: string;
  }): void => {
    if (onFeedbackSubmit) {
      onFeedbackSubmit(feedbackData);
    }
  };

  // --- Generate Message & Suggestions ---
  let displayMessage = 'Welcome back!';
  let suggestions: string[] = [];

  if (isLoading) {
    displayMessage = 'Loading insights...'; // Show loading message inside the card
    suggestions = [];
  }
  // If we have pre-processed insights from the API, use those
  else if (insights && insights.length > 0) {
    // Use the highest priority insight as the main message
    const topInsight = insights[0]; // Already sorted by priority in the API
    displayMessage = topInsight.message;
    suggestions = [...topInsight.suggestions]; // Clone to avoid modifying the original

    // Add additional insight messages as suggestions if available
    if (insights.length > 1) {
      insights.slice(1, 3).forEach(insight => {
        // Only add if it's not already in the suggestions
        if (!suggestions.includes(insight.message)) {
          suggestions.push(insight.message);
        }
      });
    }
  }
  // Fall back to generating insights from raw activities
  else if (activities.length > 0) {
    const highImportanceCount = activities.filter(a => a.importance === 'high').length;
    const recentActivity = activities[0]; // Most recent

    if (highImportanceCount > 0) {
      displayMessage = `You have ${highImportanceCount} high importance item${highImportanceCount > 1 ? 's' : ''} needing attention.`;
      suggestions = ['View High Priority Tasks', `Review Recent ${navigationLabel}`];
    } else {
      displayMessage = `Your latest activity was: ${recentActivity.summary}`;
      suggestions = ['View Recent Activity', 'Check Deadlines'];
    }
  } else {
    // Handle case with no recent activities or insights fetched
    displayMessage = `Welcome ${userName || 'back'}! No recent activity highlights found.`;
    suggestions = [
      navigationLabel === 'Cases' ? 'Create New Case' : 'Create New Matter',
      'View Documents'
    ];
  }
  // --- End Generation Logic ---

  // Don't render if dismissed or no activities to process (optional, could show generic welcome)
  if (dismissed) {
    return null;
  }

  return (
    <Card className="w-full max-w-3xl mx-auto mb-6 shadow-lg border-primary/20">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {activeInsight?.aiGenerated ? (
            <Sparkles className="h-5 w-5 text-primary" />
          ) : (
            <AlertCircle className="h-5 w-5 text-primary" />
          )}
          AiLex Assistant
          {activeInsight?.aiGenerated && (
            <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full ml-2">
              AI-Generated
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-lg">{displayMessage}</p>

        {/* Show related entity if available */}
        {activeInsight?.relatedEntity && (
          <div className="mt-2 text-sm text-muted-foreground">
            Related to: <span className="font-medium">{activeInsight.relatedEntity.name}</span>
            <span className="ml-2 text-xs bg-secondary px-2 py-0.5 rounded-full">
              {activeInsight.relatedEntity.type}
            </span>
          </div>
        )}

        {/* Show feedback component if we have an active insight with a feedback ID */}
        {activeInsight?.feedbackId && onFeedbackSubmit && (
          <InsightFeedback
            insight={activeInsight}
            onFeedbackSubmit={handleFeedbackSubmit}
            onDismiss={handleDismiss}
          />
        )}
      </CardContent>
      {suggestions.length > 0 && (
        <CardFooter className="flex flex-wrap gap-2">
          {suggestions.map((suggestion, index) => (
            <Button
              key={index}
              variant="outline"
              onClick={() => handleSuggestionClick(suggestion)}
            >
              {suggestion}
            </Button>
          ))}
          {!activeInsight?.feedbackId && (
            <Button variant="ghost" onClick={handleDismiss}>Dismiss</Button>
          )}
        </CardFooter>
      )}
    </Card>
  );
}
