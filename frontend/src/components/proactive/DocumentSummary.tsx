import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { DocumentSummaryFeedback } from './DocumentSummaryFeedback';
import { SummaryEditorModal } from './SummaryEditorModal';
import { useSupabase } from '@/lib/supabase/provider';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

export interface DocumentSummaryProps {
  documentId: string;
  documentType: 'authored' | 'case' | 'client';
  summaryText: string;
  summarizedBy?: string;
  summarizedAt?: string;
  canEditSummary?: boolean; // Optionally allow parent to override
  onSummaryUpdated?: () => void; // Callback for parent to reload summary
}

// Define expected API response structure for saving summary
interface SaveSummaryApiResponse {
  success?: boolean;
  data?: unknown; // Could return updated summary or just success
  error?: string;
}

const ALLOWED_ROLES = ['partner', 'attorney', 'paralegal', 'staff'];

export function DocumentSummary({
  documentId,
  documentType,
  summaryText,
  summarizedBy,
  summarizedAt,
  canEditSummary,
  onSummaryUpdated,
}: DocumentSummaryProps): React.ReactElement {
  const { user } = useSupabase();
  const [modalOpen, setModalOpen] = useState(false);
  const [saving, setSaving] = useState(false);
  const { authedFetch } = useAuthenticatedFetch();

  // Determine if user can edit
  const isAllowed = canEditSummary !== undefined
    ? canEditSummary
    : user?.role && ALLOWED_ROLES.includes(user.role);

  const handleSaveSummary = async (newSummary: string) => {
    setSaving(true);
    try {
      const method = summaryText ? 'PATCH' : 'POST';
      // Use the defined interface as a generic for authedFetch
      const result = await authedFetch<SaveSummaryApiResponse>('/api/documents/summary', {
        method,
        body: JSON.stringify({
          document_id: documentId,
          document_type: documentType,
          summary_text: newSummary,
        }),
      });

      // Check for API error in the structured result
      if (result.error) {
        throw new Error(result.error || 'Failed to save summary');
      }

      if (onSummaryUpdated) onSummaryUpdated();
      setModalOpen(false); // Close modal on success
    } catch (error: unknown) { // Catch errors from authedFetch or thrown above
      console.error('Error saving summary:', error);
      // Consider adding a toast notification here for the user
    } finally {
      setSaving(false);
    }
  };

  return (
    <Card className="my-6">
      <CardHeader className="flex flex-row items-center justify-between gap-2">
        <div>
          <CardTitle>Summary</CardTitle>
          {summarizedBy || summarizedAt ? (
            <CardDescription>
              {summarizedBy && <span>By {summarizedBy}</span>}
              {summarizedBy && summarizedAt && <span> &middot; </span>}
              {summarizedAt && <span>{new Date(summarizedAt).toLocaleString()}</span>}
            </CardDescription>
          ) : null}
        </div>
        {isAllowed && (
          <Button
            size="sm"
            variant="outline"
            onClick={() => setModalOpen(true)}
            disabled={saving}
          >
            {summaryText ? 'Edit Summary' : 'Add Summary'}
          </Button>
        )}
      </CardHeader>
      <CardContent>
        <div className="prose max-w-none whitespace-pre-wrap text-base">
          {summaryText}
        </div>
      </CardContent>
      <CardFooter>
        <DocumentSummaryFeedback documentId={documentId} documentType={documentType} />
      </CardFooter>
      <SummaryEditorModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        initialValue={summaryText}
        onSave={handleSaveSummary}
        isEditing={!!summaryText}
      />
    </Card>
  );
}
