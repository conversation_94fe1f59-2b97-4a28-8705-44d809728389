import React, { useEffect, useRef, useState, useImperativeHandle, forwardRef } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { useSupabase } from '@/lib/supabase/provider';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

// --- API Response Interfaces ---
interface HighlightsApiResponse {
  highlights: Highlight[];
}

interface CreateHighlightApiResponse {
  highlight: Highlight;
}

interface UpdateHighlightApiResponse {
  highlight: Highlight;
}

interface DeleteHighlightApiResponse {
  success: boolean;
}
// --- End API Response Interfaces ---

export interface Highlight {
  id: string;
  start_offset: number;
  end_offset: number;
  highlight_color?: string;
  comment?: string;
  created_by: string;
  created_at: string;
}

export interface DocumentViewerWithHighlightsHandle {
  scrollToHighlight: (id: string) => void;
}

interface DocumentViewerWithHighlightsProps {
  documentId: string;
  documentType: 'authored' | 'case' | 'client';
  text: string;
  canEdit?: boolean;
  highlightId?: string | null;
  onHighlightsLoaded?: (highlights: Highlight[]) => void;
}

export const DocumentViewerWithHighlights = forwardRef<DocumentViewerWithHighlightsHandle, DocumentViewerWithHighlightsProps>(
  function DocumentViewerWithHighlights({ documentId, documentType, text, canEdit, highlightId, onHighlightsLoaded }, ref) {
    const { user } = useSupabase();
    const [highlights, setHighlights] = useState<Highlight[]>([]);
    const [selection, setSelection] = useState<{ start: number; end: number } | null>(null);
    const [showToolbar, setShowToolbar] = useState(false);
    const [toolbarPos, setToolbarPos] = useState<{ x: number; y: number } | null>(null);
    const [newComment, setNewComment] = useState('');
    const [creating, setCreating] = useState(false);
    const [color, setColor] = useState('#ffe066');
    const [editingId, setEditingId] = useState<string | null>(null);
    const [editComment, setEditComment] = useState('');
    const [editColor, setEditColor] = useState('#ffe066');
    const [editLoading, setEditLoading] = useState(false);
    const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);
    const highlightRefs = useRef<Record<string, HTMLSpanElement | null>>({});
    const { authedFetch } = useAuthenticatedFetch();

    // Fetch highlights
    async function fetchHighlights() {
      try {
        const data = await authedFetch<HighlightsApiResponse>(`/api/documents/highlights?document_id=${documentId}&document_type=${documentType}`);
        setHighlights(data.highlights || []); // Use fetched data or empty array
        if (onHighlightsLoaded) onHighlightsLoaded(data.highlights || []);
      } catch (error) {
        console.error('Failed to fetch highlights:', error);
        // Handle error appropriately, maybe set an error state
      }
    }

    useEffect(() => {
      fetchHighlights();
      // eslint-disable-next-line
    }, [documentId, documentType]);

    // Expose scrollToHighlight
    useImperativeHandle(ref, () => ({
      scrollToHighlight: (id: string) => {
        const el = highlightRefs.current[id];
        if (el) {
          el.scrollIntoView({ behavior: 'smooth', block: 'center' });
          el.classList.add('ring-2', 'ring-primary');
          setTimeout(() => {
            el.classList.remove('ring-2', 'ring-primary');
          }, 1200);
        }
      },
    }), []);

    // Handle text selection
    const handleMouseUp = (): void => {
      const sel = window.getSelection();
      if (!sel || sel.isCollapsed) {
        setShowToolbar(false);
        setSelection(null);
        return;
      }
      const anchor = sel.anchorOffset;
      const focus = sel.focusOffset;
      const start = Math.min(anchor, focus);
      const end = Math.max(anchor, focus);
      if (end > start) {
        setSelection({ start, end });
        const range = sel.getRangeAt(0).getBoundingClientRect();
        setToolbarPos({ x: range.left + range.width / 2, y: range.top - 40 });
        setShowToolbar(true);
      }
    };

    // Create highlight
    const handleCreateHighlight = async () => {
      if (!selection) return;
      setCreating(true);
      setShowToolbar(false);
      try {
        const newHighlight = await authedFetch<CreateHighlightApiResponse>('/api/documents/highlights', {
          method: 'POST',
          body: JSON.stringify({
            document_id: documentId,
            document_type: documentType,
            start_offset: selection.start,
            end_offset: selection.end,
            highlight_color: color,
            comment: newComment.trim() || undefined,
          }),
        });

        // Add the new highlight to the state
        if (newHighlight.highlight) {
             setHighlights(prev => [...prev, newHighlight.highlight].sort((a, b) => a.start_offset - b.start_offset));
        }
        setSelection(null);
        setNewComment('');

      } catch (error) {
        console.error('Failed to create highlight:', error);
        // Handle error (e.g., show toast notification)
      } finally {
        setCreating(false);
      }
    };
    // Edit highlight
    const handleEditHighlight = (hl: Highlight): void => {
      setEditingId(hl.id);
      setEditComment(hl.comment || '');
      setEditColor(hl.highlight_color || '#ffe066');
    };

    const handleSaveEdit = async (hl: Highlight) => {
      setEditLoading(true);
      try {
        const updatedHighlight = await authedFetch<UpdateHighlightApiResponse>(`/api/documents/highlights/${hl.id}`, {
          method: 'PUT',
          body: JSON.stringify({
            comment: editComment.trim() || undefined,
            highlight_color: editColor,
          }),
        });

        // Update the highlight in the state
        if (updatedHighlight.highlight) {
            setHighlights(prev => prev.map(h => h.id === hl.id ? updatedHighlight.highlight : h));
        }
        setEditingId(null);

      } catch (error) {
         console.error('Failed to save highlight edit:', error);
         // Handle error
      } finally {
        setEditLoading(false);
      }
    };

    const handleDeleteHighlight = async (hl: Highlight) => {
      setEditLoading(true); // Use editLoading to disable buttons during delete
      try {
        const response = await authedFetch<DeleteHighlightApiResponse>(`/api/documents/highlights/${hl.id}`, {
          method: 'DELETE',
        });

        // Remove the highlight from the state if deletion was successful
        if (response.success) {
             setHighlights(prev => prev.filter(h => h.id !== hl.id));
        }
        setDeleteConfirmId(null);

      } catch (error) {
        console.error('Failed to delete highlight:', error);
        // Handle error
      } finally {
         setEditLoading(false);
      }
    };

    // Render text with highlights
    function renderTextWithHighlights() {
      if (!highlights.length) return <span>{text}</span>;
      // Sort highlights by start_offset
      const sorted = [...highlights].sort((a, b) => a.start_offset - b.start_offset);
      let last = 0;
      const nodes = [];
      sorted.forEach((hl, i) => {
        if (hl.start_offset > last) {
          nodes.push(<span key={`plain-${i}`}>{text.slice(last, hl.start_offset)}</span>);
        }
        const isCreator = user && user.id === hl.created_by;
        nodes.push(
          <Popover key={hl.id}>
            <PopoverTrigger asChild>
              <span
                ref={el => { highlightRefs.current[hl.id] = el; }}
                id={`highlight-${hl.id}`}
                style={{
                  background: hl.highlight_color || '#ffe066',
                  borderRadius: 2,
                  cursor: 'pointer',
                  outline: highlightId === hl.id ? '2px solid #6366f1' : undefined,
                  transition: 'outline 0.2s',
                }}
                className={`px-0.5 py-0.5 mx-0.5 ${highlightId === hl.id ? 'ring-2 ring-primary' : ''}`}
              >
                {text.slice(hl.start_offset, hl.end_offset)}
              </span>
            </PopoverTrigger>
            <PopoverContent className="max-w-xs">
              {editingId === hl.id ? (
                <div className="flex flex-col gap-2">
                  <textarea
                    className="border rounded px-1 py-0.5 text-sm"
                    rows={2}
                    value={editComment}
                    onChange={e => setEditComment(e.target.value)}
                  />
                  <div className="flex items-center gap-2">
                    <input
                      type="color"
                      value={editColor}
                      onChange={e => setEditColor(e.target.value)}
                      className="w-6 h-6 border-none bg-transparent"
                      title="Highlight color"
                    />
                    <Button size="sm" variant="default" onClick={() => handleSaveEdit(hl)} disabled={editLoading}>
                      {editLoading ? 'Saving...' : 'Save'}
                    </Button>
                    <Button size="sm" variant="ghost" onClick={() => setEditingId(null)} disabled={editLoading}>
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <>
                  <div className="text-sm font-medium mb-1">Comment:</div>
                  <div className="mb-2 text-sm text-muted-foreground whitespace-pre-wrap">{hl.comment || <em>No comment</em>}</div>
                  <div className="text-xs text-muted-foreground">By: {hl.created_by}</div>
                  <div className="text-xs text-muted-foreground">{new Date(hl.created_at).toLocaleString()}</div>
                  {isCreator && (
                    <div className="flex gap-2 mt-2">
                      <Button size="sm" variant="outline" onClick={() => handleEditHighlight(hl)}>
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => setDeleteConfirmId(hl.id)}
                      >
                        Delete
                      </Button>
                    </div>
                  )}
                  {deleteConfirmId === hl.id && (
                    <div className="mt-2 flex flex-col gap-2">
                      <div className="text-xs text-muted-foreground">Delete this highlight?</div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="destructive" onClick={() => handleDeleteHighlight(hl)} disabled={editLoading}>
                          Confirm
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => setDeleteConfirmId(null)} disabled={editLoading}>
                          Cancel
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </PopoverContent>
          </Popover>
        );
        last = hl.end_offset;
      });
      if (last < text.length) nodes.push(<span key="plain-last">{text.slice(last)}</span>);
      return nodes;
    }

    return (
      <div className="relative" onMouseUp={canEdit ? handleMouseUp : undefined}>
        <div className="prose max-w-none leading-relaxed text-base select-text">
          {renderTextWithHighlights()}
        </div>
        {showToolbar && selection && (
          <div
            className="absolute z-50 bg-white border rounded shadow-lg flex flex-col items-center p-2"
            style={{ left: toolbarPos?.x, top: toolbarPos?.y }}
          >
            <input
              type="color"
              value={color}
              onChange={e => setColor(e.target.value)}
              className="w-6 h-6 mb-1 border-none bg-transparent"
              title="Highlight color"
            />
            <textarea
              className="border rounded px-1 py-0.5 text-sm mb-1"
              rows={2}
              placeholder="Add a comment..."
              value={newComment}
              onChange={e => setNewComment(e.target.value)}
              style={{ minWidth: 120 }}
            />
            <Button size="sm" variant="default" onClick={handleCreateHighlight} disabled={creating}>
              {creating ? 'Saving...' : 'Highlight'}
            </Button>
          </div>
        )}
      </div>
    );
  }
);
