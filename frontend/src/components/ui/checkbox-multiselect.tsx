"use client"

import * as React from "react"
import { ChevronsUpDown } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { cn } from "@/lib/utils"

export interface MultiSelectOption {
  label: string
  value: string
}

interface CheckboxMultiSelectProps {
  options: MultiSelectOption[]
  selected: string[]
  onChange: (selected: string[]) => void
  placeholder?: string
  error?: boolean
}

export function CheckboxMultiSelect({
  options,
  selected,
  onChange,
  placeholder = "Select options...",
  error = false,
}: CheckboxMultiSelectProps): React.ReactElement {
  const [open, setOpen] = React.useState(false)
  const [search, setSearch] = React.useState("")

  const filteredOptions = React.useMemo(
    () =>
      options.filter((o) =>
        o.label.toLowerCase().includes(search.toLowerCase())
      ),
    [options, search]
  )

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between text-left font-normal",
            error && "border-red-500 focus:ring-red-500"
          )}
        >
          {selected.length === 0
            ? <span className="text-muted-foreground">{placeholder}</span>
            : options
                .filter((o) => selected.includes(o.value))
                .map((o) => o.label)
                .join(", ")}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-2">
        <Input
          placeholder="Search..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="mb-2"
        />
        <div className="max-h-60 overflow-y-auto space-y-1">
          {filteredOptions.map((option) => {
            const checked = selected.includes(option.value)
            return (
              <label
                key={option.value}
                className="flex items-center space-x-2 cursor-pointer"
              >
                <Checkbox
                  checked={checked}
                  onCheckedChange={(val) => {
                    if (val) {
                      onChange([...selected, option.value])
                    } else {
                      onChange(selected.filter((v) => v !== option.value))
                    }
                  }}
                />
                <span className={checked ? "font-medium" : "font-normal"}>
                  {option.label}
                </span>
              </label>
            )
          })}
        </div>
      </PopoverContent>
    </Popover>
  )
}
