"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Command, CommandGroup, CommandItem, CommandInput, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"

export interface MultiSelectOption {
  label: string
  value: string
}

interface ShadcnMultiSelectProps {
  options: MultiSelectOption[]
  selected: string[]
  onChange: (selected: string[]) => void
  placeholder?: string
  error?: boolean
}

export function ShadcnMultiSelect({
  options,
  selected,
  onChange,
  placeholder = "Select options...",
  error = false,
}: ShadcnMultiSelectProps): React.ReactElement {
  const [open, setOpen] = React.useState(false)

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between text-left font-normal",
            error && "border-red-500 focus:ring-red-500"
          )}
        >
          {selected.length === 0
            ? <span className="text-muted-foreground">{placeholder}</span>
            : options
                .filter((o) => selected.includes(o.value))
                .map((o) => o.label)
                .join(", ")}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0">
        <Command>
          <CommandInput placeholder="Search..." />
          <CommandList>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  onSelect={() => {
                    if (selected.includes(option.value)) {
                      onChange(selected.filter((v) => v !== option.value))
                    } else {
                      onChange([...selected, option.value])
                    }
                  }}
                  onMouseDown={e => {
                    e.preventDefault();
                    if (selected.includes(option.value)) {
                      onChange(selected.filter((v) => v !== option.value));
                    } else {
                      onChange([...selected, option.value]);
                    }
                  }}
                  className="cursor-pointer"
                  aria-selected={selected.includes(option.value)}
                >
                  <span
                    className={
                      selected.includes(option.value)
                        ? "font-medium"
                        : "font-normal"
                    }
                  >
                    {option.label}
                  </span>
                  {selected.includes(option.value) && (
                    <Check className="ml-auto h-4 w-4 text-primary" />
                  )}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
