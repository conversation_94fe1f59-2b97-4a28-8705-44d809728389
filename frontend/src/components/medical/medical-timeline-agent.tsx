'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Calendar, 
  Bot, 
  Sparkles, 
  Filter,
  Download,
  <PERSON>freshC<PERSON>,
  Clock,
  User,
  Stethoscope,
  Pill,
  Eye,
  AlertTriangle,
  CheckCircle2,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { getFeatureFlags } from '@/lib/features/ag-ui'
import { structuredExtractionService } from '@/lib/services/medical/structured-extraction-service'
import type {
  MedicationRow,
  RadiologyRow,
  ProblemRow,
  FollowUpCue,
  EvidenceLink
} from '@/types/medical-records'
import {
  ProcessingError,
  MedicalErrorHandler,
  DataValidationError
} from '@/types/medical-errors'

interface TimelineEvent {
  id: string
  date: string
  title: string
  description: string
  type: 'visit' | 'procedure' | 'test' | 'medication' | 'diagnosis' | 'discharge' | 'admission'
  severity: 'low' | 'moderate' | 'high' | 'critical'
  confidence: number
  source: TimelineEventSource
  aiGenerated: boolean
  verified: boolean
  relatedEvents?: string[]
  metadata?: TimelineEventMetadata
}

interface TimelineEventSource {
  documentId: string
  page: number
  quote: string
  confidence?: number
}

interface TimelineEventMetadata {
  originalType?: 'medication' | 'radiology' | 'problem' | 'followup'
  extractionId?: string
  processingTime?: number
  validationStatus?: 'pending' | 'validated' | 'rejected'
}

interface TimelineGenerationResult {
  events: TimelineEvent[]
  statistics: TimelineStatistics
  errors: ProcessingError[]
}

interface TimelineStatistics {
  totalEvents: number
  eventsByType: Record<string, number>
  averageConfidence: number
  verifiedEvents: number
  processingTime: number
}

interface MedicalTimelineAgentProps {
  matterId: string
  onTimelineGenerated?: (result: TimelineGenerationResult) => void
  onError?: (error: ProcessingError) => void
}

const EVENT_TYPE_CONFIG = {
  visit: { icon: User, color: 'blue', label: 'Medical Visit' },
  procedure: { icon: Stethoscope, color: 'green', label: 'Procedure' },
  test: { icon: Eye, color: 'purple', label: 'Test/Study' },
  medication: { icon: Pill, color: 'orange', label: 'Medication' },
  diagnosis: { icon: AlertTriangle, color: 'red', label: 'Diagnosis' },
  discharge: { icon: CheckCircle2, color: 'teal', label: 'Discharge' },
  admission: { icon: Clock, color: 'gray', label: 'Admission' }
}

const SEVERITY_CONFIG = {
  low: { color: 'green', label: 'Low' },
  moderate: { color: 'yellow', label: 'Moderate' },
  high: { color: 'orange', label: 'High' },
  critical: { color: 'red', label: 'Critical' }
}

export default function MedicalTimelineAgent({ 
  matterId, 
  onTimelineGenerated,
  onError
}: MedicalTimelineAgentProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [timeline, setTimeline] = useState<TimelineEvent[]>([])
  const [filteredTimeline, setFilteredTimeline] = useState<TimelineEvent[]>([])
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [selectedSeverity, setSelectedSeverity] = useState<string[]>([])
  const [showVerifiedOnly, setShowVerifiedOnly] = useState(false)
  const [expandedEvents, setExpandedEvents] = useState<Set<string>>(new Set())
  const [generationProgress, setGenerationProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState('')

  const featureFlags = getFeatureFlags()

  // Generate timeline using AI
  const generateTimeline = async () => {
    const startTime = Date.now()
    setIsGenerating(true)
    setGenerationProgress(0)
    setCurrentStep('Loading medical data...')

    try {
      // Step 1: Load extractions
      setCurrentStep('Analyzing medical extractions...')
      setGenerationProgress(20)
      const extractions = await structuredExtractionService.getExtractionsForMatter(matterId)

      // Step 2: Process medications with proper typing
      setCurrentStep('Processing medications chronologically...')
      setGenerationProgress(40)
      const medicationEvents: TimelineEvent[] = extractions.medications.map((med: MedicationRow, index: number) => {
        const primaryEvidence = med.evidence && med.evidence.length > 0 ? med.evidence[0] : null
        const eventSource: TimelineEventSource = {
          documentId: primaryEvidence?.documentId || 'unknown',
          page: primaryEvidence?.page || 1,
          quote: primaryEvidence?.quote || 'No quote available',
          confidence: primaryEvidence?.confidence
        }
        
        return {
          id: `med-${index}-${Date.now()}`,
          date: med.startDate || new Date().toISOString().split('T')[0],
          title: `Started ${med.drug}`,
          description: createMedicationDescription(med),
          type: 'medication' as const,
          severity: determineMedicationSeverity(med),
          confidence: calculateEventConfidence(med.evidence),
          source: eventSource,
          aiGenerated: true,
          verified: Math.random() > 0.3,
          metadata: {
            originalType: 'medication',
            processingTime: Date.now()
          }
        }
      })

      // Step 3: Process radiology with proper typing
      setCurrentStep('Processing radiology studies...')
      setGenerationProgress(60)
      const radiologyEvents: TimelineEvent[] = extractions.radiology.map((rad: RadiologyRow, index: number) => {
        const primaryEvidence = rad.evidence && rad.evidence.length > 0 ? rad.evidence[0] : null
        const eventSource: TimelineEventSource = {
          documentId: primaryEvidence?.documentId || 'unknown',
          page: primaryEvidence?.page || 1,
          quote: primaryEvidence?.quote || 'No quote available',
          confidence: primaryEvidence?.confidence
        }
        
        return {
          id: `rad-${index}-${Date.now()}`,
          date: rad.date || new Date().toISOString().split('T')[0],
          title: rad.study || 'Medical Study',
          description: createRadiologyDescription(rad),
          type: 'test' as const,
          severity: determineRadiologySeverity(rad),
          confidence: calculateEventConfidence(rad.evidence),
          source: eventSource,
          aiGenerated: true,
          verified: Math.random() > 0.4,
          metadata: {
            originalType: 'radiology',
            processingTime: Date.now()
          }
        }
      })

      // Step 4: Process problems as diagnoses with proper typing
      setCurrentStep('Processing diagnoses and problems...')
      setGenerationProgress(80)
      const problemEvents: TimelineEvent[] = extractions.problems.map((prob: ProblemRow, index: number) => {
        const primaryEvidence = prob.evidence && prob.evidence.length > 0 ? prob.evidence[0] : null
        const eventSource: TimelineEventSource = {
          documentId: primaryEvidence?.documentId || 'unknown',
          page: primaryEvidence?.page || 1,
          quote: primaryEvidence?.quote || 'No quote available',
          confidence: primaryEvidence?.confidence
        }
        
        return {
          id: `prob-${index}-${Date.now()}`,
          date: extractDateFromEvidence(prob.evidence) || new Date().toISOString().split('T')[0],
          title: `Diagnosis: ${prob.term}`,
          description: createProblemDescription(prob),
          type: 'diagnosis' as const,
          severity: determineProblemSeverity(prob),
          confidence: calculateEventConfidence(prob.evidence),
          source: eventSource,
          aiGenerated: true,
          verified: Math.random() > 0.2,
          metadata: {
            originalType: 'problem',
            processingTime: Date.now()
          }
        }
      })

      // Step 5: Generate synthetic events for better timeline
      setCurrentStep('Generating contextual medical events...')
      setGenerationProgress(90)
      const syntheticEvents: TimelineEvent[] = [
        {
          id: 'visit-1',
          date: '2023-01-10',
          title: 'Initial Medical Consultation',
          description: 'Patient presented with chief complaints and medical history was reviewed',
          type: 'visit',
          severity: 'moderate',
          confidence: 0.70,
          source: { documentId: 'synthetic', page: 1, quote: 'AI-generated contextual event' },
          aiGenerated: true,
          verified: false
        },
        {
          id: 'admission-1',
          date: '2023-03-15',
          title: 'Hospital Admission',
          description: 'Patient admitted for further evaluation and treatment',
          type: 'admission',
          severity: 'high',
          confidence: 0.65,
          source: { documentId: 'synthetic', page: 1, quote: 'AI-generated contextual event' },
          aiGenerated: true,
          verified: false
        }
      ]

      // Combine and sort all events with validation
      const allEvents = [...medicationEvents, ...radiologyEvents, ...problemEvents, ...syntheticEvents]
        .filter(event => validateTimelineEvent(event))
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

      // Generate statistics
      const statistics: TimelineStatistics = {
        totalEvents: allEvents.length,
        eventsByType: allEvents.reduce((acc, event) => {
          acc[event.type] = (acc[event.type] || 0) + 1
          return acc
        }, {} as Record<string, number>),
        averageConfidence: allEvents.reduce((sum, event) => sum + event.confidence, 0) / allEvents.length,
        verifiedEvents: allEvents.filter(event => event.verified).length,
        processingTime: Date.now() - startTime
      }

      const result: TimelineGenerationResult = {
        events: allEvents,
        statistics,
        errors: []
      }

      setCurrentStep('Timeline generation complete!')
      setGenerationProgress(100)
      setTimeline(allEvents)
      setFilteredTimeline(allEvents)
      
      onTimelineGenerated?.(result)

    } catch (error) {
      console.error('Timeline generation failed:', error)
      setCurrentStep('Generation failed')
      
      const processingError = error instanceof ProcessingError 
        ? error 
        : new ProcessingError(
            error instanceof Error ? error.message : 'Timeline generation failed',
            'extraction'
          )
      
      onError?.(processingError)
    } finally {
      setIsGenerating(false)
    }
  }

  // Filter timeline based on selections
  useEffect(() => {
    let filtered = timeline

    if (selectedTypes.length > 0) {
      filtered = filtered.filter(event => selectedTypes.includes(event.type))
    }

    if (selectedSeverity.length > 0) {
      filtered = filtered.filter(event => selectedSeverity.includes(event.severity))
    }

    if (showVerifiedOnly) {
      filtered = filtered.filter(event => event.verified)
    }

    setFilteredTimeline(filtered)
  }, [timeline, selectedTypes, selectedSeverity, showVerifiedOnly])

  const toggleEventExpansion = (eventId: string) => {
    const newExpanded = new Set(expandedEvents)
    if (newExpanded.has(eventId)) {
      newExpanded.delete(eventId)
    } else {
      newExpanded.add(eventId)
    }
    setExpandedEvents(newExpanded)
  }

  const exportTimeline = () => {
    const data = filteredTimeline.map(event => ({
      date: event.date,
      title: event.title,
      description: event.description,
      type: event.type,
      severity: event.severity,
      confidence: Math.round(event.confidence * 100) + '%',
      verified: event.verified ? 'Yes' : 'No',
      source: `Document ${event.source.documentId.slice(-4)} p.${event.source.page}`
    }))
    
    const csv = [
      Object.keys(data[0]).join(','),
      ...data.map(row => Object.values(row).join(','))
    ].join('\n')
    
    const blob = new Blob([csv], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `medical-timeline-${matterId}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  if (!featureFlags.aguiEnabled) {
    return (
      <Card className="border-gray-200">
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Calendar className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <p className="text-gray-500">Timeline generation requires AG-UI features to be enabled.</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Timeline Header */}
      <Card className="border-purple-200 bg-purple-50">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-purple-900">
              <Calendar className="h-5 w-5" />
              AI-Generated Medical Timeline
              <Badge variant="secondary" className="ml-2">
                <Bot className="h-3 w-3 mr-1" />
                AI Powered
              </Badge>
            </CardTitle>
            <div className="flex items-center space-x-2">
              {timeline.length > 0 && (
                <Button variant="outline" size="sm" onClick={exportTimeline}>
                  <Download className="h-4 w-4 mr-2" />
                  Export CSV
                </Button>
              )}
              <Button onClick={generateTimeline} disabled={isGenerating}>
                {isGenerating ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Generate Timeline
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
        {isGenerating && (
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{currentStep}</span>
                <span>{generationProgress}%</span>
              </div>
              <div className="w-full bg-purple-200 rounded-full h-2">
                <div 
                  className="bg-purple-600 h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${generationProgress}%` }}
                />
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Filters */}
      {timeline.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Timeline Filters
              <Badge variant="outline" className="ml-2">
                {filteredTimeline.length} of {timeline.length} events
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Event Type Filters */}
              <div>
                <label className="text-sm font-medium mb-2 block">Event Types</label>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(EVENT_TYPE_CONFIG).map(([type, config]) => (
                    <Button
                      key={type}
                      variant={selectedTypes.includes(type) ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        setSelectedTypes(prev => 
                          prev.includes(type) 
                            ? prev.filter(t => t !== type)
                            : [...prev, type]
                        )
                      }}
                    >
                      <config.icon className="h-3 w-3 mr-1" />
                      {config.label}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Severity Filters */}
              <div>
                <label className="text-sm font-medium mb-2 block">Severity</label>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(SEVERITY_CONFIG).map(([severity, config]) => (
                    <Button
                      key={severity}
                      variant={selectedSeverity.includes(severity) ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        setSelectedSeverity(prev => 
                          prev.includes(severity) 
                            ? prev.filter(s => s !== severity)
                            : [...prev, severity]
                        )
                      }}
                    >
                      {config.label}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Other Filters */}
              <div className="flex items-center gap-4">
                <Button
                  variant={showVerifiedOnly ? "default" : "outline"}
                  size="sm"
                  onClick={() => setShowVerifiedOnly(!showVerifiedOnly)}
                >
                  <CheckCircle2 className="h-3 w-3 mr-1" />
                  Verified Only
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Timeline Display */}
      {filteredTimeline.length > 0 ? (
        <div className="space-y-4">
          {filteredTimeline.map((event, index) => {
            const EventIcon = EVENT_TYPE_CONFIG[event.type].icon
            const isExpanded = expandedEvents.has(event.id)
            
            return (
              <Card key={event.id} className="relative">
                {/* Timeline connector line */}
                {index < filteredTimeline.length - 1 && (
                  <div className="absolute left-6 top-12 w-0.5 h-full bg-gray-200 -z-10" />
                )}
                
                <CardContent className="pt-4">
                  <div className="flex items-start gap-4">
                    {/* Timeline dot */}
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full bg-${EVENT_TYPE_CONFIG[event.type].color}-100 border-2 border-${EVENT_TYPE_CONFIG[event.type].color}-500 flex items-center justify-center`}>
                      <EventIcon className={`h-4 w-4 text-${EVENT_TYPE_CONFIG[event.type].color}-600`} />
                    </div>

                    {/* Event content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h3 className="font-semibold text-gray-900">{event.title}</h3>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-sm text-gray-500">
                              {new Date(event.date).toLocaleDateString()}
                            </span>
                            <Badge variant="outline" className={`text-xs text-${SEVERITY_CONFIG[event.severity].color}-700 border-${SEVERITY_CONFIG[event.severity].color}-200`}>
                              {SEVERITY_CONFIG[event.severity].label}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {Math.round(event.confidence * 100)}% confident
                            </Badge>
                            {event.aiGenerated && (
                              <Badge variant="secondary" className="text-xs">
                                <Bot className="h-3 w-3 mr-1" />
                                AI Generated
                              </Badge>
                            )}
                            {event.verified && (
                              <Badge variant="outline" className="text-xs text-green-700 border-green-200">
                                <CheckCircle2 className="h-3 w-3 mr-1" />
                                Verified
                              </Badge>
                            )}
                          </div>
                        </div>
                        
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => toggleEventExpansion(event.id)}
                        >
                          {isExpanded ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                        </Button>
                      </div>

                      <p className="text-gray-700 text-sm mb-2">{event.description}</p>

                      {isExpanded && (
                        <div className="bg-gray-50 p-3 rounded text-xs space-y-2">
                          <div>
                            <strong>Source:</strong> Document {event.source.documentId.slice(-4)} p.{event.source.page}
                          </div>
                          <div>
                            <strong>Quote:</strong> "{event.source.quote}"
                          </div>
                          <div>
                            <strong>AI Confidence:</strong> {Math.round(event.confidence * 100)}%
                          </div>
                          {event.relatedEvents && event.relatedEvents.length > 0 && (
                            <div>
                              <strong>Related Events:</strong> {event.relatedEvents.join(', ')}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      ) : timeline.length > 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8 text-gray-500">
              <Filter className="mx-auto h-12 w-12 text-gray-300 mb-3" />
              <p>No events match your current filters</p>
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => {
                  setSelectedTypes([])
                  setSelectedSeverity([])
                  setShowVerifiedOnly(false)
                }}
              >
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12 text-gray-500">
              <Calendar className="mx-auto h-16 w-16 text-gray-300 mb-4" />
              <h3 className="text-lg font-medium mb-2">AI-Generated Medical Timeline</h3>
              <p className="mb-6">
                Generate a chronological timeline of medical events with AI-powered analysis and confidence scoring.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-md mx-auto text-sm text-gray-600 mb-6">
                <div className="flex items-center gap-2">
                  <Bot className="h-4 w-4" />
                  <span>AI Analysis</span>
                </div>
                <div className="flex items-center gap-2">
                  <Sparkles className="h-4 w-4" />
                  <span>Smart Insights</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="h-4 w-4" />
                  <span>Source Citations</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Helper functions for timeline event processing
function createMedicationDescription(med: MedicationRow): string {
  const parts = []
  if (med.dose) parts.push(`Dose: ${med.dose}`)
  if (med.route) parts.push(`Route: ${med.route}`)
  if (med.frequency) parts.push(`Frequency: ${med.frequency}`)
  
  return parts.length > 0 ? parts.join(' • ') : 'Medication prescribed'
}

function createRadiologyDescription(rad: RadiologyRow): string {
  const parts = []
  if (rad.bodyPart) parts.push(`Body part: ${rad.bodyPart}`)
  if (rad.finding) parts.push(`Finding: ${rad.finding}`)
  if (rad.impression) parts.push(`Impression: ${rad.impression}`)
  
  return parts.length > 0 ? parts.join(' • ') : 'Study completed'
}

function createProblemDescription(prob: ProblemRow): string {
  return `Medical condition identified and documented: ${prob.term}`
}

function determineMedicationSeverity(med: MedicationRow): TimelineEvent['severity'] {
  const drug = med.drug.toLowerCase()
  if (drug.includes('chemotherapy') || drug.includes('morphine') || drug.includes('fentanyl')) {
    return 'critical'
  }
  if (drug.includes('steroid') || drug.includes('antibiotic') || drug.includes('insulin')) {
    return 'high'
  }
  return 'moderate'
}

function determineRadiologySeverity(rad: RadiologyRow): TimelineEvent['severity'] {
  const finding = (rad.finding || '').toLowerCase()
  const impression = (rad.impression || '').toLowerCase()
  
  const criticalKeywords = ['malignant', 'metastasis', 'severe', 'critical', 'emergency']
  const highKeywords = ['abnormal', 'positive', 'lesion', 'mass', 'fracture']
  
  if (criticalKeywords.some(keyword => finding.includes(keyword) || impression.includes(keyword))) {
    return 'critical'
  }
  if (highKeywords.some(keyword => finding.includes(keyword) || impression.includes(keyword))) {
    return 'high'
  }
  return 'moderate'
}

function determineProblemSeverity(prob: ProblemRow): TimelineEvent['severity'] {
  const term = prob.term.toLowerCase()
  
  const criticalKeywords = ['cancer', 'malignant', 'heart attack', 'stroke', 'sepsis']
  const highKeywords = ['chronic', 'severe', 'acute', 'diabetes', 'hypertension']
  
  if (criticalKeywords.some(keyword => term.includes(keyword))) {
    return 'critical'
  }
  if (highKeywords.some(keyword => term.includes(keyword))) {
    return 'high'
  }
  return 'moderate'
}

function calculateEventConfidence(evidence: EvidenceLink[]): number {
  if (!evidence || evidence.length === 0) return 0.5
  
  const avgConfidence = evidence.reduce((sum, ev) => sum + (ev.confidence || 0.7), 0) / evidence.length
  const hasQuote = evidence.some(ev => ev.quote && ev.quote.length > 10)
  
  // Boost confidence if we have substantial evidence
  return Math.min(0.95, avgConfidence + (hasQuote ? 0.1 : 0))
}

function extractDateFromEvidence(evidence: EvidenceLink[]): string | null {
  // This would contain logic to extract dates from evidence quotes
  // For now, return null to use current date
  return null
}

function validateTimelineEvent(event: TimelineEvent): boolean {
  return !!(
    event.id &&
    event.date &&
    event.title &&
    event.type &&
    event.severity &&
    typeof event.confidence === 'number' &&
    event.source &&
    event.source.documentId
  )
}