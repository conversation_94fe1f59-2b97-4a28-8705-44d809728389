import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Plus, Settings2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import ModelSelector from './ModelSelector';
import type { AgentConfig, NodeConfig } from '@/hooks/admin/useLLMSelections';

interface AgentTreeViewProps {
  agents: AgentConfig[];
  availableModels: { id: string; name: string; provider: string }[];
  onModelChange: (agent: string, node: string | null, model: string, temperature: number) => void;
  onDeleteSelection: (agent: string, node: string | null) => void;
  onAddSelection: (agent: string, node: string | null) => void;
  isLoading?: boolean;
  selectedTenant: string;
}

interface AgentNodeProps {
  agent: AgentConfig;
  availableModels: { id: string; name: string; provider: string }[];
  onModelChange: (agent: string, node: string | null, model: string, temperature: number) => void;
  onDeleteSelection: (agent: string, node: string | null) => void;
  onAddSelection: (agent: string, node: string | null) => void;
  isLoading?: boolean;
}

function AgentNode({
  agent,
  availableModels,
  onModelChange,
  onDeleteSelection,
  onAddSelection,
  isLoading = false
}: AgentNodeProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const hasNodes = agent.nodes.length > 0;

  const getInheritedModel = (node: NodeConfig): string | undefined => {
    if (node.inheritsFrom === 'agent' && agent.currentModel) {
      return agent.currentModel;
    }
    if (node.inheritsFrom === 'default') {
      // Return a default model - this could be fetched from configuration
      return 'openai/gpt-3.5-turbo';
    }
    return undefined;
  };

  const getAgentInheritedModel = (): string | undefined => {
    if (agent.inheritsFrom === 'default') {
      return 'openai/gpt-3.5-turbo';
    }
    return undefined;
  };

  return (
    <Card className="mb-4">
      <CardContent className="p-4">
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <div className="flex items-center justify-between">
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="flex items-center gap-2 p-0 h-auto">
                {hasNodes ? (
                  isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )
                ) : (
                  <div className="w-4" />
                )}
                <span className="text-lg">{agent.icon}</span>
                <div className="flex flex-col items-start">
                  <span className="font-medium">{agent.displayName}</span>
                  <span className="text-xs text-muted-foreground">{agent.name}</span>
                </div>
              </Button>
            </CollapsibleTrigger>

            <div className="flex items-center gap-2">
              {agent.isConfigured ? (
                <Badge variant="default" className="text-xs">
                  Configured
                </Badge>
              ) : (
                <Badge variant="outline" className="text-xs">
                  {agent.inheritsFrom === 'default' ? 'Default' : 'Inherited'}
                </Badge>
              )}

              <div className="flex-1 min-w-[300px]">
                <ModelSelector
                  currentModel={agent.currentModel}
                  temperature={agent.temperature}
                  onModelChange={(model) => onModelChange(agent.name, null, model, agent.temperature || 0.2)}
                  onTemperatureChange={(temp) => {
                    if (agent.currentModel) {
                      onModelChange(agent.name, null, agent.currentModel, temp);
                    }
                  }}
                  onDelete={agent.isConfigured ? () => onDeleteSelection(agent.name, null) : undefined}
                  allowInherit={!agent.isConfigured}
                  inheritedModel={getAgentInheritedModel()}
                  disabled={isLoading}
                  availableModels={availableModels}
                  size="sm"
                />
              </div>

              {!agent.isConfigured && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onAddSelection(agent.name, null)}
                  disabled={isLoading}
                  className="h-8"
                >
                  <Plus className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>

          {hasNodes && (
            <CollapsibleContent className="mt-4">
              <div className="ml-6 space-y-2">
                {agent.nodes.map((node) => (
                  <div key={node.name} className="flex items-center justify-between py-2 border-l-2 border-muted pl-4">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-muted-foreground rounded-full" />
                      <div className="flex flex-col">
                        <span className="text-sm font-medium">{node.displayName}</span>
                        <span className="text-xs text-muted-foreground">{node.name}</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      {node.isConfigured ? (
                        <Badge variant="default" className="text-xs">
                          Configured
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-xs">
                          {node.inheritsFrom === 'agent' ? 'From Agent' : 'Default'}
                        </Badge>
                      )}

                      <div className="min-w-[300px]">
                        <ModelSelector
                          currentModel={node.currentModel}
                          temperature={node.temperature}
                          onModelChange={(model) => onModelChange(agent.name, node.name, model, node.temperature || 0.2)}
                          onTemperatureChange={(temp) => {
                            if (node.currentModel) {
                              onModelChange(agent.name, node.name, node.currentModel, temp);
                            }
                          }}
                          onDelete={node.isConfigured ? () => onDeleteSelection(agent.name, node.name) : undefined}
                          allowInherit={!node.isConfigured}
                          inheritedModel={getInheritedModel(node)}
                          disabled={isLoading}
                          availableModels={availableModels}
                          size="sm"
                        />
                      </div>

                      {!node.isConfigured && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onAddSelection(agent.name, node.name)}
                          disabled={isLoading}
                          className="h-8"
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CollapsibleContent>
          )}
        </Collapsible>
      </CardContent>
    </Card>
  );
}

export function AgentTreeView({
  agents,
  availableModels,
  onModelChange,
  onDeleteSelection,
  onAddSelection,
  isLoading = false,
  selectedTenant
}: AgentTreeViewProps): React.ReactElement {
  if (agents.length === 0) {
    return (
      <div className="text-center py-12">
        <Settings2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium mb-2">No agents found</h3>
        <p className="text-muted-foreground">
          {selectedTenant === '*' 
            ? 'No agents are configured yet.' 
            : `No agents configured for tenant "${selectedTenant}".`}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {agents.map((agent) => (
        <AgentNode
          key={agent.name}
          agent={agent}
          availableModels={availableModels}
          onModelChange={onModelChange}
          onDeleteSelection={onDeleteSelection}
          onAddSelection={onAddSelection}
          isLoading={isLoading}
        />
      ))}
    </div>
  );
}

export default AgentTreeView;
