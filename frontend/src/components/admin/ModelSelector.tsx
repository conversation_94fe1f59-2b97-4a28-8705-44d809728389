import React, { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Settings, Thermometer, Trash2, RotateCcw } from 'lucide-react';
import { formatModelDisplayName, parseModelString } from '@/hooks/admin/useLLMSelections';

interface ModelSelectorProps {
  currentModel?: string;
  temperature?: number;
  onModelChange: (model: string) => void;
  onTemperatureChange: (temp: number) => void;
  onDelete?: () => void;
  allowInherit?: boolean;
  inheritedModel?: string;
  disabled?: boolean;
  availableModels: { id: string; name: string; provider: string }[];
  size?: 'sm' | 'default';
  showTemperature?: boolean;
}

const PROVIDER_ICONS: Record<string, string> = {
  openai: '🤖',
  anthropic: '🧠',
  google: '🔍',
  gemini: '💎',
  groq: '⚡',
  cohere: '🌊',
  mistral: '🌪️'
};

const PROVIDER_NAMES: Record<string, string> = {
  openai: 'OpenAI',
  anthropic: 'Anthropic',
  google: 'Google',
  gemini: 'Gemini',
  groq: 'Groq',
  cohere: 'Cohere',
  mistral: 'Mistral'
};

export function ModelSelector({
  currentModel,
  temperature = 0.2,
  onModelChange,
  onTemperatureChange,
  onDelete,
  allowInherit = false,
  inheritedModel,
  disabled = false,
  availableModels,
  size = 'default',
  showTemperature = true
}: ModelSelectorProps): React.ReactElement {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  // Group models by provider
  const modelsByProvider = availableModels.reduce((acc, model) => {
    if (!acc[model.provider]) {
      acc[model.provider] = [];
    }
    acc[model.provider].push(model);
    return acc;
  }, {} as Record<string, typeof availableModels>);

  const handleModelSelect = (value: string): void => {
    if (value === 'inherit') {
      if (onDelete) {
        onDelete();
      }
    } else {
      onModelChange(value);
    }
  };

  const getDisplayValue = () => {
    if (currentModel) {
      return currentModel;
    }
    if (allowInherit && inheritedModel) {
      return 'inherit';
    }
    return undefined;
  };

  const getDisplayText = () => {
    if (currentModel) {
      return formatModelDisplayName(currentModel);
    }
    if (allowInherit && inheritedModel) {
      return `Inherit (${formatModelDisplayName(inheritedModel)})`;
    }
    return 'Select model...';
  };

  const isInherited = !currentModel && allowInherit && inheritedModel;

  return (
    <div className="flex items-center gap-2">
      <div className="flex-1">
        <Select
          value={getDisplayValue()}
          onValueChange={handleModelSelect}
          disabled={disabled}
        >
          <SelectTrigger className={size === 'sm' ? 'h-8 text-xs' : undefined}>
            <SelectValue>
              <div className="flex items-center gap-2">
                {currentModel && (
                  <span className="text-xs">
                    {PROVIDER_ICONS[parseModelString(currentModel).provider] || '🤖'}
                  </span>
                )}
                {isInherited && <Badge variant="outline" className="text-xs">Inherited</Badge>}
                <span className={size === 'sm' ? 'text-xs' : undefined}>
                  {getDisplayText()}
                </span>
              </div>
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {allowInherit && inheritedModel && (
              <>
                <SelectItem value="inherit">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">Inherit</Badge>
                    <span>{formatModelDisplayName(inheritedModel)}</span>
                  </div>
                </SelectItem>
                <div className="border-t my-1" />
              </>
            )}
            
            {Object.entries(modelsByProvider).map(([provider, models]) => (
              <SelectGroup key={provider}>
                <SelectLabel className="flex items-center gap-2">
                  <span>{PROVIDER_ICONS[provider] || '🤖'}</span>
                  {PROVIDER_NAMES[provider] || provider}
                </SelectLabel>
                {models.map((model) => (
                  <SelectItem key={`${model.provider}/${model.id}`} value={`${model.provider}/${model.id}`}>
                    <div className="flex items-center gap-2">
                      <span className="font-mono text-xs text-muted-foreground">
                        {model.id}
                      </span>
                      <span>{model.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectGroup>
            ))}
          </SelectContent>
        </Select>
      </div>

      {(currentModel || showTemperature) && (
        <Popover open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size={size === 'sm' ? 'sm' : 'default'}
              className={size === 'sm' ? 'h-8 w-8 p-0' : 'h-10 w-10 p-0'}
              disabled={disabled}
            >
              <Settings className={size === 'sm' ? 'h-3 w-3' : 'h-4 w-4'} />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="end">
            <div className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium leading-none">Model Settings</h4>
                <p className="text-sm text-muted-foreground">
                  Configure temperature and other parameters
                </p>
              </div>

              {showTemperature && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Thermometer className="h-4 w-4" />
                    <Label htmlFor="temperature">Temperature: {temperature}</Label>
                  </div>
                  <Slider
                    id="temperature"
                    min={0}
                    max={2}
                    step={0.1}
                    value={[temperature]}
                    onValueChange={(value) => onTemperatureChange(value[0])}
                    className="w-full"
                    disabled={disabled}
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Conservative (0)</span>
                    <span>Creative (2)</span>
                  </div>
                </div>
              )}

              <div className="flex gap-2">
                {onDelete && currentModel && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      onDelete();
                      setIsSettingsOpen(false);
                    }}
                    disabled={disabled}
                    className="flex-1"
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    Remove
                  </Button>
                )}
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onTemperatureChange(0.2)}
                  disabled={disabled}
                  className="flex-1"
                >
                  <RotateCcw className="h-3 w-3 mr-1" />
                  Reset Temp
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      )}
    </div>
  );
}

export default ModelSelector;
