/**
 * Multi-Currency Pricing Card Component for PI Lawyer AI
 * Displays subscription plans with automatic currency detection and price parity
 */

'use client';

import React, { useState, useEffect } from 'react';
import { CheckIcon, InformationCircleIcon } from '@heroicons/react/24/outline';
import type {
  PlanTier,
  PricingResult,
  SupportedCurrency,
  Customer} from '@/lib/services/multi-currency-pricing';
import {
  MultiCurrencyPricingService,
  CurrencyConfigService
} from '@/lib/services/multi-currency-pricing';

// Re-export types for use in other components
export type { Customer, PricingResult, PlanTier, SupportedCurrency };
import { CurrencySelector, CurrencyDisplay } from './CurrencySelector';

interface PricingCardProps {
  planTier: PlanTier;
  customer: Customer;
  features: string[];
  isPopular?: boolean;
  onSubscribe: (pricing: PricingResult) => void;
  className?: string;
}

interface PricingCardSkeletonProps {
  className?: string;
}

const PricingCardSkeleton: React.FC<PricingCardSkeletonProps> = ({ className = '' }) => (
  <div className={`bg-white rounded-lg shadow-lg p-6 animate-pulse ${className}`}>
    <div className="h-6 bg-gray-200 rounded mb-4"></div>
    <div className="h-12 bg-gray-200 rounded mb-6"></div>
    <div className="space-y-3">
      {[1, 2, 3, 4].map((i) => (
        <div key={i} className="h-4 bg-gray-200 rounded"></div>
      ))}
    </div>
    <div className="h-10 bg-gray-200 rounded mt-6"></div>
  </div>
);

export const MultiCurrencyPricingCard: React.FC<PricingCardProps> = ({
  planTier,
  customer,
  features,
  isPopular = false,
  onSubscribe,
  className = ''
}) => {
  const [pricing, setPricing] = useState<PricingResult | null>(null);
  const [selectedCurrency, setSelectedCurrency] = useState<SupportedCurrency>('USD');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load initial pricing
  useEffect(() => {
    const loadPricing = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const result = await MultiCurrencyPricingService.getPricingForCustomer(planTier, customer);
        setPricing(result);
        setSelectedCurrency(result.currency);
      } catch (err) {
        console.error('Error loading pricing:', err);
        setError('Failed to load pricing information');
      } finally {
        setIsLoading(false);
      }
    };

    loadPricing();
  }, [planTier, customer]);

  // Update pricing when currency changes
  const handleCurrencyChange = async (currency: SupportedCurrency) => {
    setSelectedCurrency(currency);
    
    try {
      const customerWithCurrency = { ...customer, preferred_currency: currency };
      const result = await MultiCurrencyPricingService.getPricingForCustomer(planTier, customerWithCurrency);
      setPricing(result);
    } catch (err) {
      console.error('Error updating pricing for currency:', err);
      setError('Failed to update pricing');
    }
  };

  const getPlanDisplayName = (tier: PlanTier): string => {
    return tier.charAt(0).toUpperCase() + tier.slice(1);
  };

  const getPlanDescription = (tier: PlanTier): string => {
    const descriptions = {
      solo: 'Perfect for individual lawyers',
      team: 'Ideal for small law firms',
      scale: 'Built for growing practices'
    };
    return descriptions[tier];
  };

  if (isLoading) {
    return <PricingCardSkeleton className={className} />;
  }

  if (error || !pricing) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="text-center text-red-600">
          <InformationCircleIcon className="h-8 w-8 mx-auto mb-2" />
          <p>{error || 'Failed to load pricing'}</p>
        </div>
      </div>
    );
  }

  const cardClasses = `
    relative bg-white rounded-lg shadow-lg p-6 transition-all duration-200
    ${isPopular ? 'ring-2 ring-blue-500 scale-105' : 'hover:shadow-xl'}
    ${className}
  `;

  return (
    <div className={cardClasses}>
      {/* Popular badge */}
      {isPopular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
            Most Popular
          </span>
        </div>
      )}

      {/* Plan header */}
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          {getPlanDisplayName(planTier)} Plan
        </h3>
        <p className="text-gray-600 text-sm">
          {getPlanDescription(planTier)}
        </p>
      </div>

      {/* Pricing display */}
      <div className="text-center mb-6">
        <div className="flex items-baseline justify-center mb-2">
          <span className="text-4xl font-bold text-gray-900">
            {pricing.formattedPrice.split('/')[0]}
          </span>
          <span className="text-gray-600 ml-1">/month</span>
        </div>
        
        {/* Currency selector */}
        <div className="flex justify-center mb-2">
          <CurrencySelector
            selectedCurrency={selectedCurrency}
            onCurrencyChange={handleCurrencyChange}
            customerId={customer.id}
            showLabel={false}
            size="sm"
          />
        </div>

        {/* Tax notice for EU */}
        {pricing.taxInclusive && (
          <p className="text-xs text-gray-500">
            Price includes applicable VAT
          </p>
        )}

        {/* Detection method notice */}
        {pricing.detectionMethod !== 'user_preference' && (
          <p className="text-xs text-blue-600 mt-1">
            <CurrencyDisplay 
              currency={pricing.currency} 
              region={pricing.region}
              showFlag={false}
              className="font-medium"
            />
            {' '}pricing detected for your region
          </p>
        )}
      </div>

      {/* Features list */}
      <div className="mb-6">
        <ul className="space-y-3">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start">
              <CheckIcon className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700 text-sm">{feature}</span>
            </li>
          ))}
        </ul>
      </div>

      {/* Subscribe button */}
      <button
        onClick={() => onSubscribe(pricing)}
        className={`
          w-full py-3 px-4 rounded-lg font-medium transition-colors duration-200
          ${isPopular 
            ? 'bg-blue-600 text-white hover:bg-blue-700' 
            : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
          }
        `}
      >
        Get Started
      </button>

      {/* Price parity notice */}
      <div className="mt-4 text-center">
        <p className="text-xs text-gray-500">
          Price parity model: {pricing.currency} {pricing.amount} = USD {pricing.priceParityBaseUsd}
        </p>
      </div>
    </div>
  );
};

/**
 * Pricing comparison component showing all plans
 */
interface PricingComparisonProps {
  customer: Customer;
  onSubscribe: (pricing: PricingResult) => void;
  className?: string;
}

export const MultiCurrencyPricingComparison: React.FC<PricingComparisonProps> = ({
  customer,
  onSubscribe,
  className = ''
}) => {
  const plans: {
    tier: PlanTier;
    features: string[];
    isPopular?: boolean;
  }[] = [
    {
      tier: 'solo',
      features: [
        'AI-powered legal research',
        'Document analysis',
        'Basic case management',
        '5 AI consultations/month',
        'Email support'
      ]
    },
    {
      tier: 'team',
      isPopular: true,
      features: [
        'Everything in Solo',
        'Team collaboration tools',
        'Advanced case management',
        '25 AI consultations/month',
        'Priority support',
        'Custom templates'
      ]
    },
    {
      tier: 'scale',
      features: [
        'Everything in Team',
        'Unlimited AI consultations',
        'Advanced analytics',
        'API access',
        'Dedicated support',
        'Custom integrations',
        'White-label options'
      ]
    }
  ];

  return (
    <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>
      {plans.map((plan) => (
        <MultiCurrencyPricingCard
          key={plan.tier}
          planTier={plan.tier}
          customer={customer}
          features={plan.features}
          isPopular={plan.isPopular}
          onSubscribe={onSubscribe}
        />
      ))}
    </div>
  );
};

/**
 * Simple pricing display for existing subscriptions
 */
interface CurrentPricingDisplayProps {
  pricing: PricingResult;
  showCurrencySelector?: boolean;
  onCurrencyChange?: (currency: SupportedCurrency) => void;
  className?: string;
}

export const CurrentPricingDisplay: React.FC<CurrentPricingDisplayProps> = ({
  pricing,
  showCurrencySelector = false,
  onCurrencyChange,
  className = ''
}) => {
  return (
    <div className={`bg-gray-50 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h4 className="font-medium text-gray-900">
            {getPlanDisplayName(pricing.planTier)} Plan
          </h4>
          <p className="text-2xl font-bold text-gray-900 mt-1">
            {pricing.formattedPrice}
          </p>
          {pricing.taxInclusive && (
            <p className="text-xs text-gray-500 mt-1">
              Includes applicable taxes
            </p>
          )}
        </div>
        
        {showCurrencySelector && onCurrencyChange && (
          <div>
            <CurrencySelector
              selectedCurrency={pricing.currency}
              onCurrencyChange={onCurrencyChange}
              showLabel={false}
              size="sm"
            />
          </div>
        )}
      </div>
    </div>
  );
};

// Helper function (also used in other components)
function getPlanDisplayName(tier: PlanTier): string {
  return tier.charAt(0).toUpperCase() + tier.slice(1);
}
