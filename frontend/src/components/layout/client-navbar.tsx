"use client"

import React, {  useState, useEffect  } from 'react';
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import {
  Bell,
  Home,
  FileText,
  User,
  LogOut,
  Menu,
  Settings
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useSupabase } from "@/lib/supabase/provider"
import { Badge } from "@/components/ui/badge"
import { useMattersApi } from "@/hooks/useMattersApi"
import { PracticeArea, WorkType } from "@/types/domain/tenants/Matter"

interface UserMetadata {
  roles?: string[];
}

interface AppMetadata {
  roles?: string[];
}

interface UserWithMetadata {
  app_metadata?: AppMetadata;
  user_metadata?: UserMetadata;
}

export function ClientNavbar(): React.ReactElement {
  const pathname = usePathname()
  const router = useRouter()
  const { supabase } = useSupabase()
  const { getAllMatters } = useMattersApi()
  const [user, setUser] = useState<{ id: string; email?: string; user_metadata?: Record<string, unknown> } | null>(null)
  const [notifications, setNotifications] = useState<{ id: string; title: string; message: string }[]>([])
  const [navigationLabel, setNavigationLabel] = useState('Cases') // Dynamic label

  useEffect(() => {
    const fetchUserAndNotifications = async () => {
      // Fetch user
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        setUser(user)

        // Fetch notifications for the client
        const { data: notifData } = await supabase
          .from('notifications')
          .select('*')
          .eq('user_id', user.id)
          .eq('read', false)
          .order('created_at', { ascending: false })
          .limit(5)

        setNotifications(notifData || [])
      }
    }

    fetchUserAndNotifications()
  }, [supabase])

  // Determine navigation label based on user's matters
  useEffect(() => {
    const determineLabel = async () => {
      try {
        const matters = await getAllMatters({ limit: 50 })

        if (matters.length === 0) {
          setNavigationLabel('Cases') // Default for new users
          return
        }

        // Check if all matters are litigation
        const allLitigation = matters.every(matter => matter.workType === WorkType.LITIGATION)
        setNavigationLabel(allLitigation ? 'Cases' : 'Matters')
      } catch (error) {
        console.error('Error determining navigation label:', error)
        setNavigationLabel('Cases') // Fallback to Cases on error
      }
    }

    determineLabel()
  }, [getAllMatters])

  const handleSignOut = async () => {
    await supabase.auth.signOut()
  }

  // Dynamically build nav items based on conflict check status
  const buildNavItems = () => {
    const items = [
      {
        href: "/client-portal/dashboard",
        icon: Home,
        label: "Dashboard",
        active: pathname === "/client-portal/dashboard" || pathname === "/client-portal"
      },
      {
        href: "/client-portal/matters",
        icon: FileText,
        label: `My ${navigationLabel}`,
        active: pathname.startsWith("/client-portal/matters")
      },
      // Documents link is always visible, but the upload functionality is controlled on the documents page
      {
        href: "/client-portal/documents",
        icon: FileText,
        label: "Documents",
        active: pathname.startsWith("/client-portal/documents")
      },
      {
        href: "/client-portal/profile",
        icon: User,
        label: "Profile",
        active: pathname === "/client-portal/profile"
      }
    ];

    return items;
  };

  const navItems = buildNavItems();

  return (
    <nav className="bg-white shadow-md">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            {/* Logo or Brand */}
            <div className="flex-shrink-0 flex items-center">
              <Link href="/client/dashboard" className="text-xl font-bold text-primary">
                Client Portal
              </Link>
            </div>

            {/* Navigation Links */}
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`
                    inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium
                    ${item.active
                      ? 'border-primary text-gray-900'
                      : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                    }
                  `}
                >
                  <item.icon className="mr-2 h-4 w-4" />
                  {item.label}
                </Link>
              ))}
            </div>
          </div>

          {/* Right side - Notifications and User Menu */}
          <div className="hidden sm:ml-6 sm:flex sm:items-center">
            {/* Notifications Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="relative">
                  <Bell className="h-5 w-5" />
                  {notifications.length > 0 && (
                    <Badge
                      variant="destructive"
                      className="absolute -top-2 -right-2 px-1.5 py-0.5 text-xs"
                    >
                      {notifications.length}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-64">
                <DropdownMenuLabel>Notifications</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {notifications.length === 0 ? (
                  <DropdownMenuItem disabled>
                    No new notifications
                  </DropdownMenuItem>
                ) : (
                  notifications.map((notification) => (
                    <DropdownMenuItem key={notification.id}>
                      {notification.message}
                    </DropdownMenuItem>
                  ))
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* User Menu Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="ml-3">
                  {(user?.user_metadata?.first_name as string) || 'Client'}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {/* Only show Settings for Admin users (firm_role: admin or roles containing admin) */}
                {(() => {
                  const appMetadata = (user as UserWithMetadata)?.app_metadata;
                  const userMetadata = (user as UserWithMetadata)?.user_metadata;
                  return (
                    appMetadata?.roles?.includes('admin') ||
                    userMetadata?.roles?.includes('admin') ||
                    userMetadata?.roles?.includes('administrator')
                  );
                })() && (
                  <DropdownMenuItem onSelect={() => router.push('/settings')}>
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem onSelect={() => handleSignOut()}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Mobile menu button */}
          <div className="-mr-2 flex items-center sm:hidden">
            <Button variant="ghost" size="icon">
              <Menu className="h-6 w-6" />
            </Button>
          </div>
        </div>
      </div>
    </nav>
  )
}
