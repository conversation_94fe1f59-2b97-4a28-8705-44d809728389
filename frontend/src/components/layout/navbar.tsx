'use client'

import React, {  useState, useEffect  } from 'react';
import { useRouter } from 'next/navigation'
import { Menu, Search, Bell, User, Settings, Calendar, Briefcase } from 'lucide-react'
import { useSupabase } from '@/lib/supabase/provider'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { SubscriptionBadge } from '@/components/subscription/subscription-badge'
import { UpgradeModal } from '@/components/subscription/upgrade-modal'
import { useMattersApi } from '@/hooks/useMattersApi'
import { PracticeA<PERSON>, WorkType } from '@/types/domain/tenants/Matter'

interface UserMetadata {
  roles?: string[];
}

interface AppMetadata {
  roles?: string[];
}

interface UserWithMetadata {
  app_metadata?: AppMetadata;
  user_metadata?: UserMetadata;
}

interface NavbarProps {
  onMenuClick: () => void
}

interface SearchResult {
  id: string
  type: 'case' | 'client' | 'document' | 'matter'
  title: string
  subtitle?: string
}

/**
 * Hook to determine the appropriate navigation label for matters/cases in navbar
 */
function useNavbarNavigationLabel() {
  const { getAllMatters } = useMattersApi()
  const [navigationLabel, setNavigationLabel] = useState('Cases') // Default to Cases for backward compatibility

  useEffect(() => {
    const determineLabel = async () => {
      try {
        const matters = await getAllMatters({ limit: 50 }) // Get a sample

        if (matters.length === 0) {
          setNavigationLabel('Cases') // Default for new users
          return
        }

        // Check if all matters are litigation
        const allLitigation = matters.every(matter => matter.workType === WorkType.LITIGATION)

        if (allLitigation) {
          setNavigationLabel('Cases')
        } else {
          // Mixed practice areas - use "Matters"
          setNavigationLabel('Matters')
        }
      } catch (error) {
        console.error('Error determining navbar navigation label:', error)
        setNavigationLabel('Cases') // Fallback to Cases on error
      }
    }

    determineLabel()
  }, [getAllMatters])

  return navigationLabel
}

export function Navbar({ onMenuClick }: NavbarProps): React.ReactElement {
  const router = useRouter()
  const { supabase } = useSupabase()
  const navigationLabel = useNavbarNavigationLabel()

  const [searchOpen, setSearchOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [notifications, setNotifications] = useState<{ id: string; title: string; message: string }[]>([])
  const [pendingDeadlines, setPendingDeadlines] = useState(0)
  const [user, setUser] = useState<{ id?: string; email?: string; user_metadata?: Record<string, unknown>; role?: string; avatar_url?: string | null } | null>(null)
  const [upgradeModalOpen, setUpgradeModalOpen] = useState(false)

  // Fetch pending deadlines count
  useEffect(() => {
    const fetchPendingDeadlines = async () => {
      try {
        const response = await fetch('/api/deadlines/validation/stats')
        if (response.ok) {
          const data = await response.json()
          setPendingDeadlines(data.counts?.pending || 0)
        }
      } catch (error) {
        console.error('Error fetching pending deadlines:', error)
      }
    }

    fetchPendingDeadlines()
    // Refresh every 5 minutes
    const interval = setInterval(fetchPendingDeadlines, 5 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  // Fetch user profile and notifications
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (process.env.NODE_ENV === 'development') {
        setUser({
          email: '<EMAIL>',
          role: 'Attorney',
          avatar_url: null
        })
        // Set empty notifications in development mode
        setNotifications([])
        return
      }

      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        const { data: profile } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single()
        setUser({ ...user, ...profile })

        // Only fetch notifications in production
        const { data: notifs } = await supabase
          .from('notifications')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(5)
        setNotifications(notifs || [])
      }
    }

    fetchUserProfile()
  }, [supabase])

  // Search handler
  const handleSearch = async (query: string) => {
    setSearchQuery(query)
    if (query.length < 2) {
      setSearchResults([])
      return
    }

    // TODO: Implement combined Pinecone + Supabase search
    const results: SearchResult[] = [] // Placeholder for search results
    setSearchResults(results)
  }

  // Sign out handler
  const handleSignOut = async () => {
    await supabase.auth.signOut()
    router.push('/login')
  }

  return (
    <header className="sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between px-4">
        {/* Left section */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden"
            onClick={onMenuClick}
          >
            <Menu className="h-5 w-5" />
          </Button>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              className="relative h-9 w-9 p-0 xl:h-10 xl:w-60 xl:justify-start xl:px-3 xl:py-2"
              onClick={() => setSearchOpen(true)}
            >
              <Search className="h-4 w-4 xl:mr-2" />
              <span className="hidden xl:inline-flex">Search...</span>
              <kbd className="pointer-events-none absolute right-1.5 top-2 hidden h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 xl:flex">
                <span className="text-xs">⌘</span>K
              </kbd>
            </Button>
          </div>
        </div>

        {/* Right section */}
        <div className="flex items-center gap-4">
          {/* Pending Deadlines */}
          <Button
            variant="ghost"
            size="icon"
            className="relative"
            onClick={() => router.push('/deadlines/validation')}
          >
            <Calendar className="h-5 w-5" />
            {pendingDeadlines > 0 && (
              <Badge
                variant="destructive"
                className="absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 text-xs"
              >
                {pendingDeadlines}
              </Badge>
            )}
          </Button>
          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-5 w-5" />
                {notifications.length > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 text-xs"
                  >
                    {notifications.length}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
              <DropdownMenuLabel>Notifications</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {notifications.length === 0 ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  No new notifications
                </div>
              ) : (
                notifications.map((notification) => (
                  <DropdownMenuItem key={notification.id}>
                    {/* TODO: Implement notification item UI */}
                    {notification.message}
                  </DropdownMenuItem>
                ))
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Subscription Badge */}
          <SubscriptionBadge
            onClick={() => setUpgradeModalOpen(true)}
            className="hidden sm:inline-flex"
          />

          {/* User menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user?.avatar_url || undefined} />
                  <AvatarFallback>
                    {user?.email?.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel className="flex flex-col">
                <span>{user?.email}</span>
                <span className="text-xs text-muted-foreground">
                  {user?.role}
                </span>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onSelect={() => router.push('/profile')}>
                <User className="mr-2 h-4 w-4" />
                Profile
              </DropdownMenuItem>
              {/* Only show Settings for Admin users (firm_role: admin or roles containing admin) */}
              {(() => {
                const appMetadata = (user as UserWithMetadata)?.app_metadata;
                const userMetadata = (user as UserWithMetadata)?.user_metadata;
                return (
                  appMetadata?.roles?.includes('admin') ||
                  userMetadata?.roles?.includes('admin') ||
                  userMetadata?.roles?.includes('administrator')
                );
              })() && (
                <DropdownMenuItem onSelect={() => router.push('/settings')}>
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onSelect={handleSignOut}>
                Sign out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Upgrade Modal */}
      <UpgradeModal
        open={upgradeModalOpen}
        onClose={() => setUpgradeModalOpen(false)}
      />

      {/* Search Dialog */}
      <CommandDialog open={searchOpen} onOpenChange={setSearchOpen}>
        <CommandInput
          placeholder={`Search ${navigationLabel.toLowerCase()}, clients, or documents...`}
          value={searchQuery}
          onValueChange={handleSearch}
        />
        <CommandList>
          <CommandEmpty>No results found.</CommandEmpty>
          {searchResults.length > 0 && (
            <>
              <CommandGroup heading={navigationLabel}>
                {searchResults
                  .filter((result) => result.type === 'case' || result.type === 'matter')
                  .map((result) => (
                    <CommandItem
                      key={result.id}
                      onSelect={() => {
                        router.push(`/matters/${result.id}`)
                        setSearchOpen(false)
                      }}
                    >
                      <Briefcase className="mr-2 h-4 w-4" />
                      <div className="flex flex-col">
                        <span>{result.title}</span>
                        {result.subtitle && (
                          <span className="text-xs text-muted-foreground">
                            {result.subtitle}
                          </span>
                        )}
                      </div>
                    </CommandItem>
                  ))}
              </CommandGroup>
              <CommandGroup heading="Clients">
                {searchResults
                  .filter((result) => result.type === 'client')
                  .map((result) => (
                    <CommandItem
                      key={result.id}
                      onSelect={() => {
                        router.push(`/clients/${result.id}`)
                        setSearchOpen(false)
                      }}
                    >
                      <User className="mr-2 h-4 w-4" />
                      <div className="flex flex-col">
                        <span>{result.title}</span>
                        {result.subtitle && (
                          <span className="text-xs text-muted-foreground">
                            {result.subtitle}
                          </span>
                        )}
                      </div>
                    </CommandItem>
                  ))}
              </CommandGroup>
            </>
          )}
        </CommandList>
      </CommandDialog>
    </header>
  )
}
