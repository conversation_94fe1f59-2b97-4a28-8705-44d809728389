"use client";

import React, { useState, useCallback, useRef } from "react";
import { FileUpIcon, Loader2, TrashIcon, CheckCircleIcon, AlertCircleIcon, BrainCircuit } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

// Document types for categorization
const DOCUMENT_TYPES = [
  { id: "medical", label: "Medical Records" },
  { id: "insurance", label: "Insurance Documents" },
  { id: "police", label: "Police Reports" },
  { id: "legal", label: "Legal Documents" },
  { id: "photos", label: "Photos & Evidence" },
  { id: "correspondence", label: "Correspondence" },
  { id: "other", label: "Other Documents" }
];

export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  documentType: string;
  description: string;
  uploadProgress: number;
  status: "uploading" | "success" | "error" | "ready";
  errorMessage?: string;
  url?: string;
  enableOcr?: boolean;
  ocrType?: "text" | "tasks" | "medical";
  analysisStatus?: "processing" | "completed" | "failed";
  file?: File; // Reference to the actual file object for upload
}

interface DocumentUploadProps {
  onUploadComplete?: (files: UploadedFile[]) => void;
  maxFiles?: number;
  maxSize?: number; // in MB
  caseId?: string;
  clientId?: string;
  allowedTypes?: string[];
  enableOcrByDefault?: boolean;
  defaultOcrType?: "text" | "tasks" | "medical";
}

export function DocumentUpload({
  onUploadComplete,
  maxFiles = 10,
  maxSize = 50, // Default 50MB
  caseId,
  clientId,
  allowedTypes = [".pdf", ".jpg", ".jpeg", ".png", ".doc", ".docx", ".txt"],
  enableOcrByDefault: _enableOcrByDefault = false,
  defaultOcrType: _defaultOcrType = "text"
}: DocumentUploadProps): React.ReactElement {
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  // Document processing happens automatically now
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Handle file selection
  const handleFileSelect = useCallback(
    (selectedFiles: FileList | null) => {
      if (!selectedFiles?.length) return;

      // Check if max files limit is reached
      if (files.length + selectedFiles.length > maxFiles) {
        toast({
          title: "Too many files",
          description: `You can only upload up to ${maxFiles} files at once.`,
          variant: "destructive",
        });
        return;
      }

      // Process each file
      const newFiles: UploadedFile[] = Array.from(selectedFiles)
        .filter(file => {
          // Check file size
          if (file.size > maxSize * 1024 * 1024) {
            toast({
              title: "File too large",
              description: `${file.name} exceeds the maximum size of ${maxSize}MB.`,
              variant: "destructive",
            });
            return false;
          }

          // Check file type
          const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`;
          if (!allowedTypes.includes(fileExtension)) {
            toast({
              title: "Unsupported file type",
              description: `${file.name} is not a supported file type.`,
              variant: "destructive",
            });
            return false;
          }

          return true;
        })
        .map(file => ({
          id: crypto.randomUUID(),
          name: file.name,
          size: file.size,
          type: file.type,
          documentType: "other", // Default type
          description: "",
          uploadProgress: 0,
          status: "ready" as const,
          // All documents are automatically processed now
          enableOcr: true,
          ocrType: "text",
          file // Keep reference to the file object
        }));

      setFiles(prev => [...prev, ...newFiles]);
    },
    [files.length, maxFiles, maxSize, allowedTypes, toast]
  );

  // Handle drag events
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);
      const droppedFiles = e.dataTransfer.files;
      handleFileSelect(droppedFiles);
    },
    [handleFileSelect]
  );

  // Trigger file input click
  const handleButtonClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // Handle file input change
  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      handleFileSelect(e.target.files);
      // Reset input to allow selecting the same file again
      if (fileInputRef.current) fileInputRef.current.value = '';
    },
    [handleFileSelect]
  );

  // Update document type for a file
  const handleDocumentTypeChange = useCallback((fileId: string, documentType: string) => {
    setFiles(prev =>
      prev.map(file =>
        file.id === fileId ? { ...file, documentType } : file
      )
    );
  }, []);

  // Update description for a file
  const handleDescriptionChange = useCallback((fileId: string, description: string) => {
    setFiles(prev =>
      prev.map(file =>
        file.id === fileId ? { ...file, description } : file
      )
    );
  }, []);

  // Remove a file from the list
  const handleRemoveFile = useCallback((fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
  }, []);

  // Upload all files
  const handleUpload = useCallback(async () => {
    // Check if we have files to upload
    if (!files.length || files.every(f => f.status === "success")) {
      toast({
        title: "No files to upload",
        description: "Please add files to upload.",
      });
      return;
    }

    // Set files to uploading state
    setFiles(prev =>
      prev.map(file =>
        file.status === "ready" ? { ...file, status: "uploading", uploadProgress: 0 } : file
      )
    );

    // Upload each file
    for (const file of files.filter(f => f.status === "uploading")) {
      try {
        // Create FormData
        const formData = new FormData();
        // Make sure the file is not undefined before appending
        if (file.file) {
          formData.append("file", file.file);
        } else {
          throw new Error("File is undefined");
        }
        formData.append("documentType", file.documentType);
        formData.append("description", file.description);
        if (caseId) formData.append("caseId", caseId);
        if (clientId) formData.append("clientId", clientId);

        // Document processing happens automatically now
        formData.append("priority", "high"); // Set high priority for user uploads

        // Upload to backend
        const xhr = new XMLHttpRequest();
        xhr.upload.addEventListener('progress', (e) => {
          const percent = (e.loaded / e.total) * 100;
          setFiles(prev =>
            prev.map(f =>
              f.id === file.id ? { ...f, uploadProgress: percent } : f
            )
          );
        });
        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            const result = JSON.parse(xhr.responseText);

            // Update file status
            setFiles(prev =>
              prev.map(f =>
                f.id === file.id
                  ? {
                      ...f,
                      status: "success",
                      uploadProgress: 100,
                      url: result.data?.file_url || result.url,
                      analysisStatus: result.processing?.status === "queued" ? "processing" : undefined
                    }
                  : f
              )
            );

            // Show document processing status
            toast({
              title: "Document uploaded",
              description: "The document will be automatically processed with Gemini 2.0 Flash Thinking.",
            });
          } else {
            throw new Error(`Upload failed: ${xhr.statusText}`);
          }
        };
        xhr.onerror = () => {
          throw new Error('Upload failed');
        };
        xhr.open('POST', '/api/documents/upload', true);
        xhr.send(formData);

      } catch (error) {
        console.error("Upload error:", error);

        // Update file status to error
        setFiles(prev =>
          prev.map(f =>
            f.id === file.id
              ? {
                  ...f,
                  status: "error",
                  errorMessage: error instanceof Error ? error.message : "Upload failed"
                }
              : f
          )
        );

        toast({
          title: "Upload failed",
          description: `Failed to upload ${file.name}.`,
          variant: "destructive",
        });
      }
    }

    // Notify parent component of upload completion
    const uploadedFiles = files.filter(f => f.status === "success");
    if (uploadedFiles.length && onUploadComplete) {
      onUploadComplete(uploadedFiles);
    }
  }, [files, caseId, clientId, onUploadComplete, toast]);

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  return (
    <div className="w-full space-y-4">
      {/* Drop zone */}
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-8 text-center hover:bg-muted/50 transition-colors cursor-pointer",
          isDragging ? "border-primary bg-muted/50" : "border-muted-foreground/25"
        )}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={handleButtonClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          multiple
          onChange={handleFileInputChange}
          accept={allowedTypes.join(",")}
        />
        <FileUpIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-1">Upload Documents</h3>
        <p className="text-sm text-muted-foreground mb-2">
          Drag and drop files here or click to browse
        </p>
        <p className="text-xs text-muted-foreground">
          Supported formats: {allowedTypes.join(", ")} • Max size: {maxSize}MB
        </p>
      </div>

      {/* Document Processing Information */}
      <div className="space-y-4 mt-4">
        <div className="p-4 rounded-md bg-muted/50 border border-muted">
          <div className="flex items-center space-x-2">
            <BrainCircuit className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Automatic Document Processing</h3>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Documents are automatically processed with Gemini 2.0 Flash Thinking to extract text, identify tasks, and generate insights.
            Processing happens in the background after upload.
          </p>
        </div>
      </div>

      {/* File list */}
      {files.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Selected Files</h3>
          {files.map((file) => (
            <Card key={file.id} className="overflow-hidden">
              <CardHeader className="py-3 px-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {file.status === "uploading" && <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />}
                    {file.status === "success" && <CheckCircleIcon className="h-4 w-4 text-green-500" />}
                    {file.status === "error" && <AlertCircleIcon className="h-4 w-4 text-destructive" />}
                    <CardTitle className="text-sm font-medium truncate">{file.name}</CardTitle>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveFile(file.id);
                    }}
                  >
                    <TrashIcon className="h-4 w-4" />
                  </Button>
                </div>
                <CardDescription className="text-xs">
                  {formatFileSize(file.size)}
                </CardDescription>
              </CardHeader>

              <CardContent className="py-2 px-4">
                {file.status === "uploading" && (
                  <Progress value={file.uploadProgress} className="h-1 mb-2" />
                )}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor={`document-type-${file.id}`}>Document Type</Label>
                    <Select
                      value={file.documentType}
                      onValueChange={(value) => handleDocumentTypeChange(file.id, value)}
                      disabled={file.status === "uploading" || file.status === "success"}
                    >
                      <SelectTrigger id={`document-type-${file.id}`}>
                        <SelectValue placeholder="Select document type" />
                      </SelectTrigger>
                      <SelectContent>
                        {DOCUMENT_TYPES.map((type) => (
                          <SelectItem key={type.id} value={type.id}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`description-${file.id}`}>Description (Optional)</Label>
                    <Input
                      id={`description-${file.id}`}
                      placeholder="Brief description of the document"
                      value={file.description}
                      onChange={(e) => handleDescriptionChange(file.id, e.target.value)}
                      disabled={file.status === "uploading" || file.status === "success"}
                    />
                  </div>
                </div>
                {file.status === "error" && (
                  <p className="text-xs text-destructive mt-2">{file.errorMessage}</p>
                )}
              </CardContent>
            </Card>
          ))}

          <div className="flex justify-end">
            <Button
              onClick={handleUpload}
              disabled={!files.length || files.every(f => f.status === "success" || f.status === "uploading")}
            >
              {files.some(f => f.status === "uploading") && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Upload Documents
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
