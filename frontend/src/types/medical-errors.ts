/**
 * Medical Error Type Hierarchy
 * 
 * Comprehensive error type definitions for medical command center,
 * providing strong typing for error handling and debugging.
 */

// ============================================================================
// Base Error Classes
// ============================================================================

/**
 * Base abstract class for all medical-related errors
 */
export abstract class MedicalError extends Error {
  /** Error code for programmatic identification */
  abstract readonly code: string;
  
  /** Error severity level */
  abstract readonly severity: 'low' | 'medium' | 'high' | 'critical';
  
  /** Timestamp when error occurred */
  readonly timestamp: number;
  
  /** Additional context information */
  readonly context?: Record<string, unknown>;
  
  /** Whether error should be reported to monitoring */
  readonly reportable: boolean;
  
  constructor(
    message: string, 
    context?: Record<string, unknown>,
    reportable = true
  ) {
    super(message);
    this.name = this.constructor.name;
    this.timestamp = Date.now();
    this.context = context;
    this.reportable = reportable;
    
    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
  
  /**
   * Convert error to JSON for logging/reporting
   */
  toJSON(): MedicalErrorSerialized {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      severity: this.severity,
      timestamp: this.timestamp,
      context: this.context,
      stack: this.stack,
      reportable: this.reportable
    };
  }
}

/**
 * Serialized error format for logging and transmission
 */
export interface MedicalErrorSerialized {
  name: string;
  message: string;
  code: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: number;
  context?: Record<string, unknown>;
  stack?: string;
  reportable: boolean;
}

// ============================================================================
// Data Processing Errors
// ============================================================================

/**
 * Error during medical document processing
 */
export class ProcessingError extends MedicalError {
  readonly code: string = 'PROCESSING_ERROR';
  readonly severity = 'high' as const;
  
  constructor(
    message: string,
    public readonly stage?: 'upload' | 'ocr' | 'nlp' | 'extraction' | 'storage',
    context?: Record<string, unknown>
  ) {
    super(message, { ...context, stage });
  }
}

/**
 * Error during document OCR processing
 */
export class OCRProcessingError extends ProcessingError {
  readonly code = 'OCR_PROCESSING_ERROR';
  
  constructor(
    message: string,
    public readonly documentId?: string,
    context?: Record<string, unknown>
  ) {
    super(message, 'ocr', { ...context, documentId });
  }
}

/**
 * Error during NLP entity extraction
 */
export class NLPExtractionError extends ProcessingError {
  readonly code = 'NLP_EXTRACTION_ERROR';
  
  constructor(
    message: string,
    public readonly entityType?: string,
    context?: Record<string, unknown>
  ) {
    super(message, 'nlp', { ...context, entityType });
  }
}

/**
 * Error during LangExtract processing
 */
export class LangExtractError extends ProcessingError {
  readonly code = 'LANGEXTRACT_ERROR';
  
  constructor(
    message: string,
    public readonly taskType?: string,
    context?: Record<string, unknown>
  ) {
    super(message, 'extraction', { ...context, taskType });
  }
}

// ============================================================================
// Database and Storage Errors
// ============================================================================

/**
 * Error during database operations
 */
export class DatabaseError extends MedicalError {
  readonly code: string = 'DATABASE_ERROR';
  readonly severity: 'low' | 'medium' | 'high' | 'critical' = 'high';
  
  constructor(
    message: string,
    public readonly operation?: 'select' | 'insert' | 'update' | 'delete' | 'rpc',
    public readonly table?: string,
    context?: Record<string, unknown>
  ) {
    super(message, { ...context, operation, table });
  }
}

/**
 * Error due to tenant isolation violations
 */
export class TenantIsolationError extends DatabaseError {
  readonly code = 'TENANT_ISOLATION_ERROR';
  readonly severity = 'critical' as const;
  
  constructor(
    message: string,
    public readonly tenantId?: string,
    public readonly attemptedAccess?: string,
    context?: Record<string, unknown>
  ) {
    super(message, undefined, undefined, { ...context, tenantId, attemptedAccess });
  }
}

/**
 * Error during data validation
 */
export class DataValidationError extends MedicalError {
  readonly code = 'DATA_VALIDATION_ERROR';
  readonly severity = 'medium' as const;
  
  constructor(
    message: string,
    public readonly field?: string,
    public readonly expectedType?: string,
    public readonly actualValue?: unknown,
    context?: Record<string, unknown>
  ) {
    super(message, { ...context, field, expectedType, actualValue });
  }
}

// ============================================================================
// HIPAA and Compliance Errors
// ============================================================================

/**
 * Error related to HIPAA compliance violations
 */
export class HIPAAViolationError extends MedicalError {
  readonly code: string = 'HIPAA_VIOLATION_ERROR';
  readonly severity = 'critical' as const;
  
  constructor(
    message: string,
    public readonly violationType?: 'unauthorized_access' | 'data_exposure' | 'audit_failure' | 'encryption_failure',
    public readonly phiElements?: string[],
    context?: Record<string, unknown>
  ) {
    super(message, { ...context, violationType, phiElements });
  }
}

/**
 * Error during audit logging
 */
export class AuditLogError extends HIPAAViolationError {
  readonly code = 'AUDIT_LOG_ERROR';
  
  constructor(
    message: string,
    public readonly userId?: string,
    public readonly action?: string,
    context?: Record<string, unknown>
  ) {
    super(message, 'audit_failure', undefined, { ...context, userId, action });
  }
}

/**
 * Error due to insufficient permissions
 */
export class PermissionError extends MedicalError {
  readonly code = 'PERMISSION_ERROR';
  readonly severity = 'high' as const;
  
  constructor(
    message: string,
    public readonly requiredRole?: string,
    public readonly userRole?: string,
    public readonly resource?: string,
    context?: Record<string, unknown>
  ) {
    super(message, { ...context, requiredRole, userRole, resource });
  }
}

// ============================================================================
// AI and Integration Errors
// ============================================================================

/**
 * Error during AI processing
 */
export class AIProcessingError extends MedicalError {
  readonly code = 'AI_PROCESSING_ERROR';
  readonly severity = 'medium' as const;
  
  constructor(
    message: string,
    public readonly aiService?: 'document_ai' | 'healthcare_nlp' | 'medgemma' | 'langextract',
    public readonly modelVersion?: string,
    context?: Record<string, unknown>
  ) {
    super(message, { ...context, aiService, modelVersion });
  }
}

/**
 * Error with AG-UI protocol integration
 */
export class AGUIProtocolError extends MedicalError {
  readonly code = 'AGUI_PROTOCOL_ERROR';
  readonly severity = 'medium' as const;
  
  constructor(
    message: string,
    public readonly eventType?: string,
    public readonly protocolVersion?: string,
    context?: Record<string, unknown>
  ) {
    super(message, { ...context, eventType, protocolVersion });
  }
}

/**
 * Error during ResilientChat integration
 */
export class ChatIntegrationError extends MedicalError {
  readonly code = 'CHAT_INTEGRATION_ERROR';
  readonly severity = 'medium' as const;
  
  constructor(
    message: string,
    public readonly messageId?: string,
    public readonly sessionId?: string,
    context?: Record<string, unknown>
  ) {
    super(message, { ...context, messageId, sessionId });
  }
}

// ============================================================================
// Network and Service Errors
// ============================================================================

/**
 * Error during network operations
 */
export class NetworkError extends MedicalError {
  readonly code: string = 'NETWORK_ERROR';
  readonly severity: 'low' | 'medium' | 'high' | 'critical' = 'medium';
  
  constructor(
    message: string,
    public readonly statusCode?: number,
    public readonly endpoint?: string,
    context?: Record<string, unknown>
  ) {
    super(message, { ...context, statusCode, endpoint });
  }
}

/**
 * Error due to request timeout
 */
export class TimeoutError extends NetworkError {
  readonly code = 'TIMEOUT_ERROR';
  
  constructor(
    message: string,
    public readonly timeoutMs?: number,
    endpoint?: string,
    context?: Record<string, unknown>
  ) {
    super(message, undefined, endpoint, { ...context, timeoutMs });
  }
}

/**
 * Error due to rate limiting
 */
export class RateLimitError extends NetworkError {
  readonly code = 'RATE_LIMIT_ERROR';
  readonly severity = 'low' as const;
  
  constructor(
    message: string,
    public readonly retryAfterMs?: number,
    endpoint?: string,
    context?: Record<string, unknown>
  ) {
    super(message, 429, endpoint, { ...context, retryAfterMs });
  }
}

// ============================================================================
// Configuration and Setup Errors
// ============================================================================

/**
 * Error due to missing or invalid configuration
 */
export class ConfigurationError extends MedicalError {
  readonly code: string = 'CONFIGURATION_ERROR';
  readonly severity: 'low' | 'medium' | 'high' | 'critical' = 'high';
  
  constructor(
    message: string,
    public readonly configKey?: string,
    public readonly expectedValue?: string,
    context?: Record<string, unknown>
  ) {
    super(message, { ...context, configKey, expectedValue }, false);
  }
}

/**
 * Error when required feature is not enabled
 */
export class FeatureNotEnabledError extends ConfigurationError {
  readonly code = 'FEATURE_NOT_ENABLED_ERROR';
  readonly severity = 'medium' as const;
  
  constructor(
    message: string,
    public readonly featureName?: string,
    public readonly featureFlag?: string,
    context?: Record<string, unknown>
  ) {
    super(message, featureFlag, 'true', { ...context, featureName });
  }
}

// ============================================================================
// User Interface Errors
// ============================================================================

/**
 * Error in user interface components
 */
export class UIError extends MedicalError {
  readonly code: string = 'UI_ERROR';
  readonly severity = 'low' as const;
  
  constructor(
    message: string,
    public readonly component?: string,
    public readonly userAction?: string,
    context?: Record<string, unknown>
  ) {
    super(message, { ...context, component, userAction });
  }
}

/**
 * Error during form validation
 */
export class FormValidationError extends UIError {
  readonly code = 'FORM_VALIDATION_ERROR';
  
  constructor(
    message: string,
    public readonly fieldName?: string,
    public readonly validationRule?: string,
    context?: Record<string, unknown>
  ) {
    super(message, undefined, 'form_submission', { ...context, fieldName, validationRule });
  }
}

// ============================================================================
// Error Factory and Utilities
// ============================================================================

/**
 * Factory for creating medical errors from unknown error objects
 */
export class MedicalErrorFactory {
  /**
   * Create a medical error from an unknown error
   */
  static fromError(error: unknown, context?: Record<string, unknown>): MedicalError {
    if (error instanceof MedicalError) {
      return error;
    }
    
    if (error instanceof Error) {
      return new ProcessingError(
        error.message,
        undefined,
        { ...context, originalError: error.name, stack: error.stack }
      );
    }
    
    if (typeof error === 'string') {
      return new ProcessingError(error, undefined, context);
    }
    
    return new ProcessingError(
      'Unknown error occurred',
      undefined,
      { ...context, originalError: error }
    );
  }
  
  /**
   * Create error from Supabase error
   */
  static fromSupabaseError(
    error: { message: string; code?: string; details?: string },
    operation?: string,
    table?: string
  ): DatabaseError {
    return new DatabaseError(
      error.message,
      operation as any,
      table,
      { supabaseCode: error.code, supabaseDetails: error.details }
    );
  }
  
  /**
   * Create error from HTTP response
   */
  static fromHttpError(
    statusCode: number,
    message: string,
    endpoint: string,
    context?: Record<string, unknown>
  ): NetworkError {
    if (statusCode === 429) {
      return new RateLimitError(message, undefined, endpoint, context);
    }
    
    if (statusCode >= 500) {
      return new NetworkError(message, statusCode, endpoint, context);
    }
    
    return new NetworkError(message, statusCode, endpoint, context);
  }
}

/**
 * Error reporter interface for logging and monitoring
 */
export interface ErrorReporter {
  reportError(error: MedicalError): void;
  reportBatch(errors: MedicalError[]): void;
}

/**
 * Error handler utility functions
 */
export class MedicalErrorHandler {
  /**
   * Determine if error is recoverable
   */
  static isRecoverable(error: MedicalError): boolean {
    return error.severity !== 'critical' && 
           !(error instanceof HIPAAViolationError) &&
           !(error instanceof TenantIsolationError);
  }
  
  /**
   * Determine if error should trigger retry
   */
  static shouldRetry(error: MedicalError, retryCount: number): boolean {
    if (retryCount >= 3) return false;
    
    return error instanceof NetworkError || 
           error instanceof TimeoutError ||
           (error instanceof DatabaseError && error.operation !== 'insert');
  }
  
  /**
   * Get user-friendly error message
   */
  static getUserMessage(error: MedicalError): string {
    switch (error.constructor) {
      case ProcessingError:
        return 'There was an issue processing your medical records. Please try again.';
      case NetworkError:
        return 'Network connection issue. Please check your connection and try again.';
      case PermissionError:
        return 'You do not have permission to perform this action.';
      case HIPAAViolationError:
        return 'A security issue was detected. Please contact support immediately.';
      case FeatureNotEnabledError:
        return 'This feature is currently not available. Please contact support.';
      default:
        return 'An unexpected error occurred. Please try again or contact support.';
    }
  }
  
  /**
   * Get error severity color for UI display
   */
  static getSeverityColor(error: MedicalError): string {
    switch (error.severity) {
      case 'low': return 'text-yellow-600';
      case 'medium': return 'text-orange-600';
      case 'high': return 'text-red-600';
      case 'critical': return 'text-red-800';
    }
  }
}

// ============================================================================
// Type Guards
// ============================================================================

/**
 * Type guard for medical error
 */
export function isMedicalError(error: unknown): error is MedicalError {
  return error instanceof MedicalError;
}

/**
 * Type guard for HIPAA violation error
 */
export function isHIPAAViolationError(error: unknown): error is HIPAAViolationError {
  return error instanceof HIPAAViolationError;
}

/**
 * Type guard for permission error
 */
export function isPermissionError(error: unknown): error is PermissionError {
  return error instanceof PermissionError;
}

/**
 * Type guard for network error
 */
export function isNetworkError(error: unknown): error is NetworkError {
  return error instanceof NetworkError;
}

/**
 * Type guard for configuration error
 */
export function isConfigurationError(error: unknown): error is ConfigurationError {
  return error instanceof ConfigurationError;
}

// All error types are already exported individually above