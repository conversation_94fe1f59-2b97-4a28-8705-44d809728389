// Type definitions for Jest extended matchers
import 'jest';

declare global {
  namespace jest {
    interface Matchers<R> {
      // Basic matchers
      toBe(expected: unknown): R;
      toBeCloseTo(expected: number, precision?: number): R;
      toBeDefined(): R;
      toBeFalsy(): R;
      toBeGreaterThan(expected: number | bigint): R;
      toBeGreaterThanOrEqual(expected: number | bigint): R;
      toBeInstanceOf(expected: unknown): R;
      toBeLessThan(expected: number | bigint): R;
      toBeLessThanOrEqual(expected: number | bigint): R;
      toBeNaN(): R;
      toBeNull(): R;
      toBeTruthy(): R;
      toBeUndefined(): R;
      toContain(expected: unknown): R;
      toContainEqual(expected: unknown): R;
      toEqual(expected: unknown): R;
      toHaveLength(expected: number): R;
      toHaveProperty(keyPath: string | string[], value?: unknown): R;
      toMatch(expected: string | RegExp): R;
      toMatchObject(expected: object | object[]): R;
      toStrictEqual(expected: unknown): R;
      toThrow(expected?: string | Error | RegExp): R;
      toThrowError(expected?: string | Error | RegExp): R;

      // DOM Testing Library matchers
      toBeInTheDocument(): R;
      toBeVisible(): R;
      toBeDisabled(): R;
      toBeEnabled(): R;
      toBeEmpty(): R;
      toBeInvalid(): R;
      toBeRequired(): R;
      toBeValid(): R;
      toBeChecked(): R;
      toHaveAttribute(attr: string, value?: unknown): R;
      toHaveClass(className: string): R;
      toHaveFocus(): R;
      toHaveFormValues(expectedValues: Record<string, unknown>): R;
      toHaveStyle(css: Record<string, unknown>): R;
      toHaveTextContent(text: string | RegExp): R;
      toHaveValue(value: unknown): R;

      // Jest-specific matchers
      toHaveBeenCalled(): R;
      toHaveBeenCalledTimes(expected: number): R;
      toHaveBeenCalledWith(...args: unknown[]): R;
      toHaveBeenLastCalledWith(...args: unknown[]): R;
      toHaveBeenNthCalledWith(nthCall: number, ...args: unknown[]): R;
      toHaveReturned(): R;
      toHaveReturnedTimes(expected: number): R;
      toHaveReturnedWith(expected: unknown): R;
      toHaveLastReturnedWith(expected: unknown): R;
      toHaveNthReturnedWith(nthCall: number, expected: unknown): R;

      // Promise matchers
      resolves: Matchers<Promise<R>>;
      rejects: Matchers<Promise<R>>;
    }
  }

  // Extend the Jest namespace for expect helpers
  namespace jest {
    interface Expect {
      // Asymmetric matchers
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      any(expectedType?: any): any;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      anything(): any;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      arrayContaining(sample: any[]): any;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      objectContaining(sample: Record<string, unknown>): any;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      stringContaining(expected: string): any;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      stringMatching(expected: string | RegExp): any;
      not: Expect;
    }
  }

  // Extend the global Jest functions
  interface JestMockFunctions {
    requireActual: (moduleName: string) => unknown;
    requireMock: (moduleName: string) => unknown;
    spyOn: (object: unknown, methodName: string) => jest.SpyInstance;
    clearAllMocks: () => void;
    resetAllMocks: () => void;
    restoreAllMocks: () => void;
    resetModules: () => void;
    isolateModules: (fn: () => void) => void;
    mock: (moduleName: string, factory?: unknown, options?: unknown) => void;
    unmock: (moduleName: string) => void;
    doMock: (moduleName: string, factory?: unknown, options?: unknown) => void;
    dontMock: (moduleName: string) => void;
    setMock: (moduleName: string, moduleExports: unknown) => void;
    deepUnmock: (moduleName: string) => void;
    createMockFromModule: (moduleName: string) => unknown;
  }

  // Extend the Jest Mock interface
  interface Mock<T = unknown, Y extends unknown[] = unknown[]> {
    mockClear(): this;
    mockReset(): this;
    mockRestore(): this;
    mockImplementation(fn: (...args: Y) => T): this;
    mockImplementationOnce(fn: (...args: Y) => T): this;
    mockReturnThis(): this;
    mockReturnValue(value: T): this;
    mockReturnValueOnce(value: T): this;
    mockResolvedValue(value: Awaited<T>): this;
    mockResolvedValueOnce(value: Awaited<T>): this;
    mockRejectedValue(value: unknown): this;
    mockRejectedValueOnce(value: unknown): this;
    getMockName(): string;
    mockName(name: string): this;
    mockReturnThis(): this;
  }

  // Extend the global Jest object
  interface Jest extends JestMockFunctions {
    fn<T = unknown, Y extends unknown[] = unknown[]>(): Mock<T, Y>;
    fn<T = unknown, Y extends unknown[] = unknown[]>(implementation: (...args: Y) => T): Mock<T, Y>;
  }

  // We don't need to declare global Jest variables here as they're already declared in @types/jest
}

// Extend the SpyInstance interface
declare global {
  namespace jest {
    interface SpyInstance<T = unknown, Y extends unknown[] = unknown[]> extends Mock<T, Y> {
      mockClear(): this;
      mockReset(): this;
      mockRestore(): this;
    }
  }
}

export {};
