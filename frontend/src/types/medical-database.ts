/**
 * Medical Database Type Definitions
 * 
 * Comprehensive type definitions for all medical database operations,
 * eliminating 'any' types and providing strong typing for service layer.
 */

import type { EvidenceLink } from './medical-records';

// ============================================================================
// Database Operation Parameter Types
// ============================================================================

/**
 * Parameters for storing structured extractions via database RPC function
 */
export interface StoredExtractionParams {
  p_tenant_id: string;
  p_matter_id: string;
  p_medical_record_id: string;
  p_task_type: 'medications' | 'radiology' | 'problems' | 'followups';
  p_extractions_data: Record<string, unknown>;
  p_confidence_score: number;
  p_review_html_uri?: string;
}

/**
 * Response from stored extraction RPC function
 */
export interface StoredExtractionResponse {
  extraction_id: string;
  created_at: string;
}

/**
 * Parameters for retrieving extractions for a matter
 */
export interface GetExtractionsParams {
  p_matter_id: string;
}

/**
 * Parameters for creating follow-up tasks
 */
export interface CreateFollowupTasksParams {
  p_matter_id: string;
}

// ============================================================================
// Database Row Types
// ============================================================================

/**
 * Raw medication row data from database
 */
export interface MedicationRowData {
  id: string;
  tenant_id: string;
  extraction_id: string;
  drug: string;
  dose: string | null;
  route: string | null;
  frequency: string | null;
  start_date: string | null;
  end_date: string | null;
  evidence: EvidenceLink[];
  confidence: number;
  created_at: string;
  updated_at: string;
}

/**
 * Parameters for inserting medication rows
 */
export interface InsertMedicationRowParams {
  tenant_id: string;
  extraction_id: string;
  drug: string;
  dose: string | null;
  route: string | null;
  frequency: string | null;
  start_date: string | null;
  end_date: string | null;
  evidence: EvidenceLink[];
  confidence: number;
}

/**
 * Raw radiology row data from database
 */
export interface RadiologyRowData {
  id: string;
  tenant_id: string;
  extraction_id: string;
  study: string;
  study_date: string | null;
  body_part: string | null;
  finding: string | null;
  impression: string | null;
  evidence: EvidenceLink[];
  confidence: number;
  created_at: string;
  updated_at: string;
}

/**
 * Parameters for inserting radiology rows
 */
export interface InsertRadiologyRowParams {
  tenant_id: string;
  extraction_id: string;
  study: string;
  study_date: string | null;
  body_part: string | null;
  finding: string | null;
  impression: string | null;
  evidence: EvidenceLink[];
  confidence: number;
}

/**
 * Raw problem row data from database
 */
export interface ProblemRowData {
  id: string;
  tenant_id: string;
  extraction_id: string;
  term: string;
  evidence: EvidenceLink[];
  confidence: number;
  created_at: string;
  updated_at: string;
}

/**
 * Parameters for inserting problem rows
 */
export interface InsertProblemRowParams {
  tenant_id: string;
  extraction_id: string;
  term: string;
  evidence: EvidenceLink[];
  confidence: number;
}

/**
 * Raw follow-up cue data from database
 */
export interface FollowupCueRowData {
  id: string;
  tenant_id: string;
  extraction_id: string;
  cue: string;
  target: string | null;
  due_date_guess: string | null;
  evidence: EvidenceLink[];
  confidence: number;
  followup_task_created: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Parameters for inserting follow-up cue rows
 */
export interface InsertFollowupCueParams {
  tenant_id: string;
  extraction_id: string;
  cue: string;
  target: string | null;
  due_date_guess: string | null;
  evidence: EvidenceLink[];
  confidence: number;
  followup_task_created: boolean;
}

/**
 * Raw structured extraction data from database
 */
export interface StructuredExtractionRowData {
  id: string;
  tenant_id: string;
  matter_id: string;
  medical_record_id: string;
  task_type: string;
  extractions_data: Record<string, unknown>;
  confidence_score: number;
  review_html_uri: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Medical record row data from database
 */
export interface MedicalRecordRowData {
  id: string;
  tenant_id: string;
  matter_id: string;
  case_document_id: string;
  provider_name: string | null;
  service_date: string | null;
  document_type: string | null;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  extraction_confidence: number | null;
  created_at: string;
  updated_at: string;
}

// ============================================================================
// Supabase Query Response Types
// ============================================================================

/**
 * Supabase single row response
 */
export interface SupabaseSingleResponse<T> {
  data: T | null;
  error: SupabaseError | null;
}

/**
 * Supabase multiple rows response
 */
export interface SupabaseMultiResponse<T> {
  data: T[] | null;
  error: SupabaseError | null;
}

/**
 * Supabase RPC function response
 */
export interface SupabaseRpcResponse<T> {
  data: T | null;
  error: SupabaseError | null;
}

/**
 * Supabase error structure
 */
export interface SupabaseError {
  message: string;
  details: string | null;
  hint: string | null;
  code: string;
}

// ============================================================================
// Joined Query Response Types
// ============================================================================

/**
 * Medication row with extraction metadata
 */
export interface MedicationWithExtractionData extends MedicationRowData {
  structured_extractions: {
    matter_id: string;
    confidence_score: number;
  };
}

/**
 * Radiology row with extraction metadata
 */
export interface RadiologyWithExtractionData extends RadiologyRowData {
  structured_extractions: {
    matter_id: string;
    confidence_score: number;
  };
}

/**
 * Problem row with extraction metadata
 */
export interface ProblemWithExtractionData extends ProblemRowData {
  structured_extractions: {
    matter_id: string;
    confidence_score: number;
  };
}

/**
 * Follow-up cue with extraction metadata
 */
export interface FollowupWithExtractionData extends FollowupCueRowData {
  structured_extractions: {
    matter_id: string;
    confidence_score: number;
  };
}

// ============================================================================
// Type Guards for Runtime Validation
// ============================================================================

/**
 * Type guard for medication row data
 */
export function isMedicationRowData(data: unknown): data is MedicationRowData {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).id === 'string' &&
    typeof (data as any).drug === 'string' &&
    typeof (data as any).tenant_id === 'string' &&
    Array.isArray((data as any).evidence)
  );
}

/**
 * Type guard for radiology row data
 */
export function isRadiologyRowData(data: unknown): data is RadiologyRowData {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).id === 'string' &&
    typeof (data as any).study === 'string' &&
    typeof (data as any).tenant_id === 'string' &&
    Array.isArray((data as any).evidence)
  );
}

/**
 * Type guard for problem row data
 */
export function isProblemRowData(data: unknown): data is ProblemRowData {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).id === 'string' &&
    typeof (data as any).term === 'string' &&
    typeof (data as any).tenant_id === 'string' &&
    Array.isArray((data as any).evidence)
  );
}

/**
 * Type guard for follow-up cue data
 */
export function isFollowupCueRowData(data: unknown): data is FollowupCueRowData {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).id === 'string' &&
    typeof (data as any).cue === 'string' &&
    typeof (data as any).tenant_id === 'string' &&
    Array.isArray((data as any).evidence)
  );
}

/**
 * Type guard for Supabase error
 */
export function isSupabaseError(error: unknown): error is SupabaseError {
  return (
    typeof error === 'object' &&
    error !== null &&
    typeof (error as any).message === 'string' &&
    typeof (error as any).code === 'string'
  );
}

/**
 * Type guard for extraction response validation
 */
export function isValidExtractionResponse(data: unknown): data is StoredExtractionResponse {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).extraction_id === 'string' &&
    typeof (data as any).created_at === 'string'
  );
}

// ============================================================================
// Utility Types
// ============================================================================

/**
 * Extract tenant ID from user profile
 */
export interface UserTenantProfile {
  tenant_id: string;
  id: string;
}

/**
 * Type guard for user tenant profile
 */
export function isUserTenantProfile(data: unknown): data is UserTenantProfile {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).tenant_id === 'string' &&
    typeof (data as any).id === 'string'
  );
}

/**
 * Database transaction result
 */
export interface DatabaseTransactionResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  affectedRows?: number;
}

/**
 * Batch insert result
 */
export interface BatchInsertResult {
  inserted: number;
  failed: number;
  errors: string[];
}

/**
 * Processing statistics
 */
export interface ProcessingStatistics {
  totalDocuments: number;
  processedDocuments: number;
  failedDocuments: number;
  averageConfidence: number;
  processingTime: number;
  extractedEntities: {
    medications: number;
    radiology: number;
    problems: number;
    followups: number;
  };
}

// ============================================================================
// Export utility functions
// ============================================================================

/**
 * Validate extraction data structure
 */
export function validateExtractionData(data: unknown): data is Record<string, unknown> {
  return typeof data === 'object' && data !== null;
}

/**
 * Create safe database query options
 */
export interface SafeDatabaseOptions {
  timeout?: number;
  retries?: number;
  validateResponse?: boolean;
}

/**
 * Default database options
 */
export const DEFAULT_DB_OPTIONS: Required<SafeDatabaseOptions> = {
  timeout: 30000, // 30 seconds
  retries: 3,
  validateResponse: true
};