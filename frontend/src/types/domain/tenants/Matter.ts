/**
 * Domain model for Matters
 * This is the clean, frontend-friendly model that all components should use
 * Matters represent legal work that can be litigation, transactional, advisory, or IP-related
 */

/**
 * Enumeration of specific practice areas
 */
export enum PracticeArea {
  // Litigation areas
  PERSONAL_INJURY = 'personal_injury',
  FAMILY_LAW = 'family_law',
  CRIMINAL_DEFENSE = 'criminal_defense',
  CIVIL_LITIGATION = 'civil_litigation',
  EMPLOYMENT_LAW = 'employment_law',

  // Transactional areas
  CORPORATE_BUSINESS = 'corporate_business',
  REAL_ESTATE = 'real_estate',

  // Advisory areas
  ESTATE_PLANNING = 'estate_planning',
  TAX = 'tax',
  IMMIGRATION = 'immigration',
  ELDER_LAW = 'elder_law',
  ADMINISTRATIVE_REG = 'administrative_reg',
  ENVIRONMENTAL = 'environmental',

  // IP areas
  INTELLECTUAL_PROPERTY = 'intellectual_property',

  // Other
  BANKRUPTCY = 'bankruptcy'
}

/**
 * Enumeration of work types (how the work is handled)
 */
export enum WorkType {
  LITIGATION = 'litigation',
  TRANSACTIONAL = 'transactional',
  ADVISORY = 'advisory',
  ADR = 'adr'
}

/**
 * Enumeration of possible matter statuses
 */
export enum MatterStatus {
  ACTIVE = 'active',
  PENDING = 'pending',
  CLOSED = 'closed',
  REJECTED = 'rejected',
  ON_HOLD = 'on_hold',
  SETTLED = 'settled',
  ARCHIVED = 'archived'
}

/**
 * Enumeration of possible matter priority levels
 */
export enum MatterPriorityLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

/**
 * Core Matter interface
 */
export interface Matter {
  id: string;
  tenantId: string;
  title: string;
  description: string | null;
  status: MatterStatus;
  practiceArea: PracticeArea;
  workType: WorkType;
  displayLabel: string; // Computed: "Case" for litigation work type, "Matter" for others
  sensitive: boolean;
  caseNumber: string | null;
  courtName: string | null;
  jurisdiction: string | null;
  filingDate: string | null;
  trialDate: string | null;
  primaryAttorneyId: string | null;
  priorityLevel: string | null;
  statueOfLimitations: string | null;
  statuteOfLimitations: string | null; // Alternative spelling for medical branch compatibility
  settlementAuthority: number | null; // Settlement authority amount for medical cases
  rejectionReason: string | null;
  createdBy: string;
  createdAt: string;
  updatedBy: string | null;
  updatedAt: string | null;

  // Joined data
  primaryAttorney?: {
    id: string;
    fullName?: string | null;
    email?: string | null;
  } | null;

  clients?: {
    id: string;
    fullName: string;
    email?: string | null;
    phone?: string | null; // Client phone number for medical branch compatibility
    address?: string | null; // Client address for medical branch compatibility
  }[];

  // Counts for related items
  documentCount?: number;
  deadlineCount?: number;
  noteCount?: number;
}

/**
 * Input data for creating or updating a matter
 */
export interface MatterInput {
  title: string;
  description?: string | null;
  status?: MatterStatus | null;
  practiceArea: PracticeArea;
  workType: WorkType;
  sensitive?: boolean;
  caseNumber?: string | null;
  courtName?: string | null;
  jurisdiction?: string | null;
  filingDate?: string | null;
  trialDate?: string | null;
  primaryAttorneyId?: string | null;
  priorityLevel?: string | null;
  statueOfLimitations?: string | null;
  rejectionReason?: string | null;
  clientIds?: string[];
}

/**
 * Metadata for matter-related interactions
 */
export interface MatterMetadata {
  lastViewed?: string;
  lastActivity?: string;
  activityCount?: number;
  tags?: string[];
  customFields?: Record<string, unknown>;
}

/**
 * Helper function to get display label based on work type
 */
export function getMatterDisplayLabel(workType: WorkType): string {
  return workType === WorkType.LITIGATION ? 'Case' : 'Matter';
}

/**
 * Helper function to check if a matter is litigation
 */
export function isLitigationMatter(matter: Matter): boolean {
  return matter.workType === WorkType.LITIGATION;
}

/**
 * Helper function to get practice area display name
 */
export function getPracticeAreaDisplayName(practiceArea: PracticeArea): string {
  switch (practiceArea) {
    case PracticeArea.PERSONAL_INJURY:
      return 'Personal Injury';
    case PracticeArea.FAMILY_LAW:
      return 'Family Law';
    case PracticeArea.CRIMINAL_DEFENSE:
      return 'Criminal Defense';
    case PracticeArea.CIVIL_LITIGATION:
      return 'Civil Litigation';
    case PracticeArea.EMPLOYMENT_LAW:
      return 'Employment Law';
    case PracticeArea.CORPORATE_BUSINESS:
      return 'Corporate & Business';
    case PracticeArea.REAL_ESTATE:
      return 'Real Estate';
    case PracticeArea.ESTATE_PLANNING:
      return 'Estate Planning';
    case PracticeArea.TAX:
      return 'Tax';
    case PracticeArea.IMMIGRATION:
      return 'Immigration';
    case PracticeArea.ELDER_LAW:
      return 'Elder Law';
    case PracticeArea.ADMINISTRATIVE_REG:
      return 'Administrative & Regulatory';
    case PracticeArea.ENVIRONMENTAL:
      return 'Environmental';
    case PracticeArea.INTELLECTUAL_PROPERTY:
      return 'Intellectual Property';
    case PracticeArea.BANKRUPTCY:
      return 'Bankruptcy';
    default:
      return 'Unknown';
  }
}

/**
 * Helper function to get work type display name
 */
export function getWorkTypeDisplayName(workType: WorkType): string {
  switch (workType) {
    case WorkType.LITIGATION:
      return 'Litigation';
    case WorkType.TRANSACTIONAL:
      return 'Transactional';
    case WorkType.ADVISORY:
      return 'Advisory';
    case WorkType.ADR:
      return 'Alternative Dispute Resolution';
    default:
      return 'Unknown';
  }
}

/**
 * Helper function to get status display name
 */
export function getMatterStatusDisplayName(status: MatterStatus): string {
  switch (status) {
    case MatterStatus.ACTIVE:
      return 'Active';
    case MatterStatus.PENDING:
      return 'Pending';
    case MatterStatus.CLOSED:
      return 'Closed';
    case MatterStatus.REJECTED:
      return 'Rejected';
    case MatterStatus.ON_HOLD:
      return 'On Hold';
    case MatterStatus.SETTLED:
      return 'Settled';
    case MatterStatus.ARCHIVED:
      return 'Archived';
    default:
      return 'Unknown';
  }
}

/**
 * Helper function to get priority level display name
 */
export function getPriorityDisplayName(priority: MatterPriorityLevel): string {
  switch (priority) {
    case MatterPriorityLevel.LOW:
      return 'Low';
    case MatterPriorityLevel.MEDIUM:
      return 'Medium';
    case MatterPriorityLevel.HIGH:
      return 'High';
    case MatterPriorityLevel.URGENT:
      return 'Urgent';
    default:
      return 'Medium';
  }
}

/**
 * Helper function to get practice area color for UI
 */
export function getPracticeAreaColor(practiceArea: PracticeArea): string {
  switch (practiceArea) {
    // Litigation areas - red tones
    case PracticeArea.PERSONAL_INJURY:
      return 'bg-red-100 text-red-800';
    case PracticeArea.CIVIL_LITIGATION:
      return 'bg-red-200 text-red-900';
    case PracticeArea.CRIMINAL_DEFENSE:
      return 'bg-rose-100 text-rose-800';
    case PracticeArea.EMPLOYMENT_LAW:
      return 'bg-pink-100 text-pink-800';

    // Transactional areas - blue tones
    case PracticeArea.CORPORATE_BUSINESS:
      return 'bg-blue-100 text-blue-800';
    case PracticeArea.REAL_ESTATE:
      return 'bg-sky-100 text-sky-800';

    // Advisory areas - green tones
    case PracticeArea.ESTATE_PLANNING:
      return 'bg-green-100 text-green-800';
    case PracticeArea.TAX:
      return 'bg-emerald-100 text-emerald-800';
    case PracticeArea.IMMIGRATION:
      return 'bg-teal-100 text-teal-800';
    case PracticeArea.ELDER_LAW:
      return 'bg-lime-100 text-lime-800';
    case PracticeArea.ADMINISTRATIVE_REG:
      return 'bg-green-200 text-green-900';
    case PracticeArea.ENVIRONMENTAL:
      return 'bg-green-300 text-green-900';

    // Family law - orange
    case PracticeArea.FAMILY_LAW:
      return 'bg-orange-100 text-orange-800';

    // IP - purple
    case PracticeArea.INTELLECTUAL_PROPERTY:
      return 'bg-purple-100 text-purple-800';

    // Bankruptcy - yellow
    case PracticeArea.BANKRUPTCY:
      return 'bg-yellow-100 text-yellow-800';

    default:
      return 'bg-gray-100 text-gray-800';
  }
}

/**
 * Helper function to get work type color for UI
 */
export function getWorkTypeColor(workType: WorkType): string {
  switch (workType) {
    case WorkType.LITIGATION:
      return 'bg-red-100 text-red-800';
    case WorkType.TRANSACTIONAL:
      return 'bg-blue-100 text-blue-800';
    case WorkType.ADVISORY:
      return 'bg-green-100 text-green-800';
    case WorkType.ADR:
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

/**
 * Helper function to get status color for UI
 */
export function getMatterStatusColor(status: MatterStatus): string {
  switch (status) {
    case MatterStatus.ACTIVE:
      return 'bg-green-100 text-green-800';
    case MatterStatus.PENDING:
      return 'bg-yellow-100 text-yellow-800';
    case MatterStatus.CLOSED:
      return 'bg-gray-100 text-gray-800';
    case MatterStatus.REJECTED:
      return 'bg-red-100 text-red-800';
    case MatterStatus.ON_HOLD:
      return 'bg-orange-100 text-orange-800';
    case MatterStatus.SETTLED:
      return 'bg-blue-100 text-blue-800';
    case MatterStatus.ARCHIVED:
      return 'bg-gray-100 text-gray-600';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

/**
 * Helper function to convert legacy case data to matter format
 */
export function convertCaseToMatter(caseData: any): Matter {
  // Map legacy practice areas to new ones
  let practiceArea = caseData.practiceArea;
  let workType = caseData.workType;

  // Handle legacy mapping
  if (caseData.practiceArea === 'litigation') {
    practiceArea = PracticeArea.PERSONAL_INJURY;
    workType = WorkType.LITIGATION;
  }

  return {
    ...caseData,
    practiceArea: practiceArea || PracticeArea.PERSONAL_INJURY,
    workType: workType || WorkType.LITIGATION,
    displayLabel: getMatterDisplayLabel(workType || WorkType.LITIGATION),
    sensitive: caseData.sensitive || false,
    status: caseData.status || MatterStatus.ACTIVE
  };
}

/**
 * Helper function to get practice areas grouped by work type
 */
export function getPracticeAreasByWorkType(): Record<WorkType, PracticeArea[]> {
  return {
    [WorkType.LITIGATION]: [
      PracticeArea.PERSONAL_INJURY,
      PracticeArea.FAMILY_LAW,
      PracticeArea.CRIMINAL_DEFENSE,
      PracticeArea.CIVIL_LITIGATION,
      PracticeArea.EMPLOYMENT_LAW,
    ],
    [WorkType.TRANSACTIONAL]: [
      PracticeArea.CORPORATE_BUSINESS,
      PracticeArea.REAL_ESTATE,
    ],
    [WorkType.ADVISORY]: [
      PracticeArea.ESTATE_PLANNING,
      PracticeArea.TAX,
      PracticeArea.IMMIGRATION,
      PracticeArea.ELDER_LAW,
      PracticeArea.ADMINISTRATIVE_REG,
      PracticeArea.ENVIRONMENTAL,
    ],
    [WorkType.ADR]: [
      // ADR can apply to various practice areas
      PracticeArea.PERSONAL_INJURY,
      PracticeArea.FAMILY_LAW,
      PracticeArea.EMPLOYMENT_LAW,
      PracticeArea.CORPORATE_BUSINESS,
    ],
  };
}

/**
 * Helper function to get suggested work type for a practice area
 */
export function getSuggestedWorkType(practiceArea: PracticeArea): WorkType {
  const groupings = getPracticeAreasByWorkType();

  for (const [workType, areas] of Object.entries(groupings)) {
    if (areas.includes(practiceArea)) {
      return workType as WorkType;
    }
  }

  // Default fallback
  return WorkType.LITIGATION;
}
