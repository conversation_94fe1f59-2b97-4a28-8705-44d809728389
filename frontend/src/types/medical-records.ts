/**
 * Medical Records Types
 * Type definitions for medical document processing and analysis
 */

export interface MedicalEntity {
  type: 'DIAGNOSIS' | 'MEDICATION' | 'PROCEDURE' | 'CONDITION' | 'SYMPTOM' | 'ANATOMY' | 'TEST';
  text: string;
  code?: string;
  vocabulary?: 'ICD10CM' | 'RXNORM' | 'LOINC' | 'SNOMED' | 'CPT';
  certainty: 'positive' | 'negative' | 'possible' | 'conditional';
  temporal: 'present' | 'past' | 'future' | 'hypothetical';
  subject: 'patient' | 'family' | 'other';
  confidence: number;
  startOffset?: number;
  endOffset?: number;
}

export interface ChronologyEntry {
  date: string;
  title: string;
  summary: string;
  event_type: 'visit' | 'procedure' | 'test' | 'medication' | 'diagnosis' | 'discharge' | 'admission';
  severity?: 'low' | 'moderate' | 'high' | 'critical';
  citations: {
    page: number;
    section?: string;
    confidence?: number;
  }[];
  entities?: MedicalEntity[];
  documentId?: string;
}

export interface TreatmentGap {
  type: 'missing_followup' | 'medication_gap' | 'test_delay' | 'referral_delay' | 'documentation_gap';
  description: string;
  severity: 'low' | 'moderate' | 'high' | 'critical';
  startDate: string;
  endDate?: string;
  recommendations: string[];
  relatedEntries: string[]; // IDs of related chronology entries
}

export interface ProviderInfo {
  providerName?: string;
  npi?: string;
  facility?: string;
  address?: string;
  phone?: string;
  specialty?: string;
}

export interface ServiceDates {
  serviceStart: string;
  serviceEnd: string;
  admissionDate?: string;
  dischargeDate?: string;
}

export interface DocumentClassification {
  type: 'DischargeSummary' | 'OperativeReport' | 'LabResult' | 'RadiologyReport' | 
        'ProgressNote' | 'ConsultNote' | 'EmergencyReport' | 'PathologyReport' | 'Unknown';
  confidence: number;
  subtype?: string;
}

export interface ProcessedDocument {
  id: string;
  classification: DocumentClassification;
  extractedText: string;
  entities: MedicalEntity[];
  providerInfo: ProviderInfo;
  serviceDates: ServiceDates;
  chronologyEntries: ChronologyEntry[];
  processingMetadata: {
    processedAt: string;
    processingTime: number;
    documentAiVersion: string;
    nlpVersion: string;
  };
}

export interface ClinicalSummary {
  patientId: string;
  summaryDate: string;
  primaryDiagnoses: MedicalEntity[];
  secondaryDiagnoses: MedicalEntity[];
  currentMedications: MedicalEntity[];
  recentProcedures: MedicalEntity[];
  keyFindings: string[];
  recommendations: string[];
  riskFactors: string[];
  followUpNeeded: boolean;
  followUpRecommendations?: string[];
}

export interface ExportOptions {
  format: 'csv' | 'json' | 'pdf' | 'xlsx';
  includeMetadata?: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
  entityTypes?: MedicalEntity['type'][];
  sortBy?: 'date' | 'type' | 'severity';
  groupBy?: 'date' | 'provider' | 'type';
}

export interface ExportResult {
  data: string | Buffer;
  filename: string;
  mimeType: string;
  size: number;
  generatedAt: string;
}

// LangExtract Integration Types
export interface ExtractionSpan {
  start: number;
  end: number;
}

export interface EvidenceLink {
  documentId: string;
  page: number;
  quote: string; // exact text slice from Document AI text
  span?: ExtractionSpan; // optional original text offsets
  confidence?: number; // extraction confidence score (0.0-1.0)
}

export interface MedicationRow {
  drug: string;
  dose?: string;
  route?: string;
  frequency?: string;
  startDate?: string;
  endDate?: string;
  evidence: EvidenceLink[];
}

export interface RadiologyRow {
  study: string;         // e.g., MRI Cervical Spine
  date?: string;
  bodyPart?: string;
  finding?: string;
  impression?: string;
  evidence: EvidenceLink[];
}

export interface ProblemRow {
  term: string; // e.g., "cervical radiculopathy"
  evidence: EvidenceLink[];
}

export interface FollowUpCue {
  cue: string;   // e.g., "Follow up with ortho in 6 weeks"
  target?: string;
  dueDateGuess?: string;
  evidence: EvidenceLink[];
}

export interface LangExtractRequest {
  matterId: string;
  documentId: string;
  text: string;
  task: 'medications' | 'radiology' | 'problems' | 'followups';
  modelId?: string;
  examples?: any[];
}

export interface LangExtractResponse {
  extractions: {
    class: string;
    attributes: Record<string, any>;
    span: ExtractionSpan;
  }[];
  reviewHtml?: string;
}

export interface StructuredExtractions {
  documentId: string;
  matterId: string;
  medications: MedicationRow[];
  radiology: RadiologyRow[];
  problems: ProblemRow[];
  followups: FollowUpCue[];
  reviewHtmlUri?: string;
  extractedAt: string;
  confidence: number;
}
