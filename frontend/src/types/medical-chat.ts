/**
 * Medical Chat Interface Definitions
 * 
 * Type definitions for medical AI assistant chat interactions,
 * providing strong typing for ResilientChat integration and message handling.
 */

// ============================================================================
// Core Chat Message Types
// ============================================================================

/**
 * Base structure for all medical chat messages
 */
export interface MedicalChatMessage {
  /** Unique message identifier */
  id: string;
  
  /** Message content */
  content: string;
  
  /** Message type */
  type: 'user' | 'assistant' | 'system';
  
  /** Timestamp when message was created */
  timestamp: number;
  
  /** AI confidence score (assistant messages only) */
  confidence?: number;
  
  /** Document sources referenced in the message */
  sources?: DocumentSource[];
  
  /** Additional message metadata */
  metadata?: MedicalMessageMetadata;
  
  /** Processing status for assistant messages */
  status?: MessageProcessingStatus;
}

/**
 * Message processing status for streaming responses
 */
export type MessageProcessingStatus = 
  | 'generating' 
  | 'completed' 
  | 'error' 
  | 'cancelled';

/**
 * Document source reference with citation information
 */
export interface DocumentSource {
  /** Document identifier */
  documentId: string;
  
  /** Page number where information was found */
  page: number;
  
  /** Exact quote from document */
  quote: string;
  
  /** Character span in original document */
  span?: {
    start: number;
    end: number;
  };
  
  /** Confidence in this source */
  confidence: number;
  
  /** Source type classification */
  sourceType: 'primary' | 'supporting' | 'contextual';
}

/**
 * Additional metadata for medical messages
 */
export interface MedicalMessageMetadata {
  /** Associated matter ID */
  matterId: string;
  
  /** Medical entity type if message is about specific entity */
  entityType?: 'medication' | 'radiology' | 'problem' | 'followup' | 'timeline' | 'summary';
  
  /** Number of source citations in the message */
  citationCount?: number;
  
  /** AI processing time in milliseconds */
  processingTime?: number;
  
  /** Suggested follow-up questions */
  followupSuggestions?: string[];
  
  /** Medical confidence level */
  medicalConfidence?: 'low' | 'medium' | 'high' | 'very_high';
  
  /** Whether this message contains PHI */
  containsPHI?: boolean;
  
  /** HIPAA audit information */
  auditInfo?: HIPAAAuditInfo;
}

/**
 * HIPAA audit information for medical messages
 */
export interface HIPAAAuditInfo {
  /** User ID who accessed the information */
  userId: string;
  
  /** Tenant ID for data isolation */
  tenantId: string;
  
  /** Type of access */
  accessType: 'read' | 'query' | 'export' | 'modify';
  
  /** PHI elements accessed */
  phiElements: string[];
  
  /** Purpose of access */
  purpose: string;
  
  /** Audit timestamp */
  auditTimestamp: number;
}

// ============================================================================
// Medical Context Types
// ============================================================================

/**
 * Medical context provided to the chat interface
 */
export interface MedicalChatContext {
  /** Matter ID for context */
  matterId: string;
  
  /** Available medical data summary */
  medicalSummary: MedicalDataSummary;
  
  /** User's role for permission context */
  userRole: 'partner' | 'attorney' | 'paralegal' | 'staff';
  
  /** Tenant ID for data isolation */
  tenantId: string;
  
  /** Chat session configuration */
  sessionConfig: ChatSessionConfig;
  
  /** Available medical entities for context */
  availableEntities: MedicalEntitySummary[];
}

/**
 * Summary of medical data available for the matter
 */
export interface MedicalDataSummary {
  totalMedications: number;
  totalRadiology: number;
  totalProblems: number;
  totalFollowups: number;
  totalDocuments: number;
  dateRange: {
    earliest: string | null;
    latest: string | null;
  };
  providers: string[];
  documentTypes: string[];
}

/**
 * Summary of a medical entity for context
 */
export interface MedicalEntitySummary {
  type: 'medication' | 'radiology' | 'problem' | 'followup';
  id: string;
  title: string;
  date: string | null;
  confidence: number;
  hasEvidence: boolean;
}

/**
 * Chat session configuration
 */
export interface ChatSessionConfig {
  /** Enable streaming responses */
  enableStreaming: boolean;
  
  /** Enable source grounding */
  enableSourceGrounding: boolean;
  
  /** Enable confidence scoring */
  enableConfidenceScoring: boolean;
  
  /** Maximum message history to maintain */
  maxHistoryLength: number;
  
  /** AI model configuration */
  modelConfig: ModelConfiguration;
  
  /** HIPAA compliance settings */
  hipaaConfig: HIPAAComplianceConfig;
}

/**
 * AI model configuration
 */
export interface ModelConfiguration {
  /** Model identifier */
  modelId: string;
  
  /** Temperature setting */
  temperature: number;
  
  /** Maximum tokens in response */
  maxTokens: number;
  
  /** System prompt template */
  systemPrompt: string;
  
  /** Enable zero data retention */
  zeroDataRetention: boolean;
}

/**
 * HIPAA compliance configuration
 */
export interface HIPAAComplianceConfig {
  /** Enable audit logging */
  enableAuditLogging: boolean;
  
  /** Enable PHI detection */
  enablePHIDetection: boolean;
  
  /** Redaction settings */
  redactionConfig: {
    enabled: boolean;
    redactNames: boolean;
    redactDates: boolean;
    redactAddresses: boolean;
  };
  
  /** Session timeout in minutes */
  sessionTimeout: number;
}

// ============================================================================
// Chat State Management
// ============================================================================

/**
 * Medical chat state for React components
 */
export interface MedicalChatState {
  /** Current messages in the chat */
  messages: MedicalChatMessage[];
  
  /** Loading state */
  isLoading: boolean;
  
  /** Error state */
  error: string | null;
  
  /** Current medical context */
  context: MedicalChatContext | null;
  
  /** Suggested queries */
  suggestions: string[];
  
  /** Chat session status */
  sessionStatus: ChatSessionStatus;
  
  /** Streaming message being composed */
  streamingMessage: Partial<MedicalChatMessage> | null;
}

/**
 * Chat session status
 */
export type ChatSessionStatus = 
  | 'initializing'
  | 'ready'
  | 'processing'
  | 'error'
  | 'expired'
  | 'disconnected';

// ============================================================================
// Chat Event Types
// ============================================================================

/**
 * Medical chat events for component communication
 */
export type MedicalChatEvent = 
  | MessageSentEvent
  | MessageReceivedEvent
  | StreamingUpdateEvent
  | ErrorEvent
  | ContextUpdateEvent
  | SessionStatusEvent;

/**
 * Message sent event
 */
export interface MessageSentEvent {
  type: 'message_sent';
  message: MedicalChatMessage;
  timestamp: number;
}

/**
 * Message received event
 */
export interface MessageReceivedEvent {
  type: 'message_received';
  message: MedicalChatMessage;
  timestamp: number;
}

/**
 * Streaming update event
 */
export interface StreamingUpdateEvent {
  type: 'streaming_update';
  messageId: string;
  contentDelta: string;
  timestamp: number;
}

/**
 * Error event
 */
export interface ErrorEvent {
  type: 'error';
  error: string;
  code?: string;
  timestamp: number;
}

/**
 * Context update event
 */
export interface ContextUpdateEvent {
  type: 'context_update';
  context: Partial<MedicalChatContext>;
  timestamp: number;
}

/**
 * Session status event
 */
export interface SessionStatusEvent {
  type: 'session_status';
  status: ChatSessionStatus;
  timestamp: number;
}

// ============================================================================
// Medical Insights & Analysis
// ============================================================================

/**
 * Medical insight generated from chat interaction
 */
export interface MedicalInsight {
  /** Unique insight identifier */
  id: string;
  
  /** Insight content */
  content: string;
  
  /** Type of insight */
  type: 'summary' | 'analysis' | 'recommendation' | 'gap_detection' | 'timeline';
  
  /** Confidence in the insight */
  confidence: number;
  
  /** Source documents */
  sources: DocumentSource[];
  
  /** Generated timestamp */
  timestamp: number;
  
  /** Associated medical entities */
  relatedEntities: string[];
  
  /** Actionable recommendations */
  recommendations?: ActionableRecommendation[];
}

/**
 * Actionable recommendation from medical analysis
 */
export interface ActionableRecommendation {
  /** Recommendation text */
  text: string;
  
  /** Priority level */
  priority: 'low' | 'medium' | 'high' | 'urgent';
  
  /** Type of action */
  actionType: 'follow_up' | 'investigation' | 'documentation' | 'expert_consultation';
  
  /** Estimated effort */
  effort: 'minimal' | 'moderate' | 'significant';
  
  /** Supporting evidence */
  evidence: DocumentSource[];
}

// ============================================================================
// Type Guards for Runtime Validation
// ============================================================================

/**
 * Type guard for medical chat message
 */
export function isMedicalChatMessage(data: unknown): data is MedicalChatMessage {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).id === 'string' &&
    typeof (data as any).content === 'string' &&
    ['user', 'assistant', 'system'].includes((data as any).type) &&
    typeof (data as any).timestamp === 'number'
  );
}

/**
 * Type guard for document source
 */
export function isDocumentSource(data: unknown): data is DocumentSource {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).documentId === 'string' &&
    typeof (data as any).page === 'number' &&
    typeof (data as any).quote === 'string' &&
    typeof (data as any).confidence === 'number'
  );
}

/**
 * Type guard for medical insight
 */
export function isMedicalInsight(data: unknown): data is MedicalInsight {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).id === 'string' &&
    typeof (data as any).content === 'string' &&
    ['summary', 'analysis', 'recommendation', 'gap_detection', 'timeline'].includes((data as any).type) &&
    typeof (data as any).confidence === 'number'
  );
}

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Create a medical chat message
 */
export function createMedicalChatMessage(
  content: string,
  type: MedicalChatMessage['type'],
  metadata?: Partial<MedicalMessageMetadata> & { matterId: string }
): MedicalChatMessage {
  return {
    id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    content,
    type,
    timestamp: Date.now(),
    metadata: metadata ? { ...metadata } : undefined
  };
}

/**
 * Create a document source reference
 */
export function createDocumentSource(
  documentId: string,
  page: number,
  quote: string,
  confidence: number,
  sourceType: DocumentSource['sourceType'] = 'primary'
): DocumentSource {
  return {
    documentId,
    page,
    quote,
    confidence,
    sourceType
  };
}

/**
 * Validate medical context
 */
export function validateMedicalContext(context: unknown): context is MedicalChatContext {
  return (
    typeof context === 'object' &&
    context !== null &&
    typeof (context as any).matterId === 'string' &&
    typeof (context as any).tenantId === 'string' &&
    typeof (context as any).medicalSummary === 'object'
  );
}

/**
 * Create default chat session config
 */
export function createDefaultChatSessionConfig(): ChatSessionConfig {
  return {
    enableStreaming: true,
    enableSourceGrounding: true,
    enableConfidenceScoring: true,
    maxHistoryLength: 50,
    modelConfig: {
      modelId: 'gemini-2.5-flash',
      temperature: 0.7,
      maxTokens: 2048,
      systemPrompt: 'You are a medical AI assistant helping with legal case analysis.',
      zeroDataRetention: true
    },
    hipaaConfig: {
      enableAuditLogging: true,
      enablePHIDetection: true,
      redactionConfig: {
        enabled: true,
        redactNames: false, // Names needed for legal context
        redactDates: false, // Dates needed for timeline
        redactAddresses: true
      },
      sessionTimeout: 30
    }
  };
}