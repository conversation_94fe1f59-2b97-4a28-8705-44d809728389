/**
 * Runtime Type Guards for Medical System
 * 
 * Comprehensive type guards for validating external data and ensuring type safety
 * at runtime, especially for data coming from APIs, databases, and user inputs.
 */

import type {
  MedicationRow,
  RadiologyRow,
  ProblemRow,
  FollowUpCue,
  EvidenceLink,
  StructuredExtractions
} from '@/types/medical-records'

import type {
  MedicalChatMessage,
  DocumentSource,
  MedicalMessageMetadata,
  MedicalChatContext,
  MedicalDataSummary
} from '@/types/medical-chat'

import type {
  MedicationRowData,
  RadiologyRowData,
  ProblemRowData,
  FollowupCueRowData,
  UserTenantProfile,
  SupabaseError
} from '@/types/medical-database'

import type {
  MedicalError,
  ProcessingError,
  HIPAAViolationError
} from '@/types/medical-errors'

// ============================================================================
// EvidenceLink and Source Validation
// ============================================================================

/**
 * Type guard for EvidenceLink array
 */
export function isEvidenceLink(data: unknown): data is EvidenceLink {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).quote === 'string' &&
    typeof (data as any).documentId === 'string' &&
    typeof (data as any).page === 'number'
  )
}

/**
 * Type guard for EvidenceLink array
 */
export function isEvidenceLinkArray(data: unknown): data is EvidenceLink[] {
  return Array.isArray(data) && data.every(item => isEvidenceLink(item))
}

/**
 * Type guard for DocumentSource
 */
export function isDocumentSource(data: unknown): data is DocumentSource {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).documentId === 'string' &&
    typeof (data as any).page === 'number' &&
    typeof (data as any).quote === 'string' &&
    typeof (data as any).confidence === 'number' &&
    ['primary', 'supporting', 'contextual'].includes((data as any).sourceType)
  )
}

// ============================================================================
// Medical Records Validation
// ============================================================================

/**
 * Type guard for MedicationRow
 */
export function isMedicationRow(data: unknown): data is MedicationRow {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).drug === 'string' &&
    isEvidenceLinkArray((data as any).evidence)
  )
}

/**
 * Type guard for RadiologyRow
 */
export function isRadiologyRow(data: unknown): data is RadiologyRow {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).study === 'string' &&
    isEvidenceLinkArray((data as any).evidence)
  )
}

/**
 * Type guard for ProblemRow
 */
export function isProblemRow(data: unknown): data is ProblemRow {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).term === 'string' &&
    isEvidenceLinkArray((data as any).evidence)
  )
}

/**
 * Type guard for FollowUpCue
 */
export function isFollowUpCue(data: unknown): data is FollowUpCue {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).cue === 'string' &&
    isEvidenceLinkArray((data as any).evidence)
  )
}

/**
 * Type guard for StructuredExtractions
 */
export function isStructuredExtractions(data: unknown): data is StructuredExtractions {
  return (
    typeof data === 'object' &&
    data !== null &&
    Array.isArray((data as any).medications) &&
    Array.isArray((data as any).radiology) &&
    Array.isArray((data as any).problems) &&
    Array.isArray((data as any).followups) &&
    (data as any).medications.every((item: unknown) => isMedicationRow(item)) &&
    (data as any).radiology.every((item: unknown) => isRadiologyRow(item)) &&
    (data as any).problems.every((item: unknown) => isProblemRow(item)) &&
    (data as any).followups.every((item: unknown) => isFollowUpCue(item))
  )
}

// ============================================================================
// Chat System Validation
// ============================================================================

/**
 * Type guard for MedicalChatMessage
 */
export function isMedicalChatMessage(data: unknown): data is MedicalChatMessage {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).id === 'string' &&
    typeof (data as any).content === 'string' &&
    ['user', 'assistant', 'system'].includes((data as any).type) &&
    typeof (data as any).timestamp === 'number'
  )
}

/**
 * Type guard for MedicalMessageMetadata
 */
export function isMedicalMessageMetadata(data: unknown): data is MedicalMessageMetadata {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).matterId === 'string'
  )
}

/**
 * Type guard for MedicalDataSummary
 */
export function isMedicalDataSummary(data: unknown): data is MedicalDataSummary {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).totalMedications === 'number' &&
    typeof (data as any).totalRadiology === 'number' &&
    typeof (data as any).totalProblems === 'number' &&
    typeof (data as any).totalFollowups === 'number' &&
    typeof (data as any).totalDocuments === 'number' &&
    Array.isArray((data as any).providers) &&
    Array.isArray((data as any).documentTypes)
  )
}

/**
 * Type guard for MedicalChatContext
 */
export function isMedicalChatContext(data: unknown): data is MedicalChatContext {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).matterId === 'string' &&
    typeof (data as any).tenantId === 'string' &&
    isMedicalDataSummary((data as any).medicalSummary) &&
    ['partner', 'attorney', 'paralegal', 'staff'].includes((data as any).userRole)
  )
}

// ============================================================================
// Database Types Validation
// ============================================================================

/**
 * Type guard for MedicationRowData (raw database)
 */
export function isMedicationRowData(data: unknown): data is MedicationRowData {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).id === 'string' &&
    typeof (data as any).drug === 'string' &&
    typeof (data as any).tenant_id === 'string' &&
    Array.isArray((data as any).evidence)
  )
}

/**
 * Type guard for RadiologyRowData (raw database)
 */
export function isRadiologyRowData(data: unknown): data is RadiologyRowData {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).id === 'string' &&
    typeof (data as any).study === 'string' &&
    typeof (data as any).tenant_id === 'string' &&
    Array.isArray((data as any).evidence)
  )
}

/**
 * Type guard for ProblemRowData (raw database)
 */
export function isProblemRowData(data: unknown): data is ProblemRowData {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).id === 'string' &&
    typeof (data as any).term === 'string' &&
    typeof (data as any).tenant_id === 'string' &&
    Array.isArray((data as any).evidence)
  )
}

/**
 * Type guard for FollowupCueRowData (raw database)
 */
export function isFollowupCueRowData(data: unknown): data is FollowupCueRowData {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).id === 'string' &&
    typeof (data as any).cue === 'string' &&
    typeof (data as any).tenant_id === 'string' &&
    Array.isArray((data as any).evidence)
  )
}

/**
 * Type guard for UserTenantProfile
 */
export function isUserTenantProfile(data: unknown): data is UserTenantProfile {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).tenant_id === 'string' &&
    typeof (data as any).id === 'string'
  )
}

/**
 * Type guard for SupabaseError
 */
export function isSupabaseError(error: unknown): error is SupabaseError {
  return (
    typeof error === 'object' &&
    error !== null &&
    typeof (error as any).message === 'string' &&
    typeof (error as any).code === 'string'
  )
}

// ============================================================================
// Error Types Validation
// ============================================================================

/**
 * Type guard for MedicalError
 */
export function isMedicalError(error: unknown): error is MedicalError {
  return error instanceof Error && 'code' in error && 'severity' in error
}

/**
 * Type guard for ProcessingError
 */
export function isProcessingError(error: unknown): error is ProcessingError {
  return (
    error instanceof Error &&
    'code' in error &&
    (error as any).code === 'PROCESSING_ERROR'
  )
}

/**
 * Type guard for HIPAAViolationError
 */
export function isHIPAAViolationError(error: unknown): error is HIPAAViolationError {
  return (
    error instanceof Error &&
    'code' in error &&
    (error as any).code === 'HIPAA_VIOLATION_ERROR'
  )
}

// ============================================================================
// API Response Validation
// ============================================================================

/**
 * Validate API response structure
 */
export function isValidApiResponse<T>(
  data: unknown,
  validator: (data: unknown) => data is T
): data is { data: T; error: null } | { data: null; error: string } {
  return (
    typeof data === 'object' &&
    data !== null &&
    ((
      'data' in data &&
      validator((data as any).data) &&
      (data as any).error === null
    ) || (
      (data as any).data === null &&
      typeof (data as any).error === 'string'
    ))
  )
}

/**
 * Validate paginated response structure
 */
export function isValidPaginatedResponse<T>(
  data: unknown,
  itemValidator: (data: unknown) => data is T
): data is {
  data: T[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
} {
  return (
    typeof data === 'object' &&
    data !== null &&
    Array.isArray((data as any).data) &&
    (data as any).data.every((item: unknown) => itemValidator(item)) &&
    typeof (data as any).total === 'number' &&
    typeof (data as any).page === 'number' &&
    typeof (data as any).pageSize === 'number' &&
    typeof (data as any).hasMore === 'boolean'
  )
}

// ============================================================================
// Form Data Validation
// ============================================================================

/**
 * Type guard for form data objects
 */
export function isValidFormData(data: unknown): data is Record<string, unknown> {
  return typeof data === 'object' && data !== null && !Array.isArray(data)
}

/**
 * Validate string field from form data
 */
export function validateStringField(data: unknown, fieldName: string): string | null {
  if (!isValidFormData(data)) return null
  const field = (data as any)[fieldName]
  return typeof field === 'string' && field.trim().length > 0 ? field.trim() : null
}

/**
 * Validate number field from form data
 */
export function validateNumberField(data: unknown, fieldName: string): number | null {
  if (!isValidFormData(data)) return null
  const field = (data as any)[fieldName]
  if (typeof field === 'number' && !isNaN(field)) return field
  if (typeof field === 'string') {
    const num = parseFloat(field)
    return !isNaN(num) ? num : null
  }
  return null
}

/**
 * Validate array field from form data
 */
export function validateArrayField<T>(
  data: unknown, 
  fieldName: string,
  itemValidator: (item: unknown) => item is T
): T[] | null {
  if (!isValidFormData(data)) return null
  const field = (data as any)[fieldName]
  if (!Array.isArray(field)) return null
  return field.every(itemValidator) ? field : null
}

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Safe JSON parsing with validation
 */
export function safeParse<T>(
  json: string,
  validator: (data: unknown) => data is T
): T | null {
  try {
    const parsed = JSON.parse(json)
    return validator(parsed) ? parsed : null
  } catch {
    return null
  }
}

/**
 * Validate and transform database row to business object
 */
export function transformDatabaseRow<TRow, TBusiness>(
  row: unknown,
  validator: (data: unknown) => data is TRow,
  transformer: (row: TRow) => TBusiness
): TBusiness | null {
  if (!validator(row)) return null
  try {
    return transformer(row)
  } catch {
    return null
  }
}

/**
 * Validate array of items with error collection
 */
export function validateArrayWithErrors<T>(
  items: unknown[],
  validator: (item: unknown, index: number) => item is T
): { valid: T[]; errors: { index: number; error: string }[] } {
  const valid: T[] = []
  const errors: { index: number; error: string }[] = []
  
  items.forEach((item, index) => {
    if (validator(item, index)) {
      valid.push(item)
    } else {
      errors.push({
        index,
        error: `Invalid item at index ${index}`
      })
    }
  })
  
  return { valid, errors }
}

/**
 * Create a runtime validator that combines multiple type guards
 */
export function createCompositeValidator<T>(
  ...validators: ((data: unknown) => data is T)[]
): (data: unknown) => data is T {
  return (data: unknown): data is T => {
    return validators.every(validator => validator(data))
  }
}

/**
 * Validate object has required properties
 */
export function hasRequiredProperties<T extends Record<string, unknown>>(
  obj: unknown,
  requiredKeys: (keyof T)[]
): obj is T {
  if (typeof obj !== 'object' || obj === null) return false
  
  return requiredKeys.every(key => 
    key in obj && (obj as any)[key] !== undefined && (obj as any)[key] !== null
  )
}

/**
 * Validate and sanitize input string
 */
export function validateAndSanitizeString(
  input: unknown,
  maxLength = 1000,
  allowedChars = /^[\w\s\-.,!?()]+$/
): string | null {
  if (typeof input !== 'string') return null
  
  const trimmed = input.trim()
  if (trimmed.length === 0 || trimmed.length > maxLength) return null
  if (!allowedChars.test(trimmed)) return null
  
  return trimmed
}

// ============================================================================
// HIPAA Compliance Validators
// ============================================================================

/**
 * Check if data contains potential PHI
 */
export function containsPotentialPHI(text: string): boolean {
  // Simple regex patterns for common PHI elements
  const phiPatterns = [
    /\b\d{3}-\d{2}-\d{4}\b/, // SSN pattern
    /\b\d{3}-\d{3}-\d{4}\b/, // Phone pattern
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, // Email
    /\b\d{1,2}\/\d{1,2}\/\d{4}\b/, // Date pattern (MM/DD/YYYY)
    /\b(Mr|Mrs|Ms|Dr|Doctor|Patient)\s+[A-Z][a-z]+\b/, // Name patterns
  ]
  
  return phiPatterns.some(pattern => pattern.test(text))
}

/**
 * Validate HIPAA audit log entry
 */
export function isValidHIPAAAuditEntry(data: unknown): boolean {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof (data as any).userId === 'string' &&
    typeof (data as any).tenantId === 'string' &&
    typeof (data as any).action === 'string' &&
    typeof (data as any).timestamp === 'number' &&
    Array.isArray((data as any).phiElements)
  )
}