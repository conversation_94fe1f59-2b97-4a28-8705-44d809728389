import type { J<PERSON> } from '../supabase/database.types';

/**
 * Safely converts metadata to Json type for database operations
 * @param metadata The metadata to convert
 * @returns The metadata as Json
 */
export function toJsonSafe<T extends Record<string, unknown> | undefined | null>(metadata: T): Json {
  // If metadata is undefined or null, return an empty object as Json
  if (metadata === undefined || metadata === null) {
    return {} as unknown as Json;
  }
  // Otherwise, convert the metadata to Json
  return metadata as unknown as Json;
}

/**
 * Safely converts Json from database to a Record type
 * @param jsonData The Json data from database
 * @returns The Json data as a Record or undefined
 */
export function fromJsonSafe<T = Record<string, unknown>>(jsonData: Json | null): T | undefined {
  if (jsonData === null) {
    return undefined;
  }
  return jsonData as unknown as T;
}

/**
 * Create a metadata wrapper function to handle Json conversions in mappers
 * @param row The database row
 * @param field The field name
 * @returns The metadata as a Record or undefined
 */
export function mapMetadata<T = Record<string, unknown>>(row: any, field = 'metadata'): T | null | undefined {
  if (row[field] === null || row[field] === undefined) {
    return null;
  }
  return row[field] as unknown as T;
}
