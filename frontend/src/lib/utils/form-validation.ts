/**
 * Form validation utilities for subscription management
 */

import type { 
  CreatePlanRequest, 
  UpdatePlanRequest, 
  CreateAddonRequest, 
  UpdateAddonRequest,
  ValidationError,
  ValidationResult 
} from '@/types/subscription-enhanced';
import { SUPPORTED_CURRENCIES } from '@/lib/utils/subscription';

/**
 * Base validation rules
 */
export const ValidationRules = {
  required: (value: unknown, fieldName: string): ValidationError | null => {
    if (value === null || value === undefined || value === '') {
      return { field: fieldName, message: `${fieldName} is required` };
    }
    return null;
  },

  minLength: (value: string, min: number, fieldName: string): ValidationError | null => {
    if (value && value.length < min) {
      return { field: fieldName, message: `${fieldName} must be at least ${min} characters` };
    }
    return null;
  },

  maxLength: (value: string, max: number, fieldName: string): ValidationError | null => {
    if (value && value.length > max) {
      return { field: fieldName, message: `${fieldName} must be no more than ${max} characters` };
    }
    return null;
  },

  pattern: (value: string, pattern: RegExp, fieldName: string, message?: string): ValidationError | null => {
    if (value && !pattern.test(value)) {
      return { 
        field: fieldName, 
        message: message || `${fieldName} format is invalid` 
      };
    }
    return null;
  },

  positiveNumber: (value: number, fieldName: string): ValidationError | null => {
    if (value !== undefined && value <= 0) {
      return { field: fieldName, message: `${fieldName} must be greater than 0` };
    }
    return null;
  },

  currency: (value: string, fieldName: string): ValidationError | null => {
    if (value && !Object.keys(SUPPORTED_CURRENCIES).includes(value)) {
      return { 
        field: fieldName, 
        message: `${fieldName} must be one of: ${Object.keys(SUPPORTED_CURRENCIES).join(', ')}` 
      };
    }
    return null;
  },

  countryCode: (value: string, fieldName: string): ValidationError | null => {
    if (value && !/^[A-Z]{2}$/.test(value)) {
      return { 
        field: fieldName, 
        message: `${fieldName} must be a valid 2-letter country code (e.g., US, GB, DE)` 
      };
    }
    return null;
  },

  planCode: (value: string, fieldName: string): ValidationError | null => {
    if (value && !/^[a-z0-9_-]+$/.test(value)) {
      return { 
        field: fieldName, 
        message: `${fieldName} can only contain lowercase letters, numbers, hyphens, and underscores` 
      };
    }
    return null;
  },

  email: (value: string, fieldName: string): ValidationError | null => {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (value && !emailPattern.test(value)) {
      return { field: fieldName, message: `${fieldName} must be a valid email address` };
    }
    return null;
  },

  url: (value: string, fieldName: string): ValidationError | null => {
    try {
      if (value) {
        new URL(value);
      }
      return null;
    } catch {
      return { field: fieldName, message: `${fieldName} must be a valid URL` };
    }
  },
};

/**
 * Validate subscription plan data
 */
export function validatePlanData(data: CreatePlanRequest | UpdatePlanRequest): ValidationResult {
  const errors: ValidationError[] = [];

  // Required fields
  const requiredError = ValidationRules.required(data.name, 'name');
  if (requiredError) errors.push(requiredError);

  const codeRequiredError = ValidationRules.required(data.code, 'code');
  if (codeRequiredError) errors.push(codeRequiredError);

  // Field format validation
  if (data.name) {
    const nameMinError = ValidationRules.minLength(data.name, 2, 'name');
    if (nameMinError) errors.push(nameMinError);

    const nameMaxError = ValidationRules.maxLength(data.name, 100, 'name');
    if (nameMaxError) errors.push(nameMaxError);
  }

  if (data.code) {
    const codeMinError = ValidationRules.minLength(data.code, 2, 'code');
    if (codeMinError) errors.push(codeMinError);

    const codeMaxError = ValidationRules.maxLength(data.code, 50, 'code');
    if (codeMaxError) errors.push(codeMaxError);

    const codePatternError = ValidationRules.planCode(data.code, 'code');
    if (codePatternError) errors.push(codePatternError);
  }

  if (data.description) {
    const descMaxError = ValidationRules.maxLength(data.description, 500, 'description');
    if (descMaxError) errors.push(descMaxError);
  }

  // Pricing validation
  if (!data.pricing || data.pricing.length === 0) {
    errors.push({ field: 'pricing', message: 'At least one pricing option is required' });
  } else {
    data.pricing.forEach((pricing, index) => {
      const prefix = `pricing[${index}]`;

      const currencyError = ValidationRules.required(pricing.currency, `${prefix}.currency`);
      if (currencyError) errors.push(currencyError);

      if (pricing.currency) {
        const currencyValidError = ValidationRules.currency(pricing.currency, `${prefix}.currency`);
        if (currencyValidError) errors.push(currencyValidError);
      }

      const countryError = ValidationRules.required(pricing.country_code, `${prefix}.country_code`);
      if (countryError) errors.push(countryError);

      if (pricing.country_code) {
        const countryValidError = ValidationRules.countryCode(pricing.country_code, `${prefix}.country_code`);
        if (countryValidError) errors.push(countryValidError);
      }

      const monthlyError = ValidationRules.positiveNumber(pricing.price_monthly, `${prefix}.price_monthly`);
      if (monthlyError) errors.push(monthlyError);

      const yearlyError = ValidationRules.positiveNumber(pricing.price_yearly, `${prefix}.price_yearly`);
      if (yearlyError) errors.push(yearlyError);

      // Validate yearly price is less than 12x monthly (should offer savings)
      if (pricing.price_monthly > 0 && pricing.price_yearly > 0) {
        const maxYearlyPrice = pricing.price_monthly * 12;
        if (pricing.price_yearly >= maxYearlyPrice) {
          errors.push({
            field: `${prefix}.price_yearly`,
            message: 'Yearly price should be less than 12x monthly price to offer savings'
          });
        }
      }
    });

    // Check for duplicate country/currency combinations
    const combinations = new Set<string>();
    data.pricing.forEach((pricing, index) => {
      const combo = `${pricing.country_code}-${pricing.currency}`;
      if (combinations.has(combo)) {
        errors.push({
          field: `pricing[${index}]`,
          message: `Duplicate pricing for ${pricing.country_code}/${pricing.currency}`
        });
      }
      combinations.add(combo);
    });
  }

  // Available countries validation
  if (data.available_countries) {
    data.available_countries.forEach((country, index) => {
      const countryError = ValidationRules.countryCode(country, `available_countries[${index}]`);
      if (countryError) errors.push(countryError);
    });
  }

  // Base currency validation
  if (data.base_currency) {
    const baseCurrencyError = ValidationRules.currency(data.base_currency, 'base_currency');
    if (baseCurrencyError) errors.push(baseCurrencyError);
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Validate subscription add-on data
 */
export function validateAddonData(data: CreateAddonRequest | UpdateAddonRequest): ValidationResult {
  const errors: ValidationError[] = [];

  // Required fields
  const requiredError = ValidationRules.required(data.name, 'name');
  if (requiredError) errors.push(requiredError);

  const codeRequiredError = ValidationRules.required(data.code, 'code');
  if (codeRequiredError) errors.push(codeRequiredError);

  const typeRequiredError = ValidationRules.required(data.addon_type, 'addon_type');
  if (typeRequiredError) errors.push(typeRequiredError);

  // Field format validation
  if (data.name) {
    const nameMinError = ValidationRules.minLength(data.name, 2, 'name');
    if (nameMinError) errors.push(nameMinError);

    const nameMaxError = ValidationRules.maxLength(data.name, 100, 'name');
    if (nameMaxError) errors.push(nameMaxError);
  }

  if (data.code) {
    const codeMinError = ValidationRules.minLength(data.code, 2, 'code');
    if (codeMinError) errors.push(codeMinError);

    const codeMaxError = ValidationRules.maxLength(data.code, 50, 'code');
    if (codeMaxError) errors.push(codeMaxError);

    const codePatternError = ValidationRules.planCode(data.code, 'code');
    if (codePatternError) errors.push(codePatternError);
  }

  if (data.description) {
    const descMaxError = ValidationRules.maxLength(data.description, 500, 'description');
    if (descMaxError) errors.push(descMaxError);
  }

  // Add-on type validation
  if (data.addon_type && !['feature', 'usage', 'service'].includes(data.addon_type)) {
    errors.push({
      field: 'addon_type',
      message: 'Add-on type must be one of: feature, usage, service'
    });
  }

  // Pricing validation (similar to plans)
  if (!data.pricing || data.pricing.length === 0) {
    errors.push({ field: 'pricing', message: 'At least one pricing option is required' });
  } else {
    data.pricing.forEach((pricing, index) => {
      const prefix = `pricing[${index}]`;

      const currencyError = ValidationRules.required(pricing.currency, `${prefix}.currency`);
      if (currencyError) errors.push(currencyError);

      if (pricing.currency) {
        const currencyValidError = ValidationRules.currency(pricing.currency, `${prefix}.currency`);
        if (currencyValidError) errors.push(currencyValidError);
      }

      const countryError = ValidationRules.required(pricing.country_code, `${prefix}.country_code`);
      if (countryError) errors.push(countryError);

      if (pricing.country_code) {
        const countryValidError = ValidationRules.countryCode(pricing.country_code, `${prefix}.country_code`);
        if (countryValidError) errors.push(countryValidError);
      }

      const monthlyError = ValidationRules.positiveNumber(pricing.price_monthly, `${prefix}.price_monthly`);
      if (monthlyError) errors.push(monthlyError);

      const yearlyError = ValidationRules.positiveNumber(pricing.price_yearly, `${prefix}.price_yearly`);
      if (yearlyError) errors.push(yearlyError);
    });
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Validate form field in real-time
 */
export function validateField(
  value: unknown,
  rules: ((value: unknown, fieldName: string) => ValidationError | null)[],
  fieldName: string
): ValidationError | null {
  for (const rule of rules) {
    const error = rule(value, fieldName);
    if (error) {
      return error;
    }
  }
  return null;
}

/**
 * Create validation schema for forms
 */
export function createValidationSchema<T extends Record<string, unknown>>(
  schema: Record<keyof T, ((value: unknown, fieldName: string) => ValidationError | null)[]>
) {
  return (data: T): ValidationResult => {
    const errors: ValidationError[] = [];

    Object.entries(schema).forEach(([fieldName, rules]) => {
      const value = data[fieldName as keyof T];
      const error = validateField(value, rules, fieldName);
      if (error) {
        errors.push(error);
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  };
}

/**
 * Format validation errors for display
 */
export function formatValidationErrors(errors: ValidationError[]): Record<string, string> {
  return errors.reduce((acc, error) => {
    acc[error.field] = error.message;
    return acc;
  }, {} as Record<string, string>);
}

/**
 * Get first error for a field
 */
export function getFieldError(errors: ValidationError[], fieldName: string): string | null {
  const error = errors.find(e => e.field === fieldName);
  return error ? error.message : null;
}
