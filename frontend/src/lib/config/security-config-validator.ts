/**
 * Security Configuration Validator
 * 
 * Comprehensive validation system for all security-critical configuration settings.
 * This module provides validation, monitoring, and health checking for security configurations
 * to prevent misconfigurations that could lead to security vulnerabilities.
 */



/**
 * Security Configuration Categories
 */
export enum SecurityConfigCategory {
  AUTHENTICATION = 'authentication',
  DATABASE = 'database',
  API_KEYS = 'api_keys',
  SECURITY_HEADERS = 'security_headers',
  CORS = 'cors',
  JWT = 'jwt',
  ENVIRONMENT = 'environment',
  SUPER_ADMIN = 'super_admin',
  FEATURE_FLAGS = 'feature_flags',
  EXTERNAL_SERVICES = 'external_services'
}

/**
 * Validation Severity Levels
 */
export enum ValidationSeverity {
  CRITICAL = 'critical',    // Production blocking
  HIGH = 'high',           // Security risk
  MEDIUM = 'medium',       // Best practice violation
  LOW = 'low',            // Recommendation
  INFO = 'info'           // Informational
}

/**
 * Configuration Validation Result
 */
export interface ConfigValidationResult {
  key: string;
  category: SecurityConfigCategory;
  severity: ValidationSeverity;
  isValid: boolean;
  message: string;
  recommendation?: string;
  value?: string; // Masked for sensitive values
  required: boolean;
}

/**
 * Security Configuration Audit Report
 */
export interface SecurityConfigAudit {
  timestamp: string;
  environment: string;
  overallStatus: 'healthy' | 'warning' | 'critical';
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  criticalIssues: number;
  highIssues: number;
  mediumIssues: number;
  lowIssues: number;
  results: ConfigValidationResult[];
  summary: Record<SecurityConfigCategory, {
      total: number;
      passed: number;
      failed: number;
      criticalIssues: number;
    }>;
}

/**
 * Configuration Setting Definition
 */
export interface ConfigSetting {
  key: string;
  required: boolean;
  validator: (value: string | undefined) => ConfigValidationResult;
  description: string;
}

/**
 * Security-Critical Configuration Settings Registry
 */
export const SECURITY_CONFIG_REGISTRY = {
  // Authentication Configuration
  [SecurityConfigCategory.AUTHENTICATION]: [
    {
      key: 'NEXT_PUBLIC_SUPABASE_URL',
      required: true,
      validator: validateSupabaseUrl,
      description: 'Supabase project URL for authentication'
    },
    {
      key: 'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      required: true,
      validator: validateSupabaseAnonKey,
      description: 'Supabase anonymous key for client-side authentication'
    },
    {
      key: 'SUPABASE_SERVICE_KEY',
      required: true,
      validator: validateSupabaseServiceKey,
      description: 'Supabase service role key for server-side operations'
    },
    {
      key: 'SUPABASE_JWT_SECRET',
      required: true,
      validator: validateJwtSecret,
      description: 'JWT secret for token validation'
    },
    {
      key: 'NEXT_PUBLIC_USE_SUPABASE_AUTH',
      required: false,
      validator: validateBooleanFlag,
      description: 'Feature flag for Supabase authentication mode'
    }
  ] as ConfigSetting[],

  // Database Configuration (Optional - Frontend uses Supabase, Backend uses direct DB)
  [SecurityConfigCategory.DATABASE]: [
    {
      key: 'DB_HOST',
      required: false, // Optional for frontend - backend handles direct DB connections
      validator: validateDatabaseHost,
      description: 'Database host for direct connections (backend only)'
    },
    {
      key: 'DB_PORT',
      required: false, // Optional for frontend
      validator: validateDatabasePort,
      description: 'Database port number (backend only)'
    },
    {
      key: 'DB_NAME',
      required: false, // Optional for frontend
      validator: validateDatabaseName,
      description: 'Database name (backend only)'
    },
    {
      key: 'DB_USER',
      required: false, // Optional for frontend
      validator: validateDatabaseUser,
      description: 'Database username (backend only)'
    },
    {
      key: 'DB_PASSWORD',
      required: false, // Optional for frontend
      validator: validateDatabasePassword,
      description: 'Database password (backend only)'
    }
  ] as ConfigSetting[],

  // API Keys Configuration (Optional for frontend - backend handles API calls)
  [SecurityConfigCategory.API_KEYS]: [
    {
      key: 'OPENAI_API_KEY',
      required: false, // Optional for frontend - backend handles OpenAI calls
      validator: validateOpenAIApiKey,
      description: 'OpenAI API key for AI services (backend only)'
    },
    {
      key: 'VOYAGE_API_KEY',
      required: false, // Optional for frontend - backend handles embeddings
      validator: validateVoyageApiKey,
      description: 'Voyage API key for embeddings (backend only)'
    },
    {
      key: 'PINECONE_API_KEY',
      required: false, // Optional for frontend - backend handles vector DB
      validator: validatePineconeApiKey,
      description: 'Pinecone API key for vector database (backend only)'
    },
    {
      key: 'CPK_ENDPOINT_SECRET',
      required: false, // Optional for frontend - can be configured later
      validator: validateCopilotKitSecret,
      description: 'CopilotKit endpoint secret (optional)'
    },
    {
      key: 'LANGSMITH_API_KEY',
      required: false,
      validator: validateLangSmithApiKey,
      description: 'LangSmith API key for monitoring (optional)'
    }
  ] as ConfigSetting[],

  // Super Admin Configuration
  [SecurityConfigCategory.SUPER_ADMIN]: [
    {
      key: 'SUPER_ADMIN_EMAILS',
      required: false, // Can be database-driven
      validator: validateSuperAdminEmails,
      description: 'Super admin email addresses'
    }
  ] as ConfigSetting[],

  // External Services Configuration
  [SecurityConfigCategory.EXTERNAL_SERVICES]: [
    {
      key: 'MCP_RULES_BASE',
      required: true,
      validator: validateMcpRulesBase,
      description: 'MCP Rules Engine base URL'
    },
    {
      key: 'MCP_RULES_BASE_STG',
      required: false,
      validator: validateMcpRulesBaseStaging,
      description: 'MCP Rules Engine staging URL'
    },
    {
      key: 'GOOGLE_CLOUD_PROJECT',
      required: false,
      validator: validateGoogleCloudProject,
      description: 'Google Cloud project ID'
    }
  ] as ConfigSetting[],

  // Feature Flags Configuration
  [SecurityConfigCategory.FEATURE_FLAGS]: [
    {
      key: 'FEATURE_MCP_RULES_ENGINE',
      required: false,
      validator: validateBooleanFlag,
      description: 'Feature flag for MCP Rules Engine'
    }
  ] as ConfigSetting[]
} as const;

/**
 * Mask sensitive values for logging and display
 */
function maskSensitiveValue(key: string, value: string): string {
  const sensitivePatterns = [
    'key', 'secret', 'password', 'token', 'jwt'
  ];
  
  const isSensitive = sensitivePatterns.some(pattern => 
    key.toLowerCase().includes(pattern)
  );
  
  if (!isSensitive) {
    return value;
  }
  
  if (value.length <= 8) {
    return '****';
  }
  
  return `${value.substring(0, 4)}...${value.substring(value.length - 4)}`;
}

/**
 * Get environment variable value safely
 * Can be overridden for testing
 */
let getEnvValue = (key: string): string | undefined => {
  return process.env[key];
};

/**
 * Override getEnvValue for testing
 * @internal
 */
export function __setEnvValueGetter(getter: (key: string) => string | undefined) {
  getEnvValue = getter;
}

/**
 * Reset getEnvValue to default
 * @internal
 */
export function __resetEnvValueGetter() {
  getEnvValue = (key: string): string | undefined => {
    return process.env[key];
  };
}

/**
 * Generate a human-readable summary of security configuration audit results
 */
export function getSecurityConfigSummary(audit: SecurityConfigAudit): string {
  const lines: string[] = [];

  // Header
  lines.push('='.repeat(60));
  lines.push('SECURITY CONFIGURATION AUDIT SUMMARY');
  lines.push('='.repeat(60));
  lines.push('');

  // Overall status
  const statusIcon = audit.overallStatus === 'healthy' ? '✅' :
                    audit.overallStatus === 'warning' ? '⚠️' : '❌';
  lines.push(`Overall Status: ${statusIcon} ${audit.overallStatus.toUpperCase()}`);
  lines.push(`Timestamp: ${audit.timestamp}`);
  lines.push('');

  // Statistics
  lines.push('VALIDATION STATISTICS:');
  lines.push(`  Total Checks: ${audit.totalChecks}`);
  lines.push(`  Passed: ${audit.passedChecks}`);
  lines.push(`  Failed: ${audit.failedChecks}`);
  lines.push(`  Critical Issues: ${audit.criticalIssues}`);
  lines.push(`  High Issues: ${audit.highIssues}`);
  lines.push('');

  // Group results by category and severity
  const resultsByCategory: Record<string, ConfigValidationResult[]> = {};
  audit.results.forEach(result => {
    if (!resultsByCategory[result.category]) {
      resultsByCategory[result.category] = [];
    }
    resultsByCategory[result.category].push(result);
  });

  // Show failed checks by category
  const failedResults = audit.results.filter(r => !r.isValid);
  if (failedResults.length > 0) {
    lines.push('FAILED VALIDATIONS:');

    Object.entries(resultsByCategory).forEach(([category, results]) => {
      const failedInCategory = results.filter(r => !r.isValid);
      if (failedInCategory.length > 0) {
        lines.push(`\n  ${category.toUpperCase()}:`);
        failedInCategory.forEach(result => {
          const severityIcon = result.severity === ValidationSeverity.CRITICAL ? '🔴' :
                              result.severity === ValidationSeverity.HIGH ? '🟠' :
                              result.severity === ValidationSeverity.MEDIUM ? '🟡' : '🔵';
          lines.push(`    ${severityIcon} ${result.key}: ${result.message}`);
          if (result.recommendation) {
            lines.push(`       → ${result.recommendation}`);
          }
        });
      }
    });
  }

  // Show passed checks summary
  const passedResults = audit.results.filter(r => r.isValid);
  if (passedResults.length > 0) {
    lines.push('\nPASSED VALIDATIONS:');
    Object.entries(resultsByCategory).forEach(([category, results]) => {
      const passedInCategory = results.filter(r => r.isValid);
      if (passedInCategory.length > 0) {
        lines.push(`  ${category}: ${passedInCategory.length} checks passed`);
      }
    });
  }

  lines.push('');
  lines.push('='.repeat(60));

  return lines.join('\n');
}

/**
 * Create validation result
 */
function createValidationResult(
  key: string,
  category: SecurityConfigCategory,
  severity: ValidationSeverity,
  isValid: boolean,
  message: string,
  required: boolean,
  recommendation?: string,
  value?: string
): ConfigValidationResult {
  return {
    key,
    category,
    severity,
    isValid,
    message,
    recommendation,
    value: value ? maskSensitiveValue(key, value) : undefined,
    required
  };
}

/**
 * VALIDATOR FUNCTION IMPLEMENTATIONS
 */

/**
 * Validate Supabase URL
 */
function validateSupabaseUrl(value: string | undefined, key = 'NEXT_PUBLIC_SUPABASE_URL'): ConfigValidationResult {
  if (!value) {
    return createValidationResult(
      key,
      SecurityConfigCategory.AUTHENTICATION,
      ValidationSeverity.CRITICAL,
      false,
      'Supabase URL is required for authentication',
      true,
      'Set NEXT_PUBLIC_SUPABASE_URL to your Supabase project URL'
    );
  }

  if (!value.startsWith('https://')) {
    return createValidationResult(
      key,
      SecurityConfigCategory.AUTHENTICATION,
      ValidationSeverity.HIGH,
      false,
      'Supabase URL must use HTTPS for security',
      true,
      'Ensure your Supabase URL starts with https://',
      value
    );
  }

  if (!value.includes('.supabase.co')) {
    return createValidationResult(
      key,
      SecurityConfigCategory.AUTHENTICATION,
      ValidationSeverity.MEDIUM,
      false,
      'Supabase URL format appears invalid',
      true,
      'Verify your Supabase project URL format',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.AUTHENTICATION,
    ValidationSeverity.INFO,
    true,
    'Supabase URL is properly configured',
    true,
    undefined,
    value
  );
}

/**
 * Validate Supabase Anonymous Key
 */
function validateSupabaseAnonKey(value: string | undefined, key = 'NEXT_PUBLIC_SUPABASE_ANON_KEY'): ConfigValidationResult {

  if (!value) {
    return createValidationResult(
      key,
      SecurityConfigCategory.AUTHENTICATION,
      ValidationSeverity.CRITICAL,
      false,
      'Supabase anonymous key is required for client-side authentication',
      true,
      'Set NEXT_PUBLIC_SUPABASE_ANON_KEY from your Supabase project settings'
    );
  }

  if (value.length < 100) {
    return createValidationResult(
      key,
      SecurityConfigCategory.AUTHENTICATION,
      ValidationSeverity.HIGH,
      false,
      'Supabase anonymous key appears too short',
      true,
      'Verify your anonymous key from Supabase project settings',
      value
    );
  }

  if (!value.startsWith('eyJ')) {
    return createValidationResult(
      key,
      SecurityConfigCategory.AUTHENTICATION,
      ValidationSeverity.HIGH,
      false,
      'Supabase anonymous key format appears invalid (should be JWT)',
      true,
      'Ensure you are using the correct anonymous key from Supabase',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.AUTHENTICATION,
    ValidationSeverity.INFO,
    true,
    'Supabase anonymous key is properly configured',
    true,
    undefined,
    value
  );
}

/**
 * Validate Supabase Service Key
 */
function validateSupabaseServiceKey(value: string | undefined, key = 'SUPABASE_SERVICE_KEY'): ConfigValidationResult {

  if (!value) {
    return createValidationResult(
      key,
      SecurityConfigCategory.AUTHENTICATION,
      ValidationSeverity.CRITICAL,
      false,
      'Supabase service key is required for server-side operations',
      true,
      'Set SUPABASE_SERVICE_KEY from your Supabase project settings (service_role key)'
    );
  }

  if (value.length < 100) {
    return createValidationResult(
      key,
      SecurityConfigCategory.AUTHENTICATION,
      ValidationSeverity.HIGH,
      false,
      'Supabase service key appears too short',
      true,
      'Verify your service role key from Supabase project settings',
      value
    );
  }

  if (!value.startsWith('eyJ')) {
    return createValidationResult(
      key,
      SecurityConfigCategory.AUTHENTICATION,
      ValidationSeverity.HIGH,
      false,
      'Supabase service key format appears invalid (should be JWT)',
      true,
      'Ensure you are using the service_role key from Supabase',
      value
    );
  }

  // Check if it's accidentally the anon key (common mistake)
  const anonKey = getEnvValue('NEXT_PUBLIC_SUPABASE_ANON_KEY');
  if (anonKey && value === anonKey) {
    return createValidationResult(
      key,
      SecurityConfigCategory.AUTHENTICATION,
      ValidationSeverity.CRITICAL,
      false,
      'Service key appears to be the same as anonymous key - this is a security risk',
      true,
      'Use the service_role key, not the anon key for SUPABASE_SERVICE_KEY',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.AUTHENTICATION,
    ValidationSeverity.INFO,
    true,
    'Supabase service key is properly configured',
    true,
    undefined,
    value
  );
}

/**
 * Validate JWT Secret
 */
function validateJwtSecret(value: string | undefined, key = 'SUPABASE_JWT_SECRET'): ConfigValidationResult {

  if (!value) {
    return createValidationResult(
      key,
      SecurityConfigCategory.JWT,
      ValidationSeverity.CRITICAL,
      false,
      'JWT secret is required for token validation',
      true,
      'Set SUPABASE_JWT_SECRET from your Supabase project settings'
    );
  }

  // Check for common weak patterns first (more critical than length)
  if (value === 'your-jwt-secret' || value === 'secret' || value === 'jwt-secret') {
    return createValidationResult(
      key,
      SecurityConfigCategory.JWT,
      ValidationSeverity.CRITICAL,
      false,
      'JWT secret appears to be a default/example value',
      true,
      'Generate a secure random JWT secret',
      value
    );
  }

  if (value.length < 32) {
    return createValidationResult(
      key,
      SecurityConfigCategory.JWT,
      ValidationSeverity.HIGH,
      false,
      'JWT secret is too short (minimum 32 characters recommended)',
      true,
      'Use a longer, more secure JWT secret',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.JWT,
    ValidationSeverity.INFO,
    true,
    'JWT secret is properly configured',
    true,
    undefined,
    value
  );
}

/**
 * Validate Boolean Flag
 */
function validateBooleanFlag(value: string | undefined, key = 'BOOLEAN_FLAG'): ConfigValidationResult {

  if (!value) {
    return createValidationResult(
      key,
      SecurityConfigCategory.FEATURE_FLAGS,
      ValidationSeverity.INFO,
      true,
      'Boolean flag is not set (defaults to false)',
      false
    );
  }

  if (value !== 'true' && value !== 'false') {
    return createValidationResult(
      key,
      SecurityConfigCategory.FEATURE_FLAGS,
      ValidationSeverity.MEDIUM,
      false,
      'Boolean flag should be "true" or "false"',
      false,
      'Set the flag to either "true" or "false"',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.FEATURE_FLAGS,
    ValidationSeverity.INFO,
    true,
    `Boolean flag is properly set to ${value}`,
    false,
    undefined,
    value
  );
}

/**
 * Validate Database Host
 */
function validateDatabaseHost(value: string | undefined, key = 'DB_HOST', required = false): ConfigValidationResult {

  if (!value) {
    if (!required) {
      return createValidationResult(
        key,
        SecurityConfigCategory.DATABASE,
        ValidationSeverity.INFO,
        true,
        'Database host not set (optional for frontend - uses Supabase)',
        false,
        'Frontend uses Supabase for data access'
      );
    }
    return createValidationResult(
      key,
      SecurityConfigCategory.DATABASE,
      ValidationSeverity.CRITICAL,
      false,
      'Database host is required',
      true,
      'Set DB_HOST to your database server hostname'
    );
  }

  // Check for localhost in production
  if (process.env.NODE_ENV === 'production' && (value === 'localhost' || value === '127.0.0.1')) {
    return createValidationResult(
      key,
      SecurityConfigCategory.DATABASE,
      ValidationSeverity.HIGH,
      false,
      'Database host should not be localhost in production',
      true,
      'Use a proper database server hostname for production',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.DATABASE,
    ValidationSeverity.INFO,
    true,
    'Database host is configured',
    true,
    undefined,
    value
  );
}

/**
 * Validate Database Port
 */
function validateDatabasePort(value: string | undefined, key = 'DB_PORT', required = false): ConfigValidationResult {

  if (!value) {
    if (!required) {
      return createValidationResult(
        key,
        SecurityConfigCategory.DATABASE,
        ValidationSeverity.INFO,
        true,
        'Database port not set (optional for frontend - uses Supabase)',
        false,
        'Frontend uses Supabase for data access'
      );
    }
    return createValidationResult(
      key,
      SecurityConfigCategory.DATABASE,
      ValidationSeverity.CRITICAL,
      false,
      'Database port is required',
      true,
      'Set DB_PORT to your database port (usually 5432 for PostgreSQL)'
    );
  }

  const port = parseInt(value, 10);
  if (isNaN(port) || port < 1 || port > 65535) {
    return createValidationResult(
      key,
      SecurityConfigCategory.DATABASE,
      ValidationSeverity.HIGH,
      false,
      'Database port must be a valid port number (1-65535)',
      true,
      'Set DB_PORT to a valid port number',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.DATABASE,
    ValidationSeverity.INFO,
    true,
    'Database port is properly configured',
    true,
    undefined,
    value
  );
}

/**
 * Validate Database Name
 */
function validateDatabaseName(value: string | undefined, key = 'DB_NAME', required = false): ConfigValidationResult {

  if (!value) {
    if (!required) {
      return createValidationResult(
        key,
        SecurityConfigCategory.DATABASE,
        ValidationSeverity.INFO,
        true,
        'Database name not set (optional for frontend - uses Supabase)',
        false,
        'Frontend uses Supabase for data access'
      );
    }
    return createValidationResult(
      key,
      SecurityConfigCategory.DATABASE,
      ValidationSeverity.CRITICAL,
      false,
      'Database name is required',
      true,
      'Set DB_NAME to your database name'
    );
  }

  if (value.length < 1) {
    return createValidationResult(
      key,
      SecurityConfigCategory.DATABASE,
      ValidationSeverity.HIGH,
      false,
      'Database name cannot be empty',
      true,
      'Provide a valid database name',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.DATABASE,
    ValidationSeverity.INFO,
    true,
    'Database name is configured',
    true,
    undefined,
    value
  );
}

/**
 * Validate Database User
 */
function validateDatabaseUser(value: string | undefined, key = 'DB_USER', required = false): ConfigValidationResult {

  if (!value) {
    if (!required) {
      return createValidationResult(
        key,
        SecurityConfigCategory.DATABASE,
        ValidationSeverity.INFO,
        true,
        'Database user not set (optional for frontend - uses Supabase)',
        false,
        'Frontend uses Supabase for data access'
      );
    }
    return createValidationResult(
      key,
      SecurityConfigCategory.DATABASE,
      ValidationSeverity.CRITICAL,
      false,
      'Database user is required',
      true,
      'Set DB_USER to your database username'
    );
  }

  if (value === 'root' || value === 'admin') {
    return createValidationResult(
      key,
      SecurityConfigCategory.DATABASE,
      ValidationSeverity.MEDIUM,
      true,
      'Using privileged database user - consider using a dedicated application user',
      true,
      'Create a dedicated database user with minimal required privileges',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.DATABASE,
    ValidationSeverity.INFO,
    true,
    'Database user is configured',
    true,
    undefined,
    value
  );
}

/**
 * Validate Database Password
 */
function validateDatabasePassword(value: string | undefined, key = 'DB_PASSWORD', required = false): ConfigValidationResult {

  if (!value) {
    if (!required) {
      return createValidationResult(
        key,
        SecurityConfigCategory.DATABASE,
        ValidationSeverity.INFO,
        true,
        'Database password not set (optional for frontend - uses Supabase)',
        false,
        'Frontend uses Supabase for data access'
      );
    }
    return createValidationResult(
      key,
      SecurityConfigCategory.DATABASE,
      ValidationSeverity.CRITICAL,
      false,
      'Database password is required',
      true,
      'Set DB_PASSWORD to your database password'
    );
  }

  if (value.length < 8) {
    return createValidationResult(
      key,
      SecurityConfigCategory.DATABASE,
      ValidationSeverity.HIGH,
      false,
      'Database password is too short (minimum 8 characters recommended)',
      true,
      'Use a longer, more secure database password',
      value
    );
  }

  // Check for common weak passwords
  const weakPasswords = ['password', '123456', 'admin', 'root', 'postgres'];
  if (weakPasswords.includes(value.toLowerCase())) {
    return createValidationResult(
      key,
      SecurityConfigCategory.DATABASE,
      ValidationSeverity.CRITICAL,
      false,
      'Database password is too weak',
      true,
      'Use a strong, unique database password',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.DATABASE,
    ValidationSeverity.INFO,
    true,
    'Database password is configured',
    true,
    undefined,
    value
  );
}

/**
 * Validate OpenAI API Key
 */
function validateOpenAIApiKey(value: string | undefined, key = 'OPENAI_API_KEY', required = false): ConfigValidationResult {

  if (!value) {
    if (!required) {
      return createValidationResult(
        key,
        SecurityConfigCategory.API_KEYS,
        ValidationSeverity.INFO,
        true,
        'OpenAI API key not set (optional for frontend - backend handles AI calls)',
        false,
        'Frontend uses backend API for AI services'
      );
    }
    return createValidationResult(
      key,
      SecurityConfigCategory.API_KEYS,
      ValidationSeverity.CRITICAL,
      false,
      'OpenAI API key is required for AI services',
      true,
      'Set OPENAI_API_KEY from your OpenAI account'
    );
  }

  if (!value.startsWith('sk-')) {
    return createValidationResult(
      key,
      SecurityConfigCategory.API_KEYS,
      ValidationSeverity.HIGH,
      false,
      'OpenAI API key format appears invalid (should start with sk-)',
      true,
      'Verify your OpenAI API key format',
      value
    );
  }

  if (value.length < 40) {
    return createValidationResult(
      key,
      SecurityConfigCategory.API_KEYS,
      ValidationSeverity.HIGH,
      false,
      'OpenAI API key appears too short',
      true,
      'Verify your OpenAI API key',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.API_KEYS,
    ValidationSeverity.INFO,
    true,
    'OpenAI API key is properly configured',
    true,
    undefined,
    value
  );
}

/**
 * Validate Voyage API Key
 */
function validateVoyageApiKey(value: string | undefined, key = 'VOYAGE_API_KEY', required = false): ConfigValidationResult {

  if (!value) {
    if (!required) {
      return createValidationResult(
        key,
        SecurityConfigCategory.API_KEYS,
        ValidationSeverity.INFO,
        true,
        'Voyage API key not set (optional for frontend - backend handles embeddings)',
        false,
        'Frontend uses backend API for embedding services'
      );
    }
    return createValidationResult(
      key,
      SecurityConfigCategory.API_KEYS,
      ValidationSeverity.CRITICAL,
      false,
      'Voyage API key is required for embeddings',
      true,
      'Set VOYAGE_API_KEY from your Voyage AI account'
    );
  }

  if (value.length < 20) {
    return createValidationResult(
      key,
      SecurityConfigCategory.API_KEYS,
      ValidationSeverity.HIGH,
      false,
      'Voyage API key appears too short',
      true,
      'Verify your Voyage API key',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.API_KEYS,
    ValidationSeverity.INFO,
    true,
    'Voyage API key is properly configured',
    true,
    undefined,
    value
  );
}

/**
 * Validate Pinecone API Key
 */
function validatePineconeApiKey(value: string | undefined, key = 'PINECONE_API_KEY', required = false): ConfigValidationResult {

  if (!value) {
    if (!required) {
      return createValidationResult(
        key,
        SecurityConfigCategory.API_KEYS,
        ValidationSeverity.INFO,
        true,
        'Pinecone API key not set (optional for frontend - backend handles vector DB)',
        false,
        'Frontend uses backend API for vector database services'
      );
    }
    return createValidationResult(
      key,
      SecurityConfigCategory.API_KEYS,
      ValidationSeverity.CRITICAL,
      false,
      'Pinecone API key is required for vector database',
      true,
      'Set PINECONE_API_KEY from your Pinecone account'
    );
  }

  if (value.length < 20) {
    return createValidationResult(
      key,
      SecurityConfigCategory.API_KEYS,
      ValidationSeverity.HIGH,
      false,
      'Pinecone API key appears too short',
      true,
      'Verify your Pinecone API key',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.API_KEYS,
    ValidationSeverity.INFO,
    true,
    'Pinecone API key is properly configured',
    true,
    undefined,
    value
  );
}

/**
 * Validate CopilotKit Secret
 */
function validateCopilotKitSecret(value: string | undefined, key = 'CPK_ENDPOINT_SECRET', required = false): ConfigValidationResult {

  if (!value) {
    if (!required) {
      return createValidationResult(
        key,
        SecurityConfigCategory.API_KEYS,
        ValidationSeverity.INFO,
        true,
        'CopilotKit endpoint secret not set (optional - can be configured later)',
        false,
        'CopilotKit integration can be enabled later'
      );
    }
    return createValidationResult(
      key,
      SecurityConfigCategory.API_KEYS,
      ValidationSeverity.CRITICAL,
      false,
      'CopilotKit endpoint secret is required',
      true,
      'Set CPK_ENDPOINT_SECRET for CopilotKit integration'
    );
  }

  if (value.length < 16) {
    return createValidationResult(
      key,
      SecurityConfigCategory.API_KEYS,
      ValidationSeverity.HIGH,
      false,
      'CopilotKit secret is too short (minimum 16 characters recommended)',
      true,
      'Use a longer, more secure secret',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.API_KEYS,
    ValidationSeverity.INFO,
    true,
    'CopilotKit secret is properly configured',
    true,
    undefined,
    value
  );
}

/**
 * Validate LangSmith API Key
 */
function validateLangSmithApiKey(value: string | undefined, key = 'LANGSMITH_API_KEY'): ConfigValidationResult {

  if (!value) {
    return createValidationResult(
      key,
      SecurityConfigCategory.API_KEYS,
      ValidationSeverity.INFO,
      true,
      'LangSmith API key is not set (optional for monitoring)',
      false
    );
  }

  if (value.length < 20) {
    return createValidationResult(
      key,
      SecurityConfigCategory.API_KEYS,
      ValidationSeverity.MEDIUM,
      false,
      'LangSmith API key appears too short',
      false,
      'Verify your LangSmith API key',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.API_KEYS,
    ValidationSeverity.INFO,
    true,
    'LangSmith API key is properly configured',
    false,
    undefined,
    value
  );
}

/**
 * Validate Super Admin Emails
 */
function validateSuperAdminEmails(value: string | undefined, key = 'SUPER_ADMIN_EMAILS'): ConfigValidationResult {

  if (!value) {
    return createValidationResult(
      key,
      SecurityConfigCategory.SUPER_ADMIN,
      ValidationSeverity.MEDIUM,
      true,
      'Super admin emails not set in environment (may be database-driven)',
      false,
      'Configure super admin emails via environment or database'
    );
  }

  try {
    const emails = value.split(',').map(email => email.trim());

    if (emails.length === 0) {
      return createValidationResult(
        key,
        SecurityConfigCategory.SUPER_ADMIN,
        ValidationSeverity.HIGH,
        false,
        'No super admin emails configured',
        false,
        'Add at least one super admin email',
        value
      );
    }

    // Validate email formats
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const invalidEmails = emails.filter(email => !emailRegex.test(email));

    if (invalidEmails.length > 0) {
      return createValidationResult(
        key,
        SecurityConfigCategory.SUPER_ADMIN,
        ValidationSeverity.HIGH,
        false,
        `Invalid email format(s): ${invalidEmails.join(', ')}`,
        false,
        'Ensure all super admin emails are valid',
        value
      );
    }

    return createValidationResult(
      key,
      SecurityConfigCategory.SUPER_ADMIN,
      ValidationSeverity.INFO,
      true,
      `${emails.length} super admin email(s) configured`,
      false,
      undefined,
      value
    );
  } catch (_error) {
    return createValidationResult(
      key,
      SecurityConfigCategory.SUPER_ADMIN,
      ValidationSeverity.HIGH,
      false,
      'Error parsing super admin emails',
      false,
      'Check super admin email format (comma-separated)',
      value
    );
  }
}

/**
 * Validate MCP Rules Base URL
 */
function validateMcpRulesBase(value: string | undefined, key = 'MCP_RULES_BASE'): ConfigValidationResult {

  if (!value) {
    return createValidationResult(
      key,
      SecurityConfigCategory.EXTERNAL_SERVICES,
      ValidationSeverity.CRITICAL,
      false,
      'MCP Rules Engine base URL is required',
      true,
      'Set MCP_RULES_BASE to your MCP Rules Engine URL'
    );
  }

  if (!value.startsWith('https://')) {
    return createValidationResult(
      key,
      SecurityConfigCategory.EXTERNAL_SERVICES,
      ValidationSeverity.HIGH,
      false,
      'MCP Rules base URL must use HTTPS for security',
      true,
      'Ensure MCP Rules base URL starts with https://',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.EXTERNAL_SERVICES,
    ValidationSeverity.INFO,
    true,
    'MCP Rules base URL is properly configured',
    true,
    undefined,
    value
  );
}

/**
 * Validate MCP Rules Base Staging URL
 */
function validateMcpRulesBaseStaging(value: string | undefined, key = 'MCP_RULES_BASE_STG'): ConfigValidationResult {

  if (!value) {
    return createValidationResult(
      key,
      SecurityConfigCategory.EXTERNAL_SERVICES,
      ValidationSeverity.INFO,
      true,
      'MCP Rules staging URL is not set (optional)',
      false
    );
  }

  if (!value.startsWith('https://')) {
    return createValidationResult(
      key,
      SecurityConfigCategory.EXTERNAL_SERVICES,
      ValidationSeverity.MEDIUM,
      false,
      'MCP Rules staging URL should use HTTPS for security',
      false,
      'Ensure MCP Rules staging URL starts with https://',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.EXTERNAL_SERVICES,
    ValidationSeverity.INFO,
    true,
    'MCP Rules staging URL is properly configured',
    false,
    undefined,
    value
  );
}

/**
 * Validate Google Cloud Project
 */
function validateGoogleCloudProject(value: string | undefined, key = 'GOOGLE_CLOUD_PROJECT'): ConfigValidationResult {

  if (!value) {
    return createValidationResult(
      key,
      SecurityConfigCategory.EXTERNAL_SERVICES,
      ValidationSeverity.INFO,
      true,
      'Google Cloud project is not set (optional)',
      false
    );
  }

  // Google Cloud project IDs have specific format requirements
  if (!/^[a-z][a-z0-9-]{4,28}[a-z0-9]$/.test(value)) {
    return createValidationResult(
      key,
      SecurityConfigCategory.EXTERNAL_SERVICES,
      ValidationSeverity.MEDIUM,
      false,
      'Google Cloud project ID format appears invalid',
      false,
      'Verify your Google Cloud project ID format',
      value
    );
  }

  return createValidationResult(
    key,
    SecurityConfigCategory.EXTERNAL_SERVICES,
    ValidationSeverity.INFO,
    true,
    'Google Cloud project is properly configured',
    false,
    undefined,
    value
  );
}

/**
 * MAIN AUDIT FUNCTIONS
 */

/**
 * Perform comprehensive security configuration audit
 */
export async function performSecurityConfigAudit(): Promise<SecurityConfigAudit> {
  const timestamp = new Date().toISOString();
  const environment = process.env.NODE_ENV || 'development';
  const results: ConfigValidationResult[] = [];

  // Initialize summary counters
  const summary: SecurityConfigAudit['summary'] = {} as any;
  Object.values(SecurityConfigCategory).forEach(category => {
    summary[category] = {
      total: 0,
      passed: 0,
      failed: 0,
      criticalIssues: 0
    };
  });

  // Validate all registered configurations
  for (const [category, configs] of Object.entries(SECURITY_CONFIG_REGISTRY)) {
    const categoryKey = category as SecurityConfigCategory;

    for (const config of configs) {
      const envValue = getEnvValue(config.key);

      // Call validator with value, key, and required flag
      let result: ConfigValidationResult;
      if (config.validator.length > 2) {
        // Validator accepts value, key, and required parameters
        result = (config.validator as any)(envValue, config.key, config.required);
      } else if (config.validator.length > 1) {
        // Validator accepts key parameter
        result = (config.validator as any)(envValue, config.key);
      } else {
        // Validator only accepts value
        result = config.validator(envValue);
        // Override the key in the result
        result.key = config.key;
      }

      result.category = categoryKey;
      results.push(result);

      // Update summary
      summary[categoryKey].total++;
      if (result.isValid) {
        summary[categoryKey].passed++;
      } else {
        summary[categoryKey].failed++;
        if (result.severity === ValidationSeverity.CRITICAL) {
          summary[categoryKey].criticalIssues++;
        }
      }
    }
  }

  // Calculate overall statistics
  const totalChecks = results.length;
  const passedChecks = results.filter(r => r.isValid).length;
  const failedChecks = totalChecks - passedChecks;
  const criticalIssues = results.filter(r => r.severity === ValidationSeverity.CRITICAL && !r.isValid).length;
  const highIssues = results.filter(r => r.severity === ValidationSeverity.HIGH && !r.isValid).length;
  const mediumIssues = results.filter(r => r.severity === ValidationSeverity.MEDIUM && !r.isValid).length;
  const lowIssues = results.filter(r => r.severity === ValidationSeverity.LOW && !r.isValid).length;

  // Determine overall status
  let overallStatus: 'healthy' | 'warning' | 'critical';
  if (criticalIssues > 0) {
    overallStatus = 'critical';
  } else if (highIssues > 0 || failedChecks > totalChecks * 0.2) {
    overallStatus = 'warning';
  } else {
    overallStatus = 'healthy';
  }

  return {
    timestamp,
    environment,
    overallStatus,
    totalChecks,
    passedChecks,
    failedChecks,
    criticalIssues,
    highIssues,
    mediumIssues,
    lowIssues,
    results,
    summary
  };
}


