/**
 * Production configuration validator and setup checker
 */

import { getEnvironmentConfig, validateEnvironmentConfig } from './environment';

export interface ProductionCheckResult {
  category: string;
  name: string;
  status: 'pass' | 'warning' | 'fail';
  message: string;
  details?: Record<string, unknown>;
  fix?: string;
}

export interface ProductionReadinessReport {
  overall: 'ready' | 'warnings' | 'not_ready';
  score: number;
  checks: ProductionCheckResult[];
  summary: {
    passed: number;
    warnings: number;
    failed: number;
    total: number;
  };
  timestamp: string;
}

/**
 * Production configuration validator
 */
export class ProductionValidator {
  private config = getEnvironmentConfig();

  /**
   * Run all production readiness checks
   */
  async validateProductionReadiness(): Promise<ProductionReadinessReport> {
    const checks: ProductionCheckResult[] = [];

    // Environment configuration checks
    checks.push(...this.checkEnvironmentConfiguration());
    
    // Security checks
    checks.push(...this.checkSecurityConfiguration());
    
    // Performance checks
    checks.push(...this.checkPerformanceConfiguration());
    
    // Monitoring checks
    checks.push(...this.checkMonitoringConfiguration());
    
    // Multi-country checks
    checks.push(...this.checkMultiCountryConfiguration());
    
    // Database checks
    checks.push(...await this.checkDatabaseConfiguration());
    
    // API checks
    checks.push(...await this.checkApiConfiguration());

    // Calculate summary
    const passed = checks.filter(c => c.status === 'pass').length;
    const warnings = checks.filter(c => c.status === 'warning').length;
    const failed = checks.filter(c => c.status === 'fail').length;
    const total = checks.length;

    let overall: ProductionReadinessReport['overall'] = 'ready';
    if (failed > 0) {
      overall = 'not_ready';
    } else if (warnings > 0) {
      overall = 'warnings';
    }

    const score = Math.round(((passed + warnings * 0.5) / total) * 100);

    return {
      overall,
      score,
      checks,
      summary: { passed, warnings, failed, total },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Check environment configuration
   */
  private checkEnvironmentConfiguration(): ProductionCheckResult[] {
    const checks: ProductionCheckResult[] = [];
    const validation = validateEnvironmentConfig(this.config);

    // Environment validation
    checks.push({
      category: 'Environment',
      name: 'Environment Variables',
      status: validation.valid ? 'pass' : 'fail',
      message: validation.valid 
        ? 'All required environment variables are configured'
        : `Missing required environment variables: ${validation.errors.map(e => e.variable).join(', ')}`,
      details: {
        errors: validation.errors.length,
        warnings: validation.warnings.length
      },
      fix: validation.errors.length > 0 
        ? 'Set the missing environment variables in your deployment configuration'
        : undefined
    });

    // Node environment check
    checks.push({
      category: 'Environment',
      name: 'Node Environment',
      status: this.config.nodeEnv === 'production' ? 'pass' : 'warning',
      message: `NODE_ENV is set to "${this.config.nodeEnv}"`,
      details: { nodeEnv: this.config.nodeEnv },
      fix: this.config.nodeEnv !== 'production' 
        ? 'Set NODE_ENV=production for production deployment'
        : undefined
    });

    // App URL check
    checks.push({
      category: 'Environment',
      name: 'Application URL',
      status: this.config.appUrl.includes('localhost') ? 'warning' : 'pass',
      message: `App URL is set to "${this.config.appUrl}"`,
      details: { appUrl: this.config.appUrl },
      fix: this.config.appUrl.includes('localhost')
        ? 'Set NEXT_PUBLIC_APP_URL to your production domain'
        : undefined
    });

    return checks;
  }

  /**
   * Check security configuration
   */
  private checkSecurityConfiguration(): ProductionCheckResult[] {
    const checks: ProductionCheckResult[] = [];

    // JWT secret check
    checks.push({
      category: 'Security',
      name: 'JWT Secret',
      status: this.config.jwtSecret ? 'pass' : 'fail',
      message: this.config.jwtSecret ? 'JWT secret is configured' : 'JWT secret is not configured',
      fix: !this.config.jwtSecret ? 'Set JWT_SECRET environment variable' : undefined
    });

    // Super admin emails check
    checks.push({
      category: 'Security',
      name: 'Super Admin Configuration',
      status: this.config.superAdminEmails.length > 0 ? 'pass' : 'warning',
      message: `${this.config.superAdminEmails.length} super admin email(s) configured`,
      details: { count: this.config.superAdminEmails.length },
      fix: this.config.superAdminEmails.length === 0 
        ? 'Set SUPER_ADMIN_EMAILS environment variable'
        : undefined
    });

    // Stripe webhook secret check
    checks.push({
      category: 'Security',
      name: 'Stripe Webhook Secret',
      status: this.config.stripeWebhookSecret ? 'pass' : 'fail',
      message: this.config.stripeWebhookSecret 
        ? 'Stripe webhook secret is configured' 
        : 'Stripe webhook secret is not configured',
      fix: !this.config.stripeWebhookSecret 
        ? 'Set STRIPE_WEBHOOK_SECRET environment variable'
        : undefined
    });

    return checks;
  }

  /**
   * Check performance configuration
   */
  private checkPerformanceConfiguration(): ProductionCheckResult[] {
    const checks: ProductionCheckResult[] = [];

    // API timeout check
    checks.push({
      category: 'Performance',
      name: 'API Timeout',
      status: this.config.apiTimeout >= 10000 && this.config.apiTimeout <= 60000 ? 'pass' : 'warning',
      message: `API timeout is set to ${this.config.apiTimeout}ms`,
      details: { timeout: this.config.apiTimeout },
      fix: this.config.apiTimeout < 10000 || this.config.apiTimeout > 60000
        ? 'Set API_TIMEOUT between 10000 and 60000 milliseconds'
        : undefined
    });

    // Performance monitoring check
    checks.push({
      category: 'Performance',
      name: 'Performance Monitoring',
      status: this.config.enablePerformanceMonitoring ? 'pass' : 'warning',
      message: `Performance monitoring is ${this.config.enablePerformanceMonitoring ? 'enabled' : 'disabled'}`,
      details: { enabled: this.config.enablePerformanceMonitoring },
      fix: !this.config.enablePerformanceMonitoring
        ? 'Set NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true'
        : undefined
    });

    return checks;
  }

  /**
   * Check monitoring configuration
   */
  private checkMonitoringConfiguration(): ProductionCheckResult[] {
    const checks: ProductionCheckResult[] = [];

    // Error reporting check
    checks.push({
      category: 'Monitoring',
      name: 'Error Reporting',
      status: this.config.enableErrorReporting ? 'pass' : 'warning',
      message: `Error reporting is ${this.config.enableErrorReporting ? 'enabled' : 'disabled'}`,
      details: { enabled: this.config.enableErrorReporting },
      fix: !this.config.enableErrorReporting
        ? 'Set NEXT_PUBLIC_ENABLE_ERROR_REPORTING=true for production'
        : undefined
    });

    // Health checks check
    checks.push({
      category: 'Monitoring',
      name: 'Health Checks',
      status: this.config.enableHealthChecks ? 'pass' : 'warning',
      message: `Health checks are ${this.config.enableHealthChecks ? 'enabled' : 'disabled'}`,
      details: { enabled: this.config.enableHealthChecks },
      fix: !this.config.enableHealthChecks
        ? 'Set ENABLE_HEALTH_CHECKS=true'
        : undefined
    });

    // Debug mode check (should be disabled in production)
    checks.push({
      category: 'Monitoring',
      name: 'Debug Mode',
      status: !this.config.enableDebugMode ? 'pass' : 'warning',
      message: `Debug mode is ${this.config.enableDebugMode ? 'enabled' : 'disabled'}`,
      details: { enabled: this.config.enableDebugMode },
      fix: this.config.enableDebugMode
        ? 'Set NEXT_PUBLIC_DEBUG_MODE=false for production'
        : undefined
    });

    return checks;
  }

  /**
   * Check multi-country configuration
   */
  private checkMultiCountryConfiguration(): ProductionCheckResult[] {
    const checks: ProductionCheckResult[] = [];

    // Supported countries check
    checks.push({
      category: 'Multi-Country',
      name: 'Supported Countries',
      status: this.config.supportedCountries.length > 0 ? 'pass' : 'warning',
      message: `${this.config.supportedCountries.length} countries configured`,
      details: { 
        countries: this.config.supportedCountries,
        default: this.config.defaultCountry
      },
      fix: this.config.supportedCountries.length === 0
        ? 'Set NEXT_PUBLIC_SUPPORTED_COUNTRIES environment variable'
        : undefined
    });

    // Supported currencies check
    checks.push({
      category: 'Multi-Country',
      name: 'Supported Currencies',
      status: this.config.supportedCurrencies.length > 0 ? 'pass' : 'warning',
      message: `${this.config.supportedCurrencies.length} currencies configured`,
      details: { 
        currencies: this.config.supportedCurrencies,
        default: this.config.defaultCurrency
      },
      fix: this.config.supportedCurrencies.length === 0
        ? 'Set NEXT_PUBLIC_SUPPORTED_CURRENCIES environment variable'
        : undefined
    });

    return checks;
  }

  /**
   * Check database configuration
   */
  private async checkDatabaseConfiguration(): Promise<ProductionCheckResult[]> {
    const checks: ProductionCheckResult[] = [];

    // Supabase URL check
    checks.push({
      category: 'Database',
      name: 'Supabase URL',
      status: this.config.supabaseUrl && !this.config.supabaseUrl.includes('localhost') ? 'pass' : 'fail',
      message: this.config.supabaseUrl 
        ? `Supabase URL is configured: ${this.config.supabaseUrl.substring(0, 30)}...`
        : 'Supabase URL is not configured',
      fix: !this.config.supabaseUrl || this.config.supabaseUrl.includes('localhost')
        ? 'Set NEXT_PUBLIC_SUPABASE_URL to your production Supabase URL'
        : undefined
    });

    // Supabase keys check
    checks.push({
      category: 'Database',
      name: 'Supabase Keys',
      status: this.config.supabaseAnonKey && this.config.supabaseServiceKey ? 'pass' : 'fail',
      message: `Supabase keys configured: anon=${!!this.config.supabaseAnonKey}, service=${!!this.config.supabaseServiceKey}`,
      fix: !this.config.supabaseAnonKey || !this.config.supabaseServiceKey
        ? 'Set NEXT_PUBLIC_SUPABASE_ANON_KEY and SUPABASE_SERVICE_KEY'
        : undefined
    });

    return checks;
  }

  /**
   * Check API configuration
   */
  private async checkApiConfiguration(): Promise<ProductionCheckResult[]> {
    const checks: ProductionCheckResult[] = [];

    // Stripe configuration check
    checks.push({
      category: 'API',
      name: 'Stripe Configuration',
      status: this.config.stripePublishableKey && this.config.stripeSecretKey ? 'pass' : 'fail',
      message: `Stripe keys configured: publishable=${!!this.config.stripePublishableKey}, secret=${!!this.config.stripeSecretKey}`,
      fix: !this.config.stripePublishableKey || !this.config.stripeSecretKey
        ? 'Set NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY and STRIPE_SECRET_KEY'
        : undefined
    });

    // API base URL check
    checks.push({
      category: 'API',
      name: 'API Base URL',
      status: this.config.apiBaseUrl ? 'pass' : 'warning',
      message: `API base URL is set to "${this.config.apiBaseUrl}"`,
      details: { baseUrl: this.config.apiBaseUrl }
    });

    return checks;
  }

  /**
   * Generate production deployment checklist
   */
  generateDeploymentChecklist(): {
    category: string;
    items: {
      task: string;
      completed: boolean;
      required: boolean;
    }[];
  }[] {
    return [
      {
        category: 'Environment Configuration',
        items: [
          { task: 'Set NODE_ENV=production', completed: this.config.nodeEnv === 'production', required: true },
          { task: 'Configure production app URL', completed: !this.config.appUrl.includes('localhost'), required: true },
          { task: 'Set JWT secret', completed: !!this.config.jwtSecret, required: true },
          { task: 'Configure super admin emails', completed: this.config.superAdminEmails.length > 0, required: false },
        ]
      },
      {
        category: 'Database Configuration',
        items: [
          { task: 'Configure Supabase production URL', completed: !!this.config.supabaseUrl && !this.config.supabaseUrl.includes('localhost'), required: true },
          { task: 'Set Supabase anon key', completed: !!this.config.supabaseAnonKey, required: true },
          { task: 'Set Supabase service key', completed: !!this.config.supabaseServiceKey, required: true },
        ]
      },
      {
        category: 'Stripe Configuration',
        items: [
          { task: 'Set Stripe publishable key', completed: !!this.config.stripePublishableKey, required: true },
          { task: 'Set Stripe secret key', completed: !!this.config.stripeSecretKey, required: true },
          { task: 'Set Stripe webhook secret', completed: !!this.config.stripeWebhookSecret, required: true },
        ]
      },
      {
        category: 'Monitoring & Security',
        items: [
          { task: 'Enable error reporting', completed: this.config.enableErrorReporting, required: false },
          { task: 'Enable health checks', completed: this.config.enableHealthChecks, required: false },
          { task: 'Disable debug mode', completed: !this.config.enableDebugMode, required: true },
          { task: 'Enable performance monitoring', completed: this.config.enablePerformanceMonitoring, required: false },
        ]
      },
      {
        category: 'Multi-Country Setup',
        items: [
          { task: 'Configure supported countries', completed: this.config.supportedCountries.length > 0, required: false },
          { task: 'Configure supported currencies', completed: this.config.supportedCurrencies.length > 0, required: false },
          { task: 'Set default country', completed: !!this.config.defaultCountry, required: false },
          { task: 'Set default currency', completed: !!this.config.defaultCurrency, required: false },
        ]
      }
    ];
  }
}

// Export singleton instance
export const productionValidator = new ProductionValidator();
