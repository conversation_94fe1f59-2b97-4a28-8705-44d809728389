/**
 * Structured Extraction Service
 * Integrates LangExtract with medical records storage and processing
 */

import { supabase } from '@/lib/supabase/client';
import { langExtractService } from './langextract-service';
import type { 
  StructuredExtractions,
  MedicationRow,
  RadiologyRow,
  ProblemRow,
  FollowUpCue,
  ProcessedDocument
} from '@/types/medical-records';
import type {
  StoredExtractionParams,
  StoredExtractionResponse,
  GetExtractionsParams,
  CreateFollowupTasksParams,
  MedicationRowData,
  RadiologyRowData,
  ProblemRowData,
  FollowupCueRowData,
  InsertMedicationRowParams,
  InsertRadiologyRowParams,
  InsertProblemRowParams,
  InsertFollowupCueParams,
  SupabaseSingleResponse,
  SupabaseMultiResponse,
  SupabaseRpcResponse,
  UserTenantProfile
} from '@/types/medical-database';
import {
  isMedicationRowData,
  isRadiologyRowData,
  isProblemRowData,
  isFollowupCueRowData,
  isUserTenantProfile,
  isValidExtractionResponse
} from '@/types/medical-database';
import {
  MedicalErrorFactory,
  DatabaseError,
  TenantIsolationError,
  DataValidationError,
  ProcessingError,
  HIPAAViolationError
} from '@/types/medical-errors';

export class StructuredExtractionService {
  private supabase = supabase;

  /**
   * Process a medical document through LangExtract and store results
   */
  async processDocumentExtractions(
    matterId: string,
    documentId: string,
    processedDoc: ProcessedDocument,
    options?: {
      modelId?: string;
      storeReviewHtml?: boolean;
    }
  ): Promise<StructuredExtractions> {
    try {
      // Get tenant info from current user
      const { data: { user }, error: userError } = await this.supabase.auth.getUser();
      if (userError || !user) {
        throw new Error('Authentication required');
      }

      const { data: userProfile, error: profileError }: SupabaseSingleResponse<UserTenantProfile> = await this.supabase
        .from('users')
        .select('tenant_id, id')
        .eq('id', user.id)
        .single();

      if (profileError) {
        throw MedicalErrorFactory.fromSupabaseError({
          ...profileError,
          details: profileError.details || undefined
        }, 'select', 'users');
      }

      if (!userProfile || !isUserTenantProfile(userProfile)) {
        throw new TenantIsolationError('User tenant not found or invalid', user.id);
      }

      // Process all extraction types through LangExtract
      const extractions = await langExtractService.processAllExtractions(
        matterId,
        documentId,
        processedDoc.extractedText,
        processedDoc.processingMetadata, // Assuming this contains DocAI data
        {
          modelId: options?.modelId || 'gemini-2.5-flash',
          includeReviewHtml: options?.storeReviewHtml !== false,
        }
      );

      // Store the main extraction record
      const extractionId = await this.storeMainExtraction(
        userProfile.tenant_id,
        matterId,
        documentId,
        extractions
      );

      // Store individual rows in separate tables
      await Promise.all([
        this.storeMedicationRows(userProfile.tenant_id, extractionId, extractions.medications),
        this.storeRadiologyRows(userProfile.tenant_id, extractionId, extractions.radiology),
        this.storeProblemRows(userProfile.tenant_id, extractionId, extractions.problems),
        this.storeFollowupCues(userProfile.tenant_id, extractionId, extractions.followups),
      ]);

      // Store review HTML if provided
      if (extractions.reviewHtmlUri && options?.storeReviewHtml !== false) {
        await this.storeReviewHtml(extractionId, extractions.reviewHtmlUri);
      }

      return extractions;
    } catch (error) {
      console.error('Structured extraction processing failed:', error);
      throw MedicalErrorFactory.fromError(error, { matterId, documentId, operation: 'processDocumentExtractions' });
    }
  }

  /**
   * Store main extraction record using database function
   */
  private async storeMainExtraction(
    tenantId: string,
    matterId: string,
    documentId: string,
    extractions: StructuredExtractions
  ): Promise<string> {
    // Find the medical record ID
    const { data: medicalRecord } = await this.supabase
      .from('medical_records')
      .select('id')
      .eq('case_document_id', documentId)
      .eq('tenant_id', tenantId)
      .single();

    if (!medicalRecord) {
      throw new Error('Medical record not found for document');
    }

    // Store all task types in a single record
    const allExtractions = {
      medications: extractions.medications,
      radiology: extractions.radiology,
      problems: extractions.problems,
      followups: extractions.followups,
    };

    const { data, error } = await this.supabase.rpc('store_structured_extractions' as any, {
      p_tenant_id: tenantId,
      p_matter_id: matterId,
      p_medical_record_id: (medicalRecord as any).id,
      p_task_type: 'medications', // We'll use this as the primary type
      p_extractions_data: allExtractions,
      p_confidence_score: extractions.confidence,
      p_review_html_uri: extractions.reviewHtmlUri,
    });

    if (error) {
      throw new Error(`Failed to store extraction: ${error.message}`);
    }

    return data as string;
  }

  /**
   * Store medication rows
   */
  private async storeMedicationRows(
    tenantId: string,
    extractionId: string,
    medications: MedicationRow[]
  ): Promise<void> {
    if (medications.length === 0) return;

    const rows = medications.map(med => ({
      tenant_id: tenantId,
      extraction_id: extractionId,
      drug: med.drug,
      dose: med.dose,
      route: med.route,
      frequency: med.frequency,
      start_date: med.startDate,
      end_date: med.endDate,
      evidence: med.evidence,
      confidence: med.evidence[0]?.quote ? 0.85 : 0.7, // Simple confidence based on evidence
    }));

    const { error } = await this.supabase
      .from('medication_rows')
      .insert(rows);

    if (error) {
      throw new Error(`Failed to store medication rows: ${error.message}`);
    }
  }

  /**
   * Store radiology rows
   */
  private async storeRadiologyRows(
    tenantId: string,
    extractionId: string,
    radiology: RadiologyRow[]
  ): Promise<void> {
    if (radiology.length === 0) return;

    const rows = radiology.map(rad => ({
      tenant_id: tenantId,
      extraction_id: extractionId,
      study: rad.study,
      study_date: rad.date,
      body_part: rad.bodyPart,
      finding: rad.finding,
      impression: rad.impression,
      evidence: rad.evidence,
      confidence: rad.evidence[0]?.quote ? 0.85 : 0.7,
    }));

    const { error } = await this.supabase
      .from('radiology_rows')
      .insert(rows);

    if (error) {
      throw new Error(`Failed to store radiology rows: ${error.message}`);
    }
  }

  /**
   * Store problem rows
   */
  private async storeProblemRows(
    tenantId: string,
    extractionId: string,
    problems: ProblemRow[]
  ): Promise<void> {
    if (problems.length === 0) return;

    const rows = problems.map(prob => ({
      tenant_id: tenantId,
      extraction_id: extractionId,
      term: prob.term,
      evidence: prob.evidence,
      confidence: prob.evidence[0]?.quote ? 0.85 : 0.7,
    }));

    const { error } = await this.supabase
      .from('problem_rows')
      .insert(rows);

    if (error) {
      throw new Error(`Failed to store problem rows: ${error.message}`);
    }
  }

  /**
   * Store follow-up cues
   */
  private async storeFollowupCues(
    tenantId: string,
    extractionId: string,
    followups: FollowUpCue[]
  ): Promise<void> {
    if (followups.length === 0) return;

    const rows = followups.map(followup => ({
      tenant_id: tenantId,
      extraction_id: extractionId,
      cue: followup.cue,
      target: followup.target,
      due_date_guess: followup.dueDateGuess,
      evidence: followup.evidence,
      confidence: followup.evidence[0]?.quote ? 0.85 : 0.7,
      followup_task_created: false,
    }));

    const { error } = await this.supabase
      .from('followup_cues')
      .insert(rows);

    if (error) {
      throw new Error(`Failed to store followup cues: ${error.message}`);
    }
  }

  /**
   * Store review HTML artifact
   */
  private async storeReviewHtml(extractionId: string, htmlContent: string): Promise<void> {
    // In a real implementation, this would upload to GCS and store the URI
    // For now, we'll update the extraction record with the HTML content
    const { error } = await this.supabase
      .from('structured_extractions')
      .update({ review_html_uri: `data:text/html;base64,${btoa(htmlContent)}` })
      .eq('id', extractionId);

    if (error) {
      console.error('Failed to store review HTML:', error);
    }
  }

  /**
   * Get structured extractions for a matter
   */
  async getExtractionsForMatter(matterId: string): Promise<{
    medications: MedicationRow[];
    radiology: RadiologyRow[];
    problems: ProblemRow[];
    followups: FollowUpCue[];
  }> {
    try {
      // Get all extractions for the matter
      const { data: extractions, error } = await this.supabase.rpc(
        'get_structured_extractions_for_matter' as any,
        { p_matter_id: matterId }
      );

      if (error) {
        throw new Error(`Failed to get extractions: ${error.message}`);
      }

      // Get individual rows
      const [medicationsResult, radiologyResult, problemsResult, followupsResult] = await Promise.all([
        this.getMedicationsForMatter(matterId),
        this.getRadiologyForMatter(matterId),
        this.getProblemsForMatter(matterId),
        this.getFollowupsForMatter(matterId),
      ]);

      return {
        medications: medicationsResult,
        radiology: radiologyResult,
        problems: problemsResult,
        followups: followupsResult,
      };
    } catch (error) {
      console.error('Failed to get extractions for matter:', error);
      throw error;
    }
  }

  /**
   * Get medications for a matter
   */
  private async getMedicationsForMatter(matterId: string): Promise<MedicationRow[]> {
    const { data, error } = await this.supabase
      .from('medication_rows')
      .select(`
        *,
        structured_extractions!inner(matter_id)
      `)
      .eq('structured_extractions.matter_id', matterId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to get medications: ${error.message}`);
    }

    return data.map((row: any) => ({
      drug: row.drug,
      dose: row.dose,
      route: row.route,
      frequency: row.frequency,
      startDate: row.start_date,
      endDate: row.end_date,
      evidence: row.evidence,
    }));
  }

  /**
   * Get radiology for a matter
   */
  private async getRadiologyForMatter(matterId: string): Promise<RadiologyRow[]> {
    const { data, error } = await this.supabase
      .from('radiology_rows')
      .select(`
        *,
        structured_extractions!inner(matter_id)
      `)
      .eq('structured_extractions.matter_id', matterId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to get radiology: ${error.message}`);
    }

    return data.map((row: any) => ({
      study: row.study,
      date: row.study_date,
      bodyPart: row.body_part,
      finding: row.finding,
      impression: row.impression,
      evidence: row.evidence,
    }));
  }

  /**
   * Get problems for a matter
   */
  private async getProblemsForMatter(matterId: string): Promise<ProblemRow[]> {
    const { data, error } = await this.supabase
      .from('problem_rows')
      .select(`
        *,
        structured_extractions!inner(matter_id)
      `)
      .eq('structured_extractions.matter_id', matterId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to get problems: ${error.message}`);
    }

    return data.map((row: any) => ({
      term: row.term,
      evidence: row.evidence,
    }));
  }

  /**
   * Get follow-ups for a matter
   */
  private async getFollowupsForMatter(matterId: string): Promise<FollowUpCue[]> {
    const { data, error } = await this.supabase
      .from('followup_cues')
      .select(`
        *,
        structured_extractions!inner(matter_id)
      `)
      .eq('structured_extractions.matter_id', matterId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to get followups: ${error.message}`);
    }

    return data.map((row: any) => ({
      cue: row.cue,
      target: row.target,
      dueDateGuess: row.due_date_guess,
      evidence: row.evidence,
    }));
  }

  /**
   * Create provider follow-up tasks from cues
   */
  async createFollowupTasks(matterId: string): Promise<number> {
    const { data, error } = await this.supabase.rpc(
      'create_followup_tasks_from_cues' as any,
      { p_matter_id: matterId }
    );

    if (error) {
      throw new Error(`Failed to create followup tasks: ${error.message}`);
    }

    return (data as number) || 0;
  }
}

// Export singleton instance
export const structuredExtractionService = new StructuredExtractionService();