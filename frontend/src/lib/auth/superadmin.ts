import type { NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getEnvironmentConfig } from '@/config/env';

interface AuthResult {
  success: boolean;
  user?: {
    id: string;
    email?: string;
    [key: string]: unknown;
  };
  error?: string;
}

/**
 * Check if the current user has superadmin permissions
 */
export async function checkSuperAdminAuth(): Promise<AuthResult> {
  try {
    const supabase = await createClient();
    
    // Get the current user from the session
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return {
        success: false,
        error: 'Authentication required'
      };
    }

    // Check if user email is in the superadmin list
    const config = getEnvironmentConfig();
    const superAdminEmails = config.superAdminEmails;
    
    if (!superAdminEmails.includes(user.email || '')) {
      return {
        success: false,
        error: 'Superadmin access required'
      };
    }

    return {
      success: true,
      user: {
        email: user.email,
        ...user
      }
    };

  } catch (error) {
    console.error('Error checking superadmin auth:', error);
    return {
      success: false,
      error: 'Authentication check failed'
    };
  }
}

/**
 * Middleware function to protect superadmin routes
 */
export function withSuperAdminAuth(handler: (request: NextRequest, ...args: unknown[]) => Promise<Response>) {
  return async (request: NextRequest, ...args: unknown[]): Promise<Response> => {
    const authResult = await checkSuperAdminAuth();
    
    if (!authResult.success) {
      return new Response(
        JSON.stringify({ error: authResult.error }),
        { 
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    return handler(request, ...args);
  };
}

/**
 * Rate limiting for API calls
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function withRateLimit(
  handler: (request: NextRequest, ...args: unknown[]) => Promise<Response>,
  limit = 10,
  windowMs = 60000 // 1 minute
) {
  return async (request: NextRequest, ...args: unknown[]): Promise<Response> => {
    const ip = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean up old entries
    const entries = Array.from(rateLimitMap.entries());
    for (const [key, value] of entries) {
      if (value.resetTime < windowStart) {
        rateLimitMap.delete(key);
      }
    }

    // Check current rate limit
    const current = rateLimitMap.get(ip);
    if (current && current.count >= limit && current.resetTime > windowStart) {
      return new Response(
        JSON.stringify({ 
          error: 'Rate limit exceeded',
          retryAfter: Math.ceil((current.resetTime - now) / 1000)
        }),
        { 
          status: 429,
          headers: { 
            'Content-Type': 'application/json',
            'Retry-After': Math.ceil((current.resetTime - now) / 1000).toString()
          }
        }
      );
    }

    // Update rate limit
    if (current) {
      current.count++;
    } else {
      rateLimitMap.set(ip, { count: 1, resetTime: now + windowMs });
    }

    return handler(request, ...args);
  };
}

/**
 * Error handling wrapper for API routes
 */
export function withErrorHandling(
  handler: (request: NextRequest, ...args: unknown[]) => Promise<Response>
) {
  return async (request: NextRequest, ...args: unknown[]): Promise<Response> => {
    try {
      return await handler(request, ...args);
    } catch (error) {
      console.error('API route error:', error);
      
      return new Response(
        JSON.stringify({ 
          error: 'Internal server error',
          message: error instanceof Error ? error.message : 'Unknown error'
        }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  };
}
