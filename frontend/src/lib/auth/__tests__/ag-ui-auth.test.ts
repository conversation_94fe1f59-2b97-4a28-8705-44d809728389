/**
 * AG-UI Authentication Tests
 * 
 * These tests verify the authentication integration with CopilotKit AG-UI.
 * They test JWT generation, validation, tenant isolation, and error handling.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import type { AuthContext } from '../ag-ui-auth';
import { getCurrentAuthContext, generateCopilotJWT, getCopilotAuthHeader, getThreadComponents } from '../ag-ui-auth';
import { jwtVerify } from 'jose';

// Mock Supabase client
vi.mock('@/lib/supabase/server', () => ({
  createClient: () => ({
    auth: {
      getSession: vi.fn(),
    },
  }),
}));

// Mock cookies
vi.mock('next/headers', () => ({
  cookies: () => ({
    get: vi.fn(),
  }),
}));

// Mock environment variables
const mockEnv = {
  SUPABASE_JWT_SECRET: 'test-jwt-secret',
};

describe('AG-UI Authentication', () => {
  beforeEach(() => {
    // Setup environment variables
    process.env = { ...process.env, ...mockEnv };
    
    // Reset all mocks before each test
    vi.resetAllMocks();
  });

  afterEach(() => {
    // Clean up
    vi.restoreAllMocks();
  });

  describe('getCurrentAuthContext', () => {
    it('should return null when no session exists', async () => {
      // Setup mock to return no session
      const mockGetSession = vi.fn().mockResolvedValue({ 
        _data: { session: null },
        error: null,
      });
      
      // Override the mock implementation
      const createClient = require('@/lib/supabase/server').createClient;
      createClient().auth.getSession = mockGetSession;
      
      // Execute the function
      const result = await getCurrentAuthContext();
      
      // Verify the result
      expect(result).toBeNull();
      expect(mockGetSession).toHaveBeenCalledTimes(1);
    });

    it('should return auth context when session exists', async () => {
      // Setup mock to return a session with user
      const mockUser = {
        id: 'test-user-id',
        email: '<EMAIL>',
        user_metadata: {
          organization_id: 'test-org-id',
          role: 'admin',
        },
      };
      
      const mockGetSession = vi.fn().mockResolvedValue({ 
        data: { 
          session: {
            user: mockUser,
          },
        },
        error: null,
      });
      
      // Override the mock implementation
      const createClient = require('@/lib/supabase/server').createClient;
      createClient().auth.getSession = mockGetSession;
      
      // Execute the function
      const result = await getCurrentAuthContext();
      
      // Verify the result matches expected auth context
      expect(result).toEqual({
        userId: 'test-user-id',
        organizationId: 'test-org-id',
        role: 'admin',
        tenantId: 'test-org-id',
        authenticated: true,
        email: '<EMAIL>',
      });
    });

    it('should handle errors gracefully', async () => {
      // Setup mock to throw an error
      const mockGetSession = vi.fn().mockRejectedValue(new Error('Auth error'));
      
      // Override the mock implementation
      const createClient = require('@/lib/supabase/server').createClient;
      createClient().auth.getSession = mockGetSession;
      
      // Execute the function
      const result = await getCurrentAuthContext();
      
      // Verify error handling
      expect(result).toBeNull();
    });
  });

  describe('generateCopilotJWT', () => {
    it('should generate a valid JWT with correct claims', async () => {
      // Create a test auth context
      const authContext: AuthContext = {
        userId: 'test-user-id',
        organizationId: 'test-org-id',
        role: 'user',
        tenantId: 'test-org-id',
        authenticated: true,
        email: '<EMAIL>',
      };
      
      // Generate the JWT
      const token = await generateCopilotJWT(authContext);
      
      // Verify it's a string
      expect(typeof token).toBe('string');
      
      // Verify it's a valid JWT by attempting to decode it
      const secretKey = new TextEncoder().encode(process.env.SUPABASE_JWT_SECRET);
      const { payload } = await jwtVerify(token, secretKey);
      
      // Check the claims in the payload
      expect(payload.sub).toBe(authContext.userId);
      expect(payload.organization_id).toBe(authContext.organizationId);
      expect(payload.tenant_id).toBe(authContext.tenantId);
      expect(payload.role).toBe(authContext.role);
      expect(payload.user_email).toBe(authContext.email);
      expect(payload.authenticated).toBe(true);
      
      // Check that expiration is set
      expect(payload.exp).toBeDefined();
      expect(typeof payload.exp).toBe('number');
      
      // Check issued at time
      expect(payload.iat).toBeDefined();
      expect(typeof payload.iat).toBe('number');
    });

    it('should throw an error when JWT secret is not configured', async () => {
      // Remove the JWT secret from environment
      delete process.env.SUPABASE_JWT_SECRET;
      
      // Create a test auth context
      const authContext: AuthContext = {
        userId: 'test-user-id',
        organizationId: 'test-org-id',
        role: 'user',
        tenantId: 'test-org-id',
        authenticated: true,
      };
      
      // Attempt to generate a JWT and expect it to throw
      await expect(generateCopilotJWT(authContext)).rejects.toThrow('JWT secret not configured');
    });
  });

  describe('getCopilotAuthHeader', () => {
    it('should return null when no auth context exists', async () => {
      // Mock getCurrentAuthContext to return null
      vi.spyOn(require('../ag-ui-auth'), 'getCurrentAuthContext').mockResolvedValue(null);
      
      // Execute the function
      const result = await getCopilotAuthHeader();
      
      // Verify the result
      expect(result).toBeNull();
    });

    it('should return a Bearer token when auth context exists', async () => {
      // Create a test auth context
      const authContext: AuthContext = {
        userId: 'test-user-id',
        organizationId: 'test-org-id',
        role: 'user',
        tenantId: 'test-org-id',
        authenticated: true,
      };
      
      // Mock getCurrentAuthContext to return our test context
      vi.spyOn(require('../ag-ui-auth'), 'getCurrentAuthContext').mockResolvedValue(authContext);
      
      // Mock generateCopilotJWT to return a test token
      vi.spyOn(require('../ag-ui-auth'), 'generateCopilotJWT').mockResolvedValue('test-jwt-token');
      
      // Execute the function
      const result = await getCopilotAuthHeader();
      
      // Verify the result is a Bearer token
      expect(result).toBe('Bearer test-jwt-token');
    });

    it('should handle errors gracefully', async () => {
      // Mock getCurrentAuthContext to throw an error
      vi.spyOn(require('../ag-ui-auth'), 'getCurrentAuthContext').mockImplementation(() => {
        throw new Error('Auth error');
      });
      
      // Execute the function
      const result = await getCopilotAuthHeader();
      
      // Verify error handling
      expect(result).toBeNull();
    });
  });

  describe('getThreadComponents', () => {
    it('should include organization ID when provided', () => {
      // Execute the function with organization ID
      const result = getThreadComponents('user-123', 'org-456');
      
      // Verify the thread components include both user and org
      expect(result.threadIdComponents).toContain('org-org-456');
      expect(result.threadIdComponents).toContain('user-user-123');
      expect(result.threadIdComponents.length).toBe(2);
    });

    it('should work with just user ID when no organization is provided', () => {
      // Execute the function without organization ID
      const result = getThreadComponents('user-123');
      
      // Verify the thread components include only user
      expect(result.threadIdComponents).toContain('user-user-123');
      expect(result.threadIdComponents.length).toBe(1);
      expect(result.threadIdComponents.find(comp => comp.startsWith('org-'))).toBeUndefined();
    });
  });

  // Integration tests for tenant isolation
  describe('Tenant Isolation', () => {
    it('should create different thread IDs for users in different organizations', () => {
      // Get thread components for user in organization A
      const threadA = getThreadComponents('same-user', 'org-a');
      
      // Get thread components for same user in organization B
      const threadB = getThreadComponents('same-user', 'org-b');
      
      // Verify the threads are different
      expect(threadA.threadIdComponents).not.toEqual(threadB.threadIdComponents);
      
      // Verify they both contain the same user ID
      expect(threadA.threadIdComponents.find(comp => comp.includes('same-user'))).toBeDefined();
      expect(threadB.threadIdComponents.find(comp => comp.includes('same-user'))).toBeDefined();
      
      // Verify they have different organization IDs
      expect(threadA.threadIdComponents.find(comp => comp.includes('org-a'))).toBeDefined();
      expect(threadB.threadIdComponents.find(comp => comp.includes('org-b'))).toBeDefined();
    });
  });
});
