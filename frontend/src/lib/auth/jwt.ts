/**
 * JWT Utilities and Verification
 * Handles JWT token parsing, verification, and debugging
 */

import type { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';
import { createClient } from '@supabase/supabase-js';
import type { Tenant<PERSON>lai<PERSON>, AuthResult} from './types';
import { UserRole } from './types';

// Initialize Supabase client for JWT verification
const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Get JWT secret with proper validation
 */
function getJWTSecret(): string {
  const jwtSecret = process.env.SUPABASE_JWT_SECRET;

  if (!jwtSecret) {
    const nodeEnv = process.env.NODE_ENV || 'development';
    throw new Error(
      `SUPABASE_JWT_SECRET environment variable is required for ${nodeEnv} environment. ` +
      'Please set this variable with a secure 32+ character secret.'
    );
  }

  // Validate secret length for security
  if (jwtSecret.length < 32) {
    throw new Error(
      'SUPABASE_JWT_SECRET must be at least 32 characters long for security. ' +
      'Please generate a longer, more secure secret.'
    );
  }

  return jwtSecret;
}

const JWT_SECRET = getJWTSecret();

/**
 * Verifies a JWT token and returns the claims
 * Supports both Supabase tokens and custom JWT tokens
 *
 * @param token The JWT token to verify
 * @returns The verified claims or null if invalid
 */
export async function verifyJwt(token: string): Promise<TenantClaims | null> {
  if (!token) return null;

  try {
    // For Supabase tokens (contain dots), verify with Supabase
    if (token.includes('.')) {
      const { data: { user }, error } = await supabase.auth.getUser(token);

      if (error || !user) {
        console.error('Supabase token verification failed:', error);
        return null;
      }

      // SECURITY: Removed client-side JWT parsing - using server-verified user data only
      return {
        sub: user.id,
        email: user.email || '',
        role: (user.role || 'client') as UserRole,
        tenant_id: user.user_metadata?.tenant_id || user.id, // Use secure metadata or user ID fallback
        exp: undefined,
        iat: undefined,
        jti: undefined
      };
    }
    // For custom tokens, verify with JWT secret
    else {
      const decoded = jwt.verify(token, JWT_SECRET) as any;

      if (!decoded?.sub) {
        return null;
      }

      return {
        sub: decoded.sub,
        email: decoded.email,
        role: (decoded.role || 'client') as UserRole,
        tenant_id: decoded.tenant_id,
        exp: decoded.exp,
        iat: decoded.iat,
        jti: decoded.jti,
        ...decoded
      };
    }
  } catch (error) {
    console.error('JWT verification failed:', error);
    return null;
  }
}

/**
 * Verifies JWT token from a Next.js request
 * Extracts token from Authorization header or cookies
 *
 * @param req Next.js request object
 * @returns Auth result with user info if successful
 */
export async function verifyJWT(_req: NextRequest): Promise<AuthResult> {
  // Extract token from Authorization header or cookie
  const authHeader = _req.headers.get('authorization');
  const token = authHeader ? authHeader.split(' ')[1] : undefined;

  // If no token in header, check cookies
  const cookies = _req.cookies;
  const cookieToken = cookies.get('sb-access-token')?.value;

  // Use token from header or cookie
  const accessToken = token || cookieToken;

  if (!accessToken) {
    return { success: false, error: 'No authentication token provided' };
  }

  try {
    const claims = await verifyJwt(accessToken);
    
    if (!claims) {
      return { success: false, error: 'Invalid token' };
    }

    return {
      success: true,
      user: {
        id: claims.sub,
        email: claims.email || '',
        role: claims.role || UserRole.Client,
        tenantId: claims.tenant_id || null,
        exp: claims.exp
      }
    };
  } catch (error) {
    console.error('JWT verification error:', error);
    return { success: false, error: 'Token verification failed' };
  }
}

/**
 * DEPRECATED: parseClaims function removed for security
 * Use secure server-side validation instead of client-side JWT parsing
 *
 * @param token JWT token
 * @returns null (function disabled for security)
 */
export function parseClaims(token: string): TenantClaims | null {
  console.error('parseClaims is deprecated for security reasons. Use server-side validation instead.');
  return null;
}

/**
 * DEPRECATED: debugJwtClaims function removed for security
 * Use secure server-side validation instead of client-side JWT parsing
 *
 * @param token The JWT token to debug
 * @returns Disabled debug information
 */
export function debugJwtClaims(token: string): {
  valid: boolean;
  claims: TenantClaims | null;
  header: any;
  payload: any;
  errors: string[];
} {
  console.error('debugJwtClaims is deprecated for security reasons. Use server-side validation instead.');
  return {
    valid: false,
    claims: null,
    header: null,
    payload: null,
    errors: ['Function disabled for security']
  };
}

/**
 * DEPRECATED: extractTenantId function removed for security
 * Use secure server-side validation instead of client-side JWT parsing
 *
 * @param token The JWT token
 * @returns null (function disabled for security)
 */
export function extractTenantId(token: string): string | null {
  console.error('extractTenantId is deprecated for security reasons. Use server-side validation instead.');
  return null;
}

/**
 * Extracts user role from JWT token
 * Utility function for quick role extraction
 *
 * @param token The JWT token
 * @returns The user role or null if not found
 */
export function extractRole(token: string): UserRole | null {
  const claims = parseClaims(token);
  return claims?.role || null;
}

/**
 * Checks if a JWT token is expired
 *
 * @param token The JWT token
 * @returns True if the token is expired
 */
export function isTokenExpired(token: string): boolean {
  const claims = parseClaims(token);
  if (!claims?.exp) return true;
  
  return Date.now() >= claims.exp * 1000;
}

/**
 * Gets the remaining time until token expiration
 *
 * @param token The JWT token
 * @returns Remaining time in seconds, or 0 if expired/invalid
 */
export function getTokenTimeRemaining(token: string): number {
  const claims = parseClaims(token);
  if (!claims?.exp) return 0;
  
  const remaining = claims.exp - Math.floor(Date.now() / 1000);
  return Math.max(0, remaining);
}
