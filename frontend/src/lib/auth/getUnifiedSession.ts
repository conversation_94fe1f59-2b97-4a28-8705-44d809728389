/**
 * Unified Session Helper
 * 
 * This helper provides a unified interface for session management that can switch
 * between Supabase Auth and legacy cookie/JWT authentication based on a feature flag.
 * 
 * When USE_SUPABASE_AUTH=true: Uses Supabase session management
 * When USE_SUPABASE_AUTH=false: Falls back to legacy cookie/JWT parsing
 */

import { createServerClientForUser } from '@/lib/auth/server';
import { type JwtPayload } from '@/lib/supabase/client';

// Type for cookies that works with both middleware and server components
interface CookiesLike {
  get(name: string): { value: string } | undefined;
}

// Unified session interface that works with both Supabase and legacy auth
export interface UnifiedSession {
  user: {
    id: string;
    email?: string;
    app_metadata?: {
      roles?: string[];
      [key: string]: unknown;
    };
    user_metadata?: Record<string, unknown>;
  };
  access_token: string;
  refresh_token?: string;
  expires_at?: number;
  expires_in?: number;
  token_type?: string;
  // Additional fields for compatibility
  tenant_id?: string;
  role?: string;
  // Source indicates which auth system provided this session
  source: 'supabase' | 'legacy';
}

/**
 * Legacy session extraction from cookies
 * This preserves the existing cookie/JWT authentication logic
 */
async function legacyGetSessionFromCookie(cookies: CookiesLike): Promise<UnifiedSession | null> {
  try {
    // Look for various cookie patterns used in the existing system
    // Dynamically determine the correct cookie name based on environment
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const projectRef = (/https:\/\/([^.]+)\.supabase\.co/.exec(supabaseUrl))?.[1] || 'anwefmklplkjxkmzpnva';

    const cookieNames = [
      `sb-${projectRef}-auth-token`,
      'sb-access-token',
      'sb-auth-token'
    ];

    let sessionData: Record<string, unknown> | null = null;
    let accessToken: string | null = null;

    // Try to find session data in cookies
    for (const cookieName of cookieNames) {
      const cookieValue = cookies.get(cookieName)?.value;
      if (cookieValue) {
        try {
          // Try to parse as JSON first (for full session data)
          const parsed = JSON.parse(cookieValue);
          if (parsed.access_token) {
            sessionData = parsed;
            accessToken = parsed.access_token;
            break;
          }
        } catch {
          // If not JSON, treat as raw token
          accessToken = cookieValue;
        }
      }
    }

    if (!accessToken) {
      return null;
    }

    // SECURITY: Use secure server-side validation instead of client-side JWT parsing
    console.warn('getUnifiedSession: Client-side JWT parsing removed for security. Use server-side validation.');

    // For middleware context, we need to validate the session server-side
    // Parse the session data from the cookie to extract user information
    try {
      // Get the session string from the cookie value
      let sessionString = null;

      // Try to get the session from the found cookie
      for (const cookieName of cookieNames) {
        const cookie = cookies?.get(cookieName);
        if (cookie?.value) {
          sessionString = decodeURIComponent(cookie.value);
          break;
        }
      }

      if (!sessionString) {
        return null;
      }

      const sessionData = JSON.parse(sessionString);

      if (!sessionData.access_token || !sessionData.user) {
        return null;
      }

      // Basic token expiration check
      const expiresAt = sessionData.expires_at;
      if (expiresAt && Date.now() / 1000 > expiresAt) {
        console.log('Session expired');
        return null;
      }

      // Return a minimal session object for middleware validation
      return {
        user: {
          id: sessionData.user.id,
          email: sessionData.user.email,
          app_metadata: sessionData.user.app_metadata || {},
          user_metadata: sessionData.user.user_metadata || {}
        },
        access_token: sessionData.access_token,
        expires_at: sessionData.expires_at,
        source: 'legacy'
      } as UnifiedSession;
    } catch (error) {
      console.error('Error parsing session data:', error);
      return null;
    }

    // Construct unified session from JWT claims
    // SECURITY: This code is unreachable due to security fixes above
    // Placeholder session structure for reference
    const unifiedSession: UnifiedSession = {
      user: {
        id: 'placeholder',
        email: '<EMAIL>',
        app_metadata: {
          roles: ['client'],
          tenant_id: 'placeholder',
        },
        user_metadata: {},
      },
      access_token: accessToken || '',
      refresh_token: sessionData?.refresh_token as string | undefined,
      expires_at: undefined,
      expires_in: undefined,
      token_type: 'bearer',
      tenant_id: 'placeholder',
      role: 'client',
      source: 'legacy',
    };

    return unifiedSession;
  } catch (error) {
    console.error('Error extracting legacy session from cookies:', error);
    return null;
  }
}

/**
 * Supabase session extraction
 * Uses the official Supabase client to get session data
 */
async function supabaseGetSession(cookies?: CookiesLike): Promise<UnifiedSession | null> {
  try {
    // Create Supabase client with cookies if provided (for middleware)
    let supabase;
    if (cookies) {
      const { createServerClient } = await import('@supabase/ssr');

      // Enhanced cookie handling for middleware
      // The issue is that the Supabase SSR client expects a specific cookie interface
      supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            get(name: string) {
              const cookie = cookies.get(name);
              console.log(`🍪 Cookie get: ${name} = ${cookie ? 'found' : 'not found'}`);
              if (cookie) {
                console.log(`🍪 Cookie value preview: ${cookie.value?.substring(0, 50)}...`);
                // Return just the value string - this is what Supabase SSR expects
                return cookie.value;
              }
              return undefined;
            },
            set(name: string, value: string, options: any) {
              // No-op in middleware - we can't set cookies
              console.log(`🍪 Cookie set attempted: ${name} (ignored in middleware)`);
            },
            remove(name: string, options: any) {
              // No-op in middleware - we can't remove cookies
              console.log(`🍪 Cookie remove attempted: ${name} (ignored in middleware)`);
            },
          },
        }
      );
    } else {
      // Use regular server client for API routes
      supabase = await createServerClientForUser();
    }

    console.log('🔍 About to call supabase.auth.getSession()...');
    const { data: { session }, error } = await supabase.auth.getSession();
    console.log('🔍 supabase.auth.getSession() completed');

    console.log('🔍 supabaseGetSession DEBUG:', {
      hasSession: !!session,
      error: error?.message,
      errorCode: error?.status,
      userEmail: session?.user?.email,
      appMetadata: session?.user?.app_metadata,
      userMetadata: session?.user?.user_metadata,
      hasCookies: !!cookies,
      sessionExpiresAt: session?.expires_at,
      sessionExpiresIn: session?.expires_in,
      accessTokenLength: session?.access_token?.length
    });

    // Debug: Check what cookies are available
    if (cookies) {
      // Use the same dynamic cookie name logic
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
      const projectRef = (/https:\/\/([^.]+)\.supabase\.co/.exec(supabaseUrl))?.[1] || 'anwefmklplkjxkmzpnva';
      const authCookie = cookies.get(`sb-${projectRef}-auth-token`);
      console.log('🔍 Auth cookie debug:', {
        hasCookie: !!authCookie,
        cookieLength: authCookie?.value?.length,
        cookiePreview: authCookie?.value?.substring(0, 100) + '...',
        expectedCookieName: `sb-${projectRef}-auth-token`
      });

      // Try to parse the cookie to see if it's valid JSON
      if (authCookie?.value) {
        try {
          const parsed = JSON.parse(authCookie.value);
          console.log('🔍 Parsed cookie structure:', {
            hasAccessToken: !!parsed.access_token,
            hasRefreshToken: !!parsed.refresh_token,
            hasUser: !!parsed.user,
            expiresAt: parsed.expires_at,
            tokenType: parsed.token_type
          });
        } catch (parseError) {
          console.log('🔍 Cookie parse error:', parseError);
        }
      }
    }

    if (error || !session) {
      console.log('❌ supabaseGetSession: No session or error:', error?.message);
      return null;
    }

    // SECURITY: Use secure metadata instead of client-side JWT parsing
    const jwtPayload = null; // Removed for security - v2

    // Convert Supabase session to unified format
    const unifiedSession: UnifiedSession = {
      user: {
        id: session.user.id,
        email: session.user.email,
        app_metadata: session.user.app_metadata || {},
        user_metadata: session.user.user_metadata || {},
      },
      access_token: session.access_token,
      refresh_token: session.refresh_token,
      expires_at: session.expires_at,
      expires_in: session.expires_in,
      token_type: session.token_type,
      tenant_id: session.user.app_metadata?.tenant_id || session.user.user_metadata?.tenant_id,
      role: session.user.app_metadata?.role || session.user.user_metadata?.role,
      source: 'supabase',
    };

    return unifiedSession;
  } catch (error) {
    console.error('Error getting Supabase session:', error);
    return null;
  }
}

/**
 * Main unified session getter
 *
 * This is the primary function that components and middleware should use.
 * It automatically switches between Supabase and legacy auth based on the feature flag.
 *
 * @param cookies - Request cookies (for server-side usage)
 * @returns Unified session object or null if not authenticated
 */
export async function getUnifiedSession(cookies?: CookiesLike): Promise<UnifiedSession | null> {
  const useSupabase = process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH === 'true';

  try {
    if (useSupabase) {
      // Try Supabase first
      const supabaseSession = await supabaseGetSession(cookies);
      if (supabaseSession) {
        return supabaseSession;
      }
      
      // If Supabase fails and we have cookies, try legacy as fallback
      if (cookies) {
        console.warn('Supabase session not found, falling back to legacy auth');
        return await legacyGetSessionFromCookie(cookies);
      }
    } else {
      // Use legacy auth when flag is false
      if (cookies) {
        return await legacyGetSessionFromCookie(cookies);
      }
    }

    return null;
  } catch (error) {
    console.error('Error in getUnifiedSession:', error);
    return null;
  }
}

/**
 * Browser-specific session getter
 * For use in client-side components
 */
export async function getUnifiedSessionBrowser(): Promise<UnifiedSession | null> {
  const useSupabase = process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH === 'true';

  if (useSupabase) {
    return await supabaseGetSession();
  }

  // For legacy browser auth, we'd need to implement cookie reading
  // For now, return null and let components handle this case
  console.warn('Legacy browser session not implemented yet');
  return null;
}

/**
 * Utility to check if a session is valid (not expired)
 */
export function isSessionValid(session: UnifiedSession | null): boolean {
  if (!session) return false;
  
  if (session.expires_at && session.expires_at * 1000 < Date.now()) {
    return false;
  }
  
  return true;
}

/**
 * Utility to extract user roles from session
 */
export function getUserRoles(session: UnifiedSession | null): string[] {
  console.log('🔍 getUserRoles DEBUG:', {
    hasSession: !!session,
    sessionSource: session?.source,
    appMetadataRoles: session?.user?.app_metadata?.roles,
    appMetadataRole: session?.user?.app_metadata?.role,
    sessionRole: session?.role,
    userEmail: session?.user?.email
  });

  if (!session) {
    console.log('❌ getUserRoles: No session');
    return [];
  }

  // Try app_metadata.roles first (Supabase standard - plural)
  if (session.user.app_metadata?.roles) {
    const roles = Array.isArray(session.user.app_metadata.roles)
      ? session.user.app_metadata.roles
      : [session.user.app_metadata.roles];
    console.log('✅ getUserRoles: Found roles in app_metadata.roles:', roles);
    return roles;
  }

  // Try app_metadata.role (Supabase standard - singular)
  if (session.user.app_metadata?.role) {
    const role = [session.user.app_metadata.role as string];
    console.log('✅ getUserRoles: Found role in app_metadata.role:', role);
    return role;
  }

  // Fallback to role field (legacy)
  if (session.role) {
    const role = [session.role];
    console.log('✅ getUserRoles: Found role in session.role:', role);
    return role;
  }

  console.log('❌ getUserRoles: No roles found');
  return [];
}
