/**
 * Payment Methods Client API
 * 
 * Client-side service for interacting with payment method APIs
 * with proper error handling and type safety.
 */

import type {
  PaymentMethodAvailability,
  PaymentMethodSelectionRequest,
  PaymentMethodSelectionResponse,
  PaymentMethodValidationRequest,
  PaymentMethodValidationResponse,
  StripePaymentMethodCreationRequest,
  StripePaymentMethodCreationResponse,
  CustomerPaymentPreferences,
  SupportedRegion,
  SupportedCurrency
} from '@/lib/types/payment-methods';

interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
  details?: string;
}

/**
 * Payment Methods Client Service
 */
export class PaymentMethodsClient {
  private baseUrl: string;
  
  constructor(baseUrl = '/api/payment-methods') {
    this.baseUrl = baseUrl;
  }
  
  /**
   * Get available payment methods for a region
   */
  async getAvailablePaymentMethods(
    countryCode: SupportedRegion,
    currencyCode: SupportedCurrency,
    amountCents?: number
  ): Promise<PaymentMethodAvailability[]> {
    const params = new URLSearchParams({
      country: countryCode,
      currency: currencyCode,
      ...(amountCents && { amount: amountCents.toString() })
    });
    
    const response = await fetch(`${this.baseUrl}?${params}`);
    const result: ApiResponse<PaymentMethodAvailability[]> = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch payment methods');
    }
    
    return result.data;
  }
  
  /**
   * Get payment method recommendation
   */
  async getRecommendation(
    request: PaymentMethodSelectionRequest
  ): Promise<PaymentMethodSelectionResponse> {
    const response = await fetch(`${this.baseUrl}/recommend`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request)
    });
    
    const result: ApiResponse<PaymentMethodSelectionResponse> = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Failed to get recommendation');
    }
    
    return result.data;
  }
  
  /**
   * Validate payment method data
   */
  async validatePaymentMethod(
    request: PaymentMethodValidationRequest
  ): Promise<PaymentMethodValidationResponse> {
    const response = await fetch(`${this.baseUrl}/validate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request)
    });
    
    const result: ApiResponse<PaymentMethodValidationResponse> = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Validation failed');
    }
    
    return result.data;
  }
  
  /**
   * Create payment method with Stripe
   */
  async createPaymentMethod(
    request: StripePaymentMethodCreationRequest
  ): Promise<StripePaymentMethodCreationResponse> {
    const response = await fetch(`${this.baseUrl}/stripe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request)
    });
    
    const result: ApiResponse<StripePaymentMethodCreationResponse> = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Failed to create payment method');
    }
    
    return result.data;
  }
  
  /**
   * Process payment with existing payment method
   */
  async processPayment(options: {
    payment_method_id: string;
    amount_cents: number;
    currency_code: SupportedCurrency;
    tenant_id?: string;
    subscription_id?: string;
    description?: string;
    metadata?: Record<string, unknown>;
    confirm_immediately?: boolean;
    save_for_future_use?: boolean;
  }): Promise<StripePaymentMethodCreationResponse> {
    const response = await fetch(`${this.baseUrl}/stripe/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(options)
    });
    
    const result: ApiResponse<StripePaymentMethodCreationResponse> = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Payment processing failed');
    }
    
    return result.data;
  }
  
  /**
   * Get customer payment preferences
   */
  async getCustomerPreferences(
    tenantId: string,
    countryCode: SupportedRegion,
    currencyCode: SupportedCurrency
  ): Promise<CustomerPaymentPreferences | null> {
    const params = new URLSearchParams({
      tenant_id: tenantId,
      country: countryCode,
      currency: currencyCode
    });
    
    const response = await fetch(`${this.baseUrl}/preferences?${params}`);
    const result: ApiResponse<CustomerPaymentPreferences | null> = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch preferences');
    }
    
    return result.data;
  }
  
  /**
   * Save customer payment preferences
   */
  async saveCustomerPreferences(
    preferences: Partial<CustomerPaymentPreferences> & {
      tenant_id: string;
      country_code: SupportedRegion;
      currency_code: SupportedCurrency;
    }
  ): Promise<CustomerPaymentPreferences> {
    const response = await fetch(`${this.baseUrl}/preferences`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(preferences)
    });
    
    const result: ApiResponse<CustomerPaymentPreferences> = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Failed to save preferences');
    }
    
    return result.data;
  }
  
  /**
   * Update customer payment preferences
   */
  async updateCustomerPreferences(
    preferences: Partial<CustomerPaymentPreferences> & {
      tenant_id: string;
      country_code: SupportedRegion;
      currency_code: SupportedCurrency;
    }
  ): Promise<CustomerPaymentPreferences> {
    const response = await fetch(`${this.baseUrl}/preferences`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(preferences)
    });
    
    const result: ApiResponse<CustomerPaymentPreferences> = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Failed to update preferences');
    }
    
    return result.data;
  }
}

/**
 * Default payment methods client instance
 */
export const paymentMethodsClient = new PaymentMethodsClient();

/**
 * Utility functions for payment method operations
 */
export const PaymentMethodUtils = {
  /**
   * Format payment method display name
   */
  formatPaymentMethodName: (method: PaymentMethodAvailability): string => {
    return method.payment_method_type.name;
  },
  
  /**
   * Get payment method icon
   */
  getPaymentMethodIcon: (code: string): string => {
    const icons: Record<string, string> = {
      card: '💳',
      ach: '🏦',
      sepa_debit: '🏦',
      bancontact: '🇧🇪',
      ideal: '🇳🇱',
      sofort: '🇩🇪',
      apple_pay: '🍎',
      google_pay: '🔍'
    };
    return icons[code] || '💳';
  },
  
  /**
   * Calculate total processing fee
   */
  calculateProcessingFee: (method: PaymentMethodAvailability, amountCents: number): number => {
    const percentageFee = Math.round(amountCents * method.regional_config.processing_fee_percentage);
    return percentageFee + method.regional_config.processing_fee_fixed_cents;
  },
  
  /**
   * Format processing fee for display
   */
  formatProcessingFee: (method: PaymentMethodAvailability, currencyCode: SupportedCurrency): string => {
    const percentage = (method.regional_config.processing_fee_percentage * 100).toFixed(1);
    const fixed = (method.regional_config.processing_fee_fixed_cents / 100).toFixed(2);
    return `${percentage}% + ${currencyCode} ${fixed}`;
  }
};
