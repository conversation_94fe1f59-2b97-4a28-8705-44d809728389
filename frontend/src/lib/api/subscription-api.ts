/**
 * Subscription API client for multi-country subscription management
 */

import type {
  SubscriptionPlan,
  SubscriptionAddon,
  SubscriptionMetrics,
  CreatePlanRequest,
  UpdatePlanRequest,
  CreateAddonRequest,
  UpdateAddonRequest,
  StripeSyncResult,
  BulkSyncResult,
  SubscriptionApiResponse,
  PlanFilters
} from '@/types/subscription-enhanced';

/**
 * Base API client configuration
 */
class ApiClient {
  private baseUrl: string;

  constructor(baseUrl = '/api/admin') {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<SubscriptionApiResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.error || `HTTP ${response.status}: ${response.statusText}`,
          message: data.message || 'Request failed',
        };
      }

      return {
        success: true,
        data: data.data || data,
        message: data.message || 'Request successful',
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        message: 'Failed to complete request',
      };
    }
  }

  async get<T>(endpoint: string, params?: Record<string, string>): Promise<SubscriptionApiResponse<T>> {
    const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;
    return this.request<T>(url, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: unknown): Promise<SubscriptionApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(endpoint: string, data?: unknown): Promise<SubscriptionApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<SubscriptionApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

/**
 * Subscription API client
 */
export class SubscriptionApiClient extends ApiClient {
  constructor() {
    super('/api/admin');
  }

  // Plan management
  async getPlans(filters?: PlanFilters): Promise<SubscriptionApiResponse<SubscriptionPlan[]>> {
    const params: Record<string, string> = {};
    
    if (filters?.is_active !== undefined) {
      params.is_active = filters.is_active.toString();
    }
    if (filters?.is_public !== undefined) {
      params.is_public = filters.is_public.toString();
    }
    if (filters?.search) {
      params.search = filters.search;
    }
    if (filters?.country_codes) {
      params.countries = filters.country_codes.join(',');
    }
    if (filters?.currencies) {
      params.currencies = filters.currencies.join(',');
    }

    return this.get<SubscriptionPlan[]>('/subscriptions/plans', params);
  }

  async getPlan(id: string): Promise<SubscriptionApiResponse<SubscriptionPlan>> {
    return this.get<SubscriptionPlan>(`/subscriptions/plans/${id}`);
  }

  async createPlan(data: CreatePlanRequest): Promise<SubscriptionApiResponse<SubscriptionPlan>> {
    return this.post<SubscriptionPlan>('/subscriptions/plans', data);
  }

  async updatePlan(id: string, data: UpdatePlanRequest): Promise<SubscriptionApiResponse<SubscriptionPlan>> {
    return this.patch<SubscriptionPlan>(`/subscriptions/plans/${id}`, data);
  }

  async deletePlan(id: string): Promise<SubscriptionApiResponse<void>> {
    return this.delete<void>(`/subscriptions/plans/${id}`);
  }

  async syncPlan(id: string): Promise<SubscriptionApiResponse<StripeSyncResult>> {
    return this.post<StripeSyncResult>(`/subscriptions/plans/${id}/sync`);
  }

  // Add-on management
  async getAddons(filters?: { is_active?: boolean; search?: string }): Promise<SubscriptionApiResponse<SubscriptionAddon[]>> {
    const params: Record<string, string> = {};
    
    if (filters?.is_active !== undefined) {
      params.is_active = filters.is_active.toString();
    }
    if (filters?.search) {
      params.search = filters.search;
    }

    return this.get<SubscriptionAddon[]>('/subscriptions/addons', params);
  }

  async getAddon(id: string): Promise<SubscriptionApiResponse<SubscriptionAddon>> {
    return this.get<SubscriptionAddon>(`/subscriptions/addons/${id}`);
  }

  async createAddon(data: CreateAddonRequest): Promise<SubscriptionApiResponse<SubscriptionAddon>> {
    return this.post<SubscriptionAddon>('/subscriptions/addons', data);
  }

  async updateAddon(id: string, data: UpdateAddonRequest): Promise<SubscriptionApiResponse<SubscriptionAddon>> {
    return this.patch<SubscriptionAddon>(`/subscriptions/addons/${id}`, data);
  }

  async deleteAddon(id: string): Promise<SubscriptionApiResponse<void>> {
    return this.delete<void>(`/subscriptions/addons/${id}`);
  }

  async syncAddon(id: string): Promise<SubscriptionApiResponse<StripeSyncResult>> {
    return this.post<StripeSyncResult>(`/subscriptions/addons/${id}/sync`);
  }

  // Metrics and monitoring
  async getMetrics(): Promise<SubscriptionApiResponse<SubscriptionMetrics>> {
    return this.get<SubscriptionMetrics>('/subscriptions/metrics');
  }

  async getHealth(): Promise<SubscriptionApiResponse<{
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    score: number;
  }>> {
    return this.get('/subscriptions/health');
  }

  // Stripe synchronization
  async syncAllToStripe(): Promise<SubscriptionApiResponse<BulkSyncResult>> {
    return this.post<BulkSyncResult>('/monitoring/stripe/force-sync');
  }

  async getStripeSyncStatus(): Promise<SubscriptionApiResponse<{
    plans: {
      id: string;
      name: string;
      stripe_product_id?: string;
      last_synced_at?: string;
      sync_status: 'synced' | 'pending' | 'failed' | 'never';
    }[];
    addons: {
      id: string;
      name: string;
      stripe_product_id?: string;
      last_synced_at?: string;
      sync_status: 'synced' | 'pending' | 'failed' | 'never';
    }[];
  }>> {
    return this.get('/monitoring/stripe/sync-status');
  }

  // Multi-country specific endpoints
  async getCountrySupport(): Promise<SubscriptionApiResponse<{
    countries: {
      code: string;
      name: string;
      currency: string;
      supported: boolean;
      plan_count: number;
      addon_count: number;
    }[];
  }>> {
    return this.get('/subscriptions/countries');
  }

  async getCurrencySupport(): Promise<SubscriptionApiResponse<{
    currencies: {
      code: string;
      name: string;
      symbol: string;
      plan_count: number;
      addon_count: number;
    }[];
  }>> {
    return this.get('/subscriptions/currencies');
  }

  // Validation endpoints
  async validatePlan(data: CreatePlanRequest | UpdatePlanRequest): Promise<SubscriptionApiResponse<{
    valid: boolean;
    errors: { field: string; message: string }[];
  }>> {
    return this.post('/subscriptions/plans/validate', data);
  }

  async validateAddon(data: CreateAddonRequest | UpdateAddonRequest): Promise<SubscriptionApiResponse<{
    valid: boolean;
    errors: { field: string; message: string }[];
  }>> {
    return this.post('/subscriptions/addons/validate', data);
  }

  // Bulk operations
  async bulkUpdatePlans(updates: { id: string; data: Partial<UpdatePlanRequest> }[]): Promise<SubscriptionApiResponse<{
    updated: number;
    failed: number;
    errors: { id: string; error: string }[];
  }>> {
    return this.post('/subscriptions/plans/bulk-update', { updates });
  }

  async bulkSyncPlans(planIds: string[]): Promise<SubscriptionApiResponse<BulkSyncResult>> {
    return this.post('/subscriptions/plans/bulk-sync', { plan_ids: planIds });
  }
}

// Export singleton instance
export const subscriptionApi = new SubscriptionApiClient();

// Export utility functions for error handling
export function isApiError(response: SubscriptionApiResponse<unknown>): response is SubscriptionApiResponse<never> & { success: false } {
  return !response.success;
}

export function getApiErrorMessage(response: SubscriptionApiResponse<unknown>): string {
  if (isApiError(response)) {
    return response.error || response.message || 'Unknown error occurred';
  }
  return '';
}

export function handleApiResponse<T>(
  response: SubscriptionApiResponse<T>,
  onSuccess?: (data: T) => void,
  onError?: (error: string) => void
): T | null {
  if (response.success && response.data) {
    onSuccess?.(response.data);
    return response.data;
  } else {
    const error = getApiErrorMessage(response);
    onError?.(error);
    return null;
  }
}
