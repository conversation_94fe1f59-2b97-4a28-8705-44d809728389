/**
 * Base Repository
 *
 * This module provides a base class for repositories that access tables in different schemas.
 * It ensures consistent error handling and provides common CRUD operations.
 */
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../supabase/database.types';
import type { SchemaName, TableName, TableRow, TableInsert, TableUpdate } from './schema-client';
import { SchemaClient } from './schema-client';

/**
 * Base repository for accessing tables in different schemas
 */
export abstract class BaseRepository<
  S extends SchemaName,
  T extends TableName<S>,
  Row = TableRow<S, T>,
  Insert = TableInsert<S, T>,
  Update = TableUpdate<S, T>
> {
  protected schemaClient: SchemaClient;

  constructor(
    protected client: SupabaseClient<Database>,
    protected schema: S,
    protected table: T,
    protected tenantId?: string
  ) {
    this.schemaClient = new SchemaClient(client);
  }

  /**
   * Get all rows in the table
   *
   * @param options Query options
   * @returns An array of rows
   */
  async getAll(options: {
    page?: number;
    limit?: number;
    select?: string;
    orderBy?: { column: string; ascending?: boolean };
    filters?: Record<string, unknown>;
  } = {}): Promise<{ data: Row[]; count: number; page: number; limit: number; totalPages: number }> {
    const {
      page = 1,
      limit = 20,
      select = '*',
      orderBy,
      filters = {}
    } = options;

    // Create the query
    let query = this.schemaClient.schema(this.schema, this.table)
      .select(select, { count: 'exact' });

    // Apply tenant filter if tenantId is provided
    if (this.tenantId) {
      query = query.eq('tenant_id', this.tenantId);
    }

    // Apply additional filters
    for (const [key, value] of Object.entries(filters)) {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          query = query.in(key, value);
        } else {
          query = query.eq(key, value);
        }
      }
    }

    // Apply ordering
    if (orderBy) {
      query = query.order(orderBy.column, { ascending: orderBy.ascending ?? true });
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Execute the query
    const { data, error, count } = await query;

    // Handle errors
    if (error) {
      console.error(`Error fetching ${this.schema}.${String(this.table)}:`, error);
      throw error;
    }

    // Return the results
    return {
      data: data as Row[],
      count: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  }

  /**
   * Get a single row by ID
   *
   * @param id The ID of the row
   * @param select The columns to select
   * @returns The row or null if not found
   */
  async getById(id: string, select = '*'): Promise<Row | null> {
    let query = this.schemaClient.schema(this.schema, this.table)
      .select(select)
      .eq('id', id);

    // Apply tenant filter if tenantId is provided
    if (this.tenantId) {
      query = query.eq('tenant_id', this.tenantId);
    }

    const { data, error } = await query.single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      console.error(`Error fetching ${this.schema}.${String(this.table)} by ID:`, error);
      throw error;
    }

    return data as Row;
  }

  /**
   * Create a new row
   *
   * @param data The data to insert
   * @returns The created row
   */
  async create(data: Insert): Promise<Row> {
    // Add tenant_id if provided
    const insertData = this.tenantId
      ? { ...data, tenant_id: this.tenantId }
      : data;

    const { data: result, error } = await this.schemaClient.schema(this.schema, this.table)
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error(`Error creating ${this.schema}.${String(this.table)}:`, error);
      throw error;
    }

    return result as Row;
  }

  /**
   * Update a row
   *
   * @param id The ID of the row to update
   * @param data The data to update
   * @returns The updated row
   */
  async update(id: string, data: Update): Promise<Row> {
    let query = this.schemaClient.schema(this.schema, this.table)
      .update(data)
      .eq('id', id);

    // Apply tenant filter if tenantId is provided
    if (this.tenantId) {
      query = query.eq('tenant_id', this.tenantId);
    }

    const { data: result, error } = await query.select().single();

    if (error) {
      console.error(`Error updating ${this.schema}.${String(this.table)}:`, error);
      throw error;
    }

    return result as Row;
  }

  /**
   * Delete a row
   *
   * @param id The ID of the row to delete
   * @returns True if the row was deleted
   */
  async delete(id: string): Promise<boolean> {
    let query = this.schemaClient.schema(this.schema, this.table)
      .delete()
      .eq('id', id);

    // Apply tenant filter if tenantId is provided
    if (this.tenantId) {
      query = query.eq('tenant_id', this.tenantId);
    }

    const { error } = await query;

    if (error) {
      console.error(`Error deleting ${this.schema}.${String(this.table)}:`, error);
      throw error;
    }

    return true;
  }
}
