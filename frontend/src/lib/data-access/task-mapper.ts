/**
 * Task Data Mapper
 *
 * This module provides functions for mapping between database task records and domain task objects.
 * It extends the existing tasks repository functionality with more comprehensive object mapping.
 */
import { Database } from '../supabase/database.types';
import type { TaskRow, TaskInsert, TaskUpdate } from './tasks-repository';
import { mapMetadata } from '../utils/type-converters';

// Domain model types for Tasks
export type TaskStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled';

export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';

// Enhanced task domain model with related entities
export interface Task {
  id: string;
  title: string;
  description?: string | null;
  status: TaskStatus;
  priority?: TaskPriority;
  dueDate?: string | null;
  createdAt?: string | null;
  updatedAt?: string | null;
  createdBy: string;
  updatedBy?: string | null;
  assignedTo?: string | null;
  relatedCase?: string | null;
  tenantId: string;
  aiMetadata?: Record<string, unknown> | null;
  // Expanded relationships
  assignee?: {
    id: string;
    email: string;
    firstName?: string | null;
    lastName?: string | null;
  } | null;
  caseDetails?: {
    id: string;
    title: string;
  } | null;
}

// Task create input model
export interface CreateTaskDto {
  title: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  dueDate?: string;
  assignedTo?: string;
  relatedCase?: string;
  aiMetadata?: Record<string, unknown>;
}

// Task update input model
export interface UpdateTaskDto extends Partial<CreateTaskDto> {}

/**
 * Maps a database task row to a domain task object
 */
export function mapTaskRowToTask(row: TaskRow, includeRelations = true): Task {
  return {
    id: row.id,
    title: row.title,
    description: row.description,
    status: row.status as TaskStatus,
    dueDate: row.due_date,
    createdAt: row.created_at,
    updatedAt: row.updated_at,
    createdBy: row.created_by,
    updatedBy: row.updated_by,
    assignedTo: row.assigned_to,
    relatedCase: row.related_case,
    tenantId: row.tenant_id,
    aiMetadata: mapMetadata(row, 'ai_metadata'), // Fixed: Using mapMetadata utility for type-safe conversion
    // Relations are added outside this mapper if they're included in the query
    assignee: null,
    caseDetails: null
  };
}

/**
 * Maps a domain task create DTO to a database task insert object
 */
export function mapCreateTaskDtoToTaskInsert(
  dto: CreateTaskDto,
  tenantId: string,
  userId: string
): TaskInsert {
  return {
    title: dto.title,
    description: dto.description,
    status: dto.status || 'pending',
    due_date: dto.dueDate,
    assigned_to: dto.assignedTo,
    related_case: dto.relatedCase,
    ai_metadata: dto.aiMetadata as any,
    tenant_id: tenantId,
    created_by: userId
  };
}

/**
 * Maps a domain task update DTO to a database task update object
 */
export function mapUpdateTaskDtoToTaskUpdate(
  dto: UpdateTaskDto,
  userId: string
): TaskUpdate {
  const update: TaskUpdate = {};

  if (dto.title !== undefined) update.title = dto.title;
  if (dto.description !== undefined) update.description = dto.description;
  if (dto.status !== undefined) update.status = dto.status;
  if (dto.dueDate !== undefined) update.due_date = dto.dueDate;
  if (dto.assignedTo !== undefined) update.assigned_to = dto.assignedTo;
  if (dto.relatedCase !== undefined) update.related_case = dto.relatedCase;
  if (dto.aiMetadata !== undefined) update.ai_metadata = dto.aiMetadata as any;

  // Always include updater information
  update.updated_by = userId;
  update.updated_at = new Date().toISOString();

  return update;
}

/**
 * Maps database task rows with relations to domain task objects
 */
export function mapTaskRowsWithRelations(
  rows: (TaskRow & { assignee?: any, case?: any })[],
): Task[] {
  return rows.map(row => {
    const task = mapTaskRowToTask(row);

    // Map the relations if they exist
    if (row.assignee) {
      task.assignee = {
        id: row.assignee.id,
        email: row.assignee.email,
        firstName: row.assignee.first_name,
        lastName: row.assignee.last_name,
      };
    }

    if (row.case) {
      task.caseDetails = {
        id: row.case.id,
        title: row.case.title,
      };
    }

    return task;
  });
}

/**
 * Enhanced Task Validation Utility Methods
 */
export const TaskValidation = {
  /**
   * Validates a task due date to ensure it's in the future
   */
  isDueDateValid(dueDate?: string | null): boolean {
    if (!dueDate) return true;
    const date = new Date(dueDate);
    return !isNaN(date.getTime()) && date > new Date();
  },

  /**
   * Checks if a task is overdue
   */
  isTaskOverdue(task: Task): boolean {
    if (!task.dueDate) return false;
    const dueDate = new Date(task.dueDate);
    return dueDate < new Date() && task.status !== 'completed' && task.status !== 'cancelled';
  },

  /**
   * Gets tasks that are due soon (within the next 3 days)
   */
  getTasksDueSoon(tasks: Task[]): Task[] {
    const now = new Date();
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(now.getDate() + 3);

    return tasks.filter(task => {
      if (!task.dueDate) return false;
      const dueDate = new Date(task.dueDate);
      return dueDate > now && dueDate <= threeDaysFromNow && task.status !== 'completed' && task.status !== 'cancelled';
    });
  }
};
