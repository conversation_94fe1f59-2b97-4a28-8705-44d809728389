/**
 * Document Data Mapper
 *
 * This module provides functions for mapping between database document records and domain document objects.
 * It includes mapping for both case_documents and document_chunks tables.
 */
import { z } from "zod";
import type { Database } from "../supabase/database.types";
import { mapMetadata } from "../utils/type-converters";

// Type aliases for better readability
export type DocumentRow = Database["tenants"]["Tables"]["case_documents"]["Row"];
export type DocumentInsert = Database["tenants"]["Tables"]["case_documents"]["Insert"];
export type DocumentUpdate = Database["tenants"]["Tables"]["case_documents"]["Update"];

export type DocumentChunkRow = Database["tenants"]["Tables"]["document_chunks"]["Row"];
export type DocumentChunkInsert = Database["tenants"]["Tables"]["document_chunks"]["Insert"];
export type DocumentChunkUpdate = Database["tenants"]["Tables"]["document_chunks"]["Update"];

// Domain model types for Documents
export type DocumentType = "legal_case" | "client_document" | "legal_reference" | "court_filing" | "template" | "other";

// Enhanced document domain model with related entities
export interface Document {
  id: string;
  title: string;
  documentType: DocumentType;
  caseId?: string | null;
  gcsPath?: string | null;
  citation?: string | null;
  documentCategory?: string | null;
  subcategory?: string | null;
  practiceArea?: string | null;
  sensitive: boolean;
  tenantId: string;
  createdBy?: string | null;
  createdAt?: string | null;
  updatedBy?: string | null;
  updatedAt?: string | null;
  metadata?: Record<string, unknown> | null;
  // Expanded relationships
  case?: {
    id: string;
    title: string;
  } | null;
  creator?: {
    id: string;
    email: string;
    firstName?: string | null;
    lastName?: string | null;
  } | null;
  // Optional: include chunks directly
  chunks?: DocumentChunk[];
}

// Document chunk domain model
export interface DocumentChunk {
  id: string;
  documentId: string;
  content: string;
  embedding?: string | null;
  metadata?: Record<string, unknown> | null;
  sourceType?: string | null;
  tenantId: string;
  createdAt?: string | null;
  authoredDocumentId?: string | null;
}

// Document create input model
export interface CreateDocumentDto {
  title: string;
  documentType: DocumentType;
  caseId?: string;
  gcsPath?: string;
  citation?: string;
  documentCategory?: string;
  subcategory?: string;
  practiceArea?: string;
  sensitive?: boolean;
  metadata?: Record<string, unknown>;
}

// Document update input model
export interface UpdateDocumentDto extends Partial<CreateDocumentDto> {}

// Document chunk create input model
export interface CreateDocumentChunkDto {
  documentId: string;
  content: string;
  embedding?: string;
  metadata?: Record<string, unknown>;
  sourceType?: string;
  authoredDocumentId?: string;
}

// Document chunk update input model
export interface UpdateDocumentChunkDto extends Partial<CreateDocumentChunkDto> {}

// Zod schema for document validation
export const createDocumentSchema = z.object({
  title: z.string().min(1, "Title is required"),
  documentType: z.enum(["legal_case", "client_document", "legal_reference", "court_filing", "template", "other"]),
  caseId: z.string().uuid().optional().nullable(),
  gcsPath: z.string().optional().nullable(),
  citation: z.string().optional().nullable(),
  documentCategory: z.string().optional().nullable(),
  subcategory: z.string().optional().nullable(),
  practiceArea: z.string().optional().nullable(),
  sensitive: z.boolean().default(false),
  metadata: z.record(z.any()).optional().nullable(),
});

export const updateDocumentSchema = createDocumentSchema.partial();

export const createDocumentChunkSchema = z.object({
  documentId: z.string().uuid(),
  content: z.string().min(1, "Content is required"),
  embedding: z.string().optional().nullable(),
  metadata: z.record(z.any()).optional().nullable(),
  sourceType: z.string().optional().nullable(),
  authoredDocumentId: z.string().uuid().optional().nullable(),
});

export const updateDocumentChunkSchema = createDocumentChunkSchema.partial();

/**
 * Maps a database document row to a domain document object
 */
export function mapDocumentRowToDocument(row: DocumentRow): Document {
  return {
    id: row.id,
    title: row.title,
    documentType: mapDbDocumentTypeToDocumentType(row.document_type),
    caseId: row.case_id,
    gcsPath: row.gcs_path,
    citation: row.citation,
    documentCategory: row.document_category,
    subcategory: row.subcategory,
    practiceArea: row.practice_area,
    sensitive: row.sensitive ?? false,
    tenantId: row.tenant_id,
    createdBy: row.created_by,
    createdAt: row.created_at,
    updatedBy: row.updated_by,
    updatedAt: row.updated_at,
    metadata: mapMetadata(row), // Fixed: Using mapMetadata utility for type-safe conversion
    // Relations are added outside this mapper if they're included in the query
    case: null,
    creator: null,
    chunks: []
  };
}

/**
 * Maps a database document chunk row to a domain document chunk object
 */
export function mapDocumentChunkRowToDocumentChunk(row: DocumentChunkRow): DocumentChunk {
  return {
    id: row.id,
    documentId: row.document_id,
    content: row.content,
    embedding: row.embedding,
    metadata: mapMetadata(row), // Fixed: Using mapMetadata utility for type-safe conversion
    sourceType: row.source_type,
    tenantId: row.tenant_id,
    createdAt: row.created_at,
    authoredDocumentId: row.authored_document_id
  };
}

/**
 * Maps a domain document create DTO to a database document insert object
 */
export function mapCreateDocumentDtoToDocumentInsert(
  dto: CreateDocumentDto,
  tenantId: string,
  userId: string
): DocumentInsert {
  return {
    title: dto.title,
    document_type: mapDocumentTypeToDbDocumentType(dto.documentType),
    case_id: dto.caseId || null,
    gcs_path: dto.gcsPath || null,
    citation: dto.citation || null,
    document_category: dto.documentCategory || null,
    subcategory: dto.subcategory || null,
    practice_area: dto.practiceArea || null,
    sensitive: dto.sensitive || false,
    tenant_id: tenantId,
    created_by: userId,
    updated_by: userId,
    metadata: (dto.metadata as any) || null
  };
}

/**
 * Maps a domain document update DTO to a database document update object
 */
export function mapUpdateDocumentDtoToDocumentUpdate(
  dto: UpdateDocumentDto,
  userId: string
): DocumentUpdate {
  const update: DocumentUpdate = {};

  if (dto.title !== undefined) update.title = dto.title;
  if (dto.documentType !== undefined) update.document_type = mapDocumentTypeToDbDocumentType(dto.documentType);
  if (dto.caseId !== undefined) update.case_id = dto.caseId;
  if (dto.gcsPath !== undefined) update.gcs_path = dto.gcsPath;
  if (dto.citation !== undefined) update.citation = dto.citation;
  if (dto.documentCategory !== undefined) update.document_category = dto.documentCategory;
  if (dto.subcategory !== undefined) update.subcategory = dto.subcategory;
  if (dto.practiceArea !== undefined) update.practice_area = dto.practiceArea;
  if (dto.sensitive !== undefined) update.sensitive = dto.sensitive;
  if (dto.metadata !== undefined) update.metadata = dto.metadata as any;

  // Always include updater information
  update.updated_by = userId;
  update.updated_at = new Date().toISOString();

  return update;
}

/**
 * Maps a domain document chunk create DTO to a database document chunk insert object
 */
export function mapCreateDocumentChunkDtoToDocumentChunkInsert(
  dto: CreateDocumentChunkDto,
  tenantId: string
): DocumentChunkInsert {
  return {
    document_id: dto.documentId,
    content: dto.content,
    embedding: dto.embedding,
    metadata: dto.metadata as any,
    source_type: dto.sourceType,
    tenant_id: tenantId,
    authored_document_id: dto.authoredDocumentId
  };
}

/**
 * Maps a domain document chunk update DTO to a database document chunk update object
 */
export function mapUpdateDocumentChunkDtoToDocumentChunkUpdate(
  dto: UpdateDocumentChunkDto
): DocumentChunkUpdate {
  const update: DocumentChunkUpdate = {};

  if (dto.content !== undefined) update.content = dto.content;
  if (dto.embedding !== undefined) update.embedding = dto.embedding;
  if (dto.metadata !== undefined) update.metadata = dto.metadata as any;
  if (dto.sourceType !== undefined) update.source_type = dto.sourceType;
  if (dto.documentId !== undefined) update.document_id = dto.documentId;
  if (dto.authoredDocumentId !== undefined) update.authored_document_id = dto.authoredDocumentId;

  return update;
}

/**
 * Maps database document rows with relations to domain document objects
 */
export function mapDocumentRowsWithRelations(
  rows: (DocumentRow & { case?: any, creator?: any, chunks?: unknown[] })[],
): Document[] {
  return rows.map(row => {
    const document = mapDocumentRowToDocument(row);

    // Map the relations if they exist
    if (row.case) {
      document.case = {
        id: row.case.id,
        title: row.case.title,
      };
    }

    if (row.creator) {
      document.creator = {
        id: row.creator.id,
        email: row.creator.email,
        firstName: row.creator.first_name,
        lastName: row.creator.last_name,
      };
    }

    if (row.chunks && Array.isArray(row.chunks)) {
      document.chunks = (row.chunks as any[]).map(mapDocumentChunkRowToDocumentChunk);
    }

    return document;
  });
}

/**
 * Maps a database document type to a domain document type
 */
function mapDbDocumentTypeToDocumentType(dbType: string): DocumentType {
  switch (dbType) {
    case 'legal_case': return 'legal_case';
    case 'client_document': return 'client_document';
    case 'legal_reference': return 'legal_reference';
    case 'court_filing': return 'court_filing';
    case 'template': return 'template';
    default: return 'other';
  }
}

/**
 * Maps a domain document type to a database document type
 */
function mapDocumentTypeToDbDocumentType(documentType: DocumentType): string {
  return documentType;
}

/**
 * Enhanced Document Validation Utility Methods
 */
export const DocumentValidation = {
  /**
   * Checks if a document is searchable (has chunks)
   */
  isSearchable(document: Document): boolean {
    return Array.isArray(document.chunks) && document.chunks.length > 0;
  },

  /**
   * Checks if a document is properly categorized
   */
  isProperlyCategorizd(document: Document): boolean {
    return Boolean(document.documentCategory) || Boolean(document.practiceArea);
  },

  /**
   * Generate unique document path based on metadata
   */
  generateGcsPath(document: CreateDocumentDto, tenantId: string, caseId?: string): string {
    const sanitizedTitle = document.title.toLowerCase().replace(/[^a-z0-9]+/g, '-');
    const timestamp = new Date().getTime();
    const path = [`documents/${tenantId}`];

    if (caseId) {
      path.push(`cases/${caseId}`);
    }

    if (document.documentCategory) {
      path.push(document.documentCategory.toLowerCase());
    }

    path.push(`${sanitizedTitle}-${timestamp}`);

    return path.join('/');
  }
};
