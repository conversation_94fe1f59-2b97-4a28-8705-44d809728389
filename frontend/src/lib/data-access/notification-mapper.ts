/**
 * Notification Data Mapper
 *
 * Maps between database records and domain models for notifications and notification settings.
 * Handles both notification records and notification schedule settings.
 */

import type { Database } from '@/lib/supabase/database.types';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

// Enums
export enum NotificationType {
  CASE_UPDATE = 'case_update',
  DEADLINE_REMINDER = 'deadline_reminder',
  DOCUMENT_READY = 'document_ready',
  TASK_ASSIGNED = 'task_assigned',
  TASK_COMPLETED = 'task_completed',
  SYSTEM = 'system',
  INSIGHT = 'insight',
  ACTIVITY = 'activity'
}

export enum NotificationSeverity {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error'
}

export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// Domain Model Interfaces
export interface Notification {
  id: string;
  message: string;
  type: string;
  context: Record<string, unknown> | null;
  userId: string;
  senderId: string | null;
  tenantId: string;
  read: boolean;
  severity: string | null;
  createdAt: Date | null;
  updatedAt: Date | null;
}

export interface NotificationSchedule {
  id: string;
  type: string;
  userId: string;
  tenantId: string;
  scheduledFor: Date;
  status: NotificationStatus;
  sentAt: Date | null;
  data: Record<string, unknown> | null;
  createdAt: Date | null;
  updatedAt: Date | null;
}

export interface CreateNotificationDto {
  message: string;
  type: string;
  context?: Record<string, unknown> | null;
  userId: string;
  senderId?: string | null;
  severity?: string | null;
}

export interface UpdateNotificationDto {
  id: string;
  read?: boolean;
  message?: string;
  context?: Record<string, unknown> | null;
}

export interface CreateNotificationScheduleDto {
  type: string;
  userId: string;
  scheduledFor: Date;
  data?: Record<string, unknown> | null;
}

export interface UpdateNotificationScheduleDto {
  id: string;
  scheduledFor?: Date;
  status?: NotificationStatus;
  sentAt?: Date | null;
  data?: Record<string, unknown> | null;
}

// Validation schemas
export const NotificationSchema = z.object({
  id: z.string().uuid(),
  message: z.string().min(1),
  type: z.string().min(1),
  context: z.record(z.any()).nullable(),
  userId: z.string().uuid(),
  senderId: z.string().uuid().nullable(),
  tenantId: z.string().min(1),
  read: z.boolean().default(false),
  severity: z.string().nullable(),
  createdAt: z.date().nullable(),
  updatedAt: z.date().nullable()
});

export const NotificationScheduleSchema = z.object({
  id: z.string().uuid(),
  type: z.string().min(1),
  userId: z.string().uuid(),
  tenantId: z.string().min(1),
  scheduledFor: z.date(),
  status: z.enum([
    NotificationStatus.PENDING,
    NotificationStatus.SENT,
    NotificationStatus.FAILED,
    NotificationStatus.CANCELLED
  ]),
  sentAt: z.date().nullable(),
  data: z.record(z.any()).nullable(),
  createdAt: z.date().nullable(),
  updatedAt: z.date().nullable()
});

export const CreateNotificationSchema = z.object({
  message: z.string().min(1),
  type: z.string().min(1),
  context: z.record(z.any()).nullable().optional(),
  userId: z.string().uuid(),
  senderId: z.string().uuid().nullable().optional(),
  severity: z.string().nullable().optional()
});

export const UpdateNotificationSchema = z.object({
  id: z.string().uuid(),
  read: z.boolean().optional(),
  message: z.string().min(1).optional(),
  context: z.record(z.any()).nullable().optional()
});

export const CreateNotificationScheduleSchema = z.object({
  type: z.string().min(1),
  userId: z.string().uuid(),
  scheduledFor: z.date(),
  data: z.record(z.any()).nullable().optional()
});

export const UpdateNotificationScheduleSchema = z.object({
  id: z.string().uuid(),
  scheduledFor: z.date().optional(),
  status: z.enum([
    NotificationStatus.PENDING,
    NotificationStatus.SENT,
    NotificationStatus.FAILED,
    NotificationStatus.CANCELLED
  ]).optional(),
  sentAt: z.date().nullable().optional(),
  data: z.record(z.any()).nullable().optional()
});

// Type definitions for validation
export type NotificationInput = z.infer<typeof NotificationSchema>;
export type NotificationScheduleInput = z.infer<typeof NotificationScheduleSchema>;
export type CreateNotificationInput = z.infer<typeof CreateNotificationSchema>;
export type UpdateNotificationInput = z.infer<typeof UpdateNotificationSchema>;
export type CreateNotificationScheduleInput = z.infer<typeof CreateNotificationScheduleSchema>;
export type UpdateNotificationScheduleInput = z.infer<typeof UpdateNotificationScheduleSchema>;

/**
 * Map a notification row to a domain Notification model
 */
export function mapNotificationRowToNotification(
  row: Database['tenants']['Tables']['notifications']['Row']
): Notification {
  return {
    id: row.id,
    message: row.message,
    type: row.type,
    context: typeof row.context === 'object' ? (row.context as Record<string, unknown>) : null,
    userId: row.user_id,
    senderId: row.sender_id,
    tenantId: row.tenant_id,
    read: row.read ?? false,
    severity: row.severity,
    createdAt: row.created_at ? new Date(row.created_at) : null,
    updatedAt: row.updated_at ? new Date(row.updated_at) : null
  };
}

/**
 * Map a notification schedule row to a domain NotificationSchedule model
 */
export function mapNotificationScheduleRowToNotificationSchedule(
  row: Database['tenants']['Tables']['notification_schedules']['Row']
): NotificationSchedule {
  return {
    id: row.id,
    type: row.type,
    userId: row.user_id,
    tenantId: row.tenant_id,
    scheduledFor: new Date(row.scheduled_for),
    status: row.status as NotificationStatus,
    sentAt: row.sent_at ? new Date(row.sent_at) : null,
    data: typeof row.data === 'object' ? (row.data as Record<string, unknown>) : null,
    createdAt: row.created_at ? new Date(row.created_at) : null,
    updatedAt: row.updated_at ? new Date(row.updated_at) : null
  };
}

/**
 * Map a create notification DTO to a notification insert object
 */
export function mapCreateNotificationDtoToNotificationInsert(
  dto: CreateNotificationDto,
  tenantId: string
): Database['tenants']['Tables']['notifications']['Insert'] {
  return {
    message: dto.message,
    type: dto.type,
    context: dto.context as any || null,
    user_id: dto.userId,
    sender_id: dto.senderId || null,
    tenant_id: tenantId,
    read: false,
    severity: dto.severity || null
  };
}

/**
 * Map a create notification schedule DTO to a notification schedule insert object
 */
export function mapCreateNotificationScheduleDtoToNotificationScheduleInsert(
  dto: CreateNotificationScheduleDto,
  tenantId: string
): Database['tenants']['Tables']['notification_schedules']['Insert'] {
  return {
    type: dto.type,
    user_id: dto.userId,
    tenant_id: tenantId,
    scheduled_for: dto.scheduledFor.toISOString(),
    status: NotificationStatus.PENDING,
    data: dto.data as any || null
  };
}

/**
 * Map an update notification DTO to a notification update object
 */
export function mapUpdateNotificationDtoToNotificationUpdate(
  dto: UpdateNotificationDto
): Database['tenants']['Tables']['notifications']['Update'] {
  const update: Database['tenants']['Tables']['notifications']['Update'] = {};

  if (dto.read !== undefined) update.read = dto.read;
  if (dto.message !== undefined) update.message = dto.message;
  if (dto.context !== undefined) update.context = dto.context as any;

  // Always update the updated_at timestamp
  update.updated_at = new Date().toISOString();

  return update;
}

/**
 * Map an update notification schedule DTO to a notification schedule update object
 */
export function mapUpdateNotificationScheduleDtoToNotificationScheduleUpdate(
  dto: UpdateNotificationScheduleDto
): Database['tenants']['Tables']['notification_schedules']['Update'] {
  const update: Database['tenants']['Tables']['notification_schedules']['Update'] = {};

  if (dto.scheduledFor !== undefined) update.scheduled_for = dto.scheduledFor.toISOString();
  if (dto.status !== undefined) update.status = dto.status;
  if (dto.sentAt !== undefined) update.sent_at = dto.sentAt?.toISOString() || null;
  if (dto.data !== undefined) update.data = dto.data as any;

  // Always update the updated_at timestamp
  update.updated_at = new Date().toISOString();

  return update;
}

/**
 * Validation utilities for notifications
 */
export const NotificationValidation = {
  isRead: (notification: Notification): boolean => {
    return notification.read === true;
  },

  isUnread: (notification: Notification): boolean => {
    return notification.read !== true;
  },

  isFromSystem: (notification: Notification): boolean => {
    return notification.senderId === null;
  },

  isWarningOrError: (notification: Notification): boolean => {
    return notification.severity === NotificationSeverity.WARNING ||
           notification.severity === NotificationSeverity.ERROR;
  },

  isType: (notification: Notification, type: NotificationType): boolean => {
    return notification.type === type;
  }
};

/**
 * Validation utilities for notification schedules
 */
export const NotificationScheduleValidation = {
  isPending: (schedule: NotificationSchedule): boolean => {
    return schedule.status === NotificationStatus.PENDING;
  },

  isSent: (schedule: NotificationSchedule): boolean => {
    return schedule.status === NotificationStatus.SENT;
  },

  isFailed: (schedule: NotificationSchedule): boolean => {
    return schedule.status === NotificationStatus.FAILED;
  },

  isCancelled: (schedule: NotificationSchedule): boolean => {
    return schedule.status === NotificationStatus.CANCELLED;
  },

  isDue: (schedule: NotificationSchedule): boolean => {
    const now = new Date();
    return schedule.status === NotificationStatus.PENDING &&
           schedule.scheduledFor <= now;
  }
};

/**
 * Repository implementation for notifications
 */
export class NotificationRepository {
  /**
   * Get all notifications for a user
   */
  static async getUserNotifications(
    userId: string,
    tenantId: string,
    unreadOnly = false,
    limit = 50,
    offset = 0
  ): Promise<Notification[]> {
    const supabase = await createClient();

    try {
      let query = supabase
        .schema('tenants')
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .eq('tenant_id', tenantId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (unreadOnly) {
        query = query.eq('read', false);
      }

      const { data, error } = await query;

      if (error) throw error;

      if (!data) return [];

      return data.map(row =>
        mapNotificationRowToNotification(row as unknown as Database['tenants']['Tables']['notifications']['Row'])
      );
    } catch (error) {
      console.error('Error fetching user notifications:', error);
      return [];
    }
  }

  /**
   * Get a notification by ID
   */
  static async getNotificationById(
    id: string,
    tenantId: string
  ): Promise<Notification | null> {
    const supabase = await createClient();

    try {
      const { data, error } = await supabase
        .schema('tenants')
        .from('notifications')
        .select('*')
        .eq('id', id)
        .eq('tenant_id', tenantId)
        .single();

      if (error) throw error;

      if (!data) return null;

      return mapNotificationRowToNotification(
        data as Database['tenants']['Tables']['notifications']['Row']
      );
    } catch (error) {
      console.error('Error fetching notification by ID:', error);
      return null;
    }
  }

  /**
   * Get notifications by type
   */
  static async getNotificationsByType(
    type: string,
    userId: string,
    tenantId: string,
    limit = 20
  ): Promise<Notification[]> {
    const supabase = await createClient();

    try {
      const { data, error } = await supabase
        .schema('tenants')
        .from('notifications')
        .select('*')
        .eq('type', type)
        .eq('user_id', userId)
        .eq('tenant_id', tenantId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      if (!data) return [];

      return data.map(row =>
        mapNotificationRowToNotification(row as unknown as Database['tenants']['Tables']['notifications']['Row'])
      );
    } catch (error) {
      console.error(`Error fetching ${type} notifications:`, error);
      return [];
    }
  }

  /**
   * Create a new notification
   */
  static async createNotification(
    notificationData: CreateNotificationDto,
    tenantId: string
  ): Promise<Notification | null> {
    // Validate input
    const validationResult = CreateNotificationSchema.safeParse(notificationData);
    if (!validationResult.success) {
      console.error('Notification validation failed:', validationResult.error);
      throw new Error(`Notification validation failed: ${validationResult.error.message}`);
    }

    const supabase = await createClient();

    try {
      const insert = mapCreateNotificationDtoToNotificationInsert(notificationData, tenantId);
      const { data, error } = await supabase
        .schema('tenants')
        .from('notifications')
        .insert(insert)
        .select()
        .single();

      if (error) throw error;

      if (!data) return null;

      return mapNotificationRowToNotification(
        data as Database['tenants']['Tables']['notifications']['Row']
      );
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Update a notification
   */
  static async updateNotification(
    notificationData: UpdateNotificationDto,
    tenantId: string
  ): Promise<Notification | null> {
    // Validate input
    const validationResult = UpdateNotificationSchema.safeParse(notificationData);
    if (!validationResult.success) {
      console.error('Notification update validation failed:', validationResult.error);
      throw new Error(`Notification update validation failed: ${validationResult.error.message}`);
    }

    const supabase = await createClient();

    try {
      const update = mapUpdateNotificationDtoToNotificationUpdate(notificationData);
      const { data, error } = await supabase
        .schema('tenants')
        .from('notifications')
        .update(update)
        .eq('id', notificationData.id)
        .eq('tenant_id', tenantId)
        .select()
        .single();

      if (error) throw error;

      if (!data) return null;

      return mapNotificationRowToNotification(
        data as Database['tenants']['Tables']['notifications']['Row']
      );
    } catch (error) {
      console.error('Error updating notification:', error);
      return null;
    }
  }

  /**
   * Mark a notification as read
   */
  static async markNotificationAsRead(
    id: string,
    tenantId: string
  ): Promise<boolean> {
    const supabase = await createClient();

    try {
      const { error } = await supabase
        .schema('tenants')
        .from('notifications')
        .update({ read: true, updated_at: new Date().toISOString() })
        .eq('id', id)
        .eq('tenant_id', tenantId);

      if (error) throw error;

      return true;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return false;
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  static async markAllNotificationsAsRead(
    userId: string,
    tenantId: string
  ): Promise<boolean> {
    const supabase = await createClient();

    try {
      const { error } = await supabase
        .schema('tenants')
        .from('notifications')
        .update({ read: true, updated_at: new Date().toISOString() })
        .eq('user_id', userId)
        .eq('tenant_id', tenantId)
        .eq('read', false);

      if (error) throw error;

      return true;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      return false;
    }
  }

  /**
   * Delete a notification
   */
  static async deleteNotification(
    id: string,
    tenantId: string
  ): Promise<boolean> {
    const supabase = await createClient();

    try {
      const { error } = await supabase
        .schema('tenants')
        .from('notifications')
        .delete()
        .eq('id', id)
        .eq('tenant_id', tenantId);

      if (error) throw error;

      return true;
    } catch (error) {
      console.error('Error deleting notification:', error);
      return false;
    }
  }

  /**
   * Get all scheduled notifications for a user
   */
  static async getUserNotificationSchedules(
    userId: string,
    tenantId: string,
    status?: NotificationStatus
  ): Promise<NotificationSchedule[]> {
    const supabase = await createClient();

    try {
      let query = supabase
        .schema('tenants')
        .from('notification_schedules')
        .select('*')
        .eq('user_id', userId)
        .eq('tenant_id', tenantId)
        .order('scheduled_for', { ascending: true });

      if (status) {
        query = query.eq('status', status);
      }

      const { data, error } = await query;

      if (error) throw error;

      if (!data) return [];

      return data.map(row =>
        mapNotificationScheduleRowToNotificationSchedule(
          row as unknown as Database['tenants']['Tables']['notification_schedules']['Row']
        )
      );
    } catch (error) {
      console.error('Error fetching user notification schedules:', error);
      return [];
    }
  }

  /**
   * Get a notification schedule by ID
   */
  static async getNotificationScheduleById(
    id: string,
    tenantId: string
  ): Promise<NotificationSchedule | null> {
    const supabase = await createClient();

    try {
      const { data, error } = await supabase
        .schema('tenants')
        .from('notification_schedules')
        .select('*')
        .eq('id', id)
        .eq('tenant_id', tenantId)
        .single();

      if (error) throw error;

      if (!data) return null;

      return mapNotificationScheduleRowToNotificationSchedule(
        data as Database['tenants']['Tables']['notification_schedules']['Row']
      );
    } catch (error) {
      console.error('Error fetching notification schedule by ID:', error);
      return null;
    }
  }

  /**
   * Create a new notification schedule
   */
  static async createNotificationSchedule(
    scheduleData: CreateNotificationScheduleDto,
    tenantId: string
  ): Promise<NotificationSchedule | null> {
    // Validate input
    const validationResult = CreateNotificationScheduleSchema.safeParse(scheduleData);
    if (!validationResult.success) {
      console.error('Notification schedule validation failed:', validationResult.error);
      throw new Error(`Notification schedule validation failed: ${validationResult.error.message}`);
    }

    const supabase = await createClient();

    try {
      const insert = mapCreateNotificationScheduleDtoToNotificationScheduleInsert(scheduleData, tenantId);
      const { data, error } = await supabase
        .schema('tenants')
        .from('notification_schedules')
        .insert(insert)
        .select()
        .single();

      if (error) throw error;

      if (!data) return null;

      return mapNotificationScheduleRowToNotificationSchedule(
        data as Database['tenants']['Tables']['notification_schedules']['Row']
      );
    } catch (error) {
      console.error('Error creating notification schedule:', error);
      throw error;
    }
  }

  /**
   * Update a notification schedule
   */
  static async updateNotificationSchedule(
    scheduleData: UpdateNotificationScheduleDto,
    tenantId: string
  ): Promise<NotificationSchedule | null> {
    // Validate input
    const validationResult = UpdateNotificationScheduleSchema.safeParse(scheduleData);
    if (!validationResult.success) {
      console.error('Notification schedule update validation failed:', validationResult.error);
      throw new Error(`Notification schedule update validation failed: ${validationResult.error.message}`);
    }

    const supabase = await createClient();

    try {
      const update = mapUpdateNotificationScheduleDtoToNotificationScheduleUpdate(scheduleData);
      const { data, error } = await supabase
        .schema('tenants')
        .from('notification_schedules')
        .update(update)
        .eq('id', scheduleData.id)
        .eq('tenant_id', tenantId)
        .select()
        .single();

      if (error) throw error;

      if (!data) return null;

      return mapNotificationScheduleRowToNotificationSchedule(
        data as Database['tenants']['Tables']['notification_schedules']['Row']
      );
    } catch (error) {
      console.error('Error updating notification schedule:', error);
      return null;
    }
  }

  /**
   * Mark a notification schedule as sent
   */
  static async markScheduleAsSent(
    id: string,
    tenantId: string
  ): Promise<boolean> {
    const supabase = await createClient();

    try {
      const now = new Date().toISOString();
      const { error } = await supabase
        .schema('tenants')
        .from('notification_schedules')
        .update({
          status: NotificationStatus.SENT,
          sent_at: now,
          updated_at: now
        })
        .eq('id', id)
        .eq('tenant_id', tenantId);

      if (error) throw error;

      return true;
    } catch (error) {
      console.error('Error marking notification schedule as sent:', error);
      return false;
    }
  }

  /**
   * Cancel a notification schedule
   */
  static async cancelSchedule(
    id: string,
    tenantId: string
  ): Promise<boolean> {
    const supabase = await createClient();

    try {
      const { error } = await supabase
        .schema('tenants')
        .from('notification_schedules')
        .update({
          status: NotificationStatus.CANCELLED,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('tenant_id', tenantId);

      if (error) throw error;

      return true;
    } catch (error) {
      console.error('Error cancelling notification schedule:', error);
      return false;
    }
  }

  /**
   * Get all pending notifications that are due to be sent
   */
  static async getDueNotificationSchedules(
    tenantId: string
  ): Promise<NotificationSchedule[]> {
    const supabase = await createClient();
    const now = new Date().toISOString();

    try {
      const { data, error } = await supabase
        .schema('tenants')
        .from('notification_schedules')
        .select('*')
        .eq('tenant_id', tenantId)
        .eq('status', NotificationStatus.PENDING)
        .lte('scheduled_for', now)
        .order('scheduled_for', { ascending: true });

      if (error) throw error;

      if (!data) return [];

      return data.map(row =>
        mapNotificationScheduleRowToNotificationSchedule(
          row as unknown as Database['tenants']['Tables']['notification_schedules']['Row']
        )
      );
    } catch (error) {
      console.error('Error fetching due notification schedules:', error);
      return [];
    }
  }
}
