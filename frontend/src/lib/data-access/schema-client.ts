/**
 * Schema Client
 *
 * This module provides a standardized way to access tables in different schemas.
 * It ensures type safety and consistent error handling across the application.
 */
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../supabase/database.types';

/**
 * Type for a table in the tenants schema
 */
export type TenantsTable = keyof Database['tenants']['Tables'];

/**
 * Type for a table in the security schema
 */
export type SecurityTable = keyof Database['security']['Tables'];

/**
 * Type for a table in the public schema
 */
export type PublicTable = keyof Database['public']['Tables'];

/**
 * Type for a schema in the database
 */
export type SchemaName = keyof Database;

/**
 * Type for a table in any schema
 */
export type TableName<S extends SchemaName> = keyof Database[S]['Tables'];

/**
 * Type for a row in a table
 */
export type TableRow<
  S extends SchemaName,
  T extends TableName<S>
> = Database[S]['Tables'][T] extends { Row: infer R } ? R : never;

/**
 * Type for inserting a row into a table
 */
export type TableInsert<
  S extends SchemaName,
  T extends TableName<S>
> = Database[S]['Tables'][T] extends { Insert: infer I } ? I : never;

/**
 * Type for updating a row in a table
 */
export type TableUpdate<
  S extends SchemaName,
  T extends TableName<S>
> = Database[S]['Tables'][T] extends { Update: infer U } ? U : never;

/**
 * Type for a query builder
 */
export type QueryBuilder<T = any> = ReturnType<SupabaseClient<Database>['from']>;

/**
 * Type for a query result
 */
export interface QueryResult<T = any> {
  data: T | null;
  error: Error | null;
  count?: number | null;
}

/**
 * Schema client for accessing tables in different schemas
 */
export class SchemaClient {
  constructor(private client: SupabaseClient<Database>) {}

  /**
   * Access a table in the tenants schema
   *
   * @param table The table name
   * @returns A query builder for the table
   */
  tenants<T extends TenantsTable>(table: T): QueryBuilder<TableRow<'tenants', T>> {
    // Use the schema method to ensure proper schema handling
    return this.client.schema('tenants').from(String(table) as any);
  }

  /**
   * Access a table in the security schema
   *
   * @param table The table name
   * @returns A query builder for the table
   */
  security<T extends SecurityTable>(table: T): QueryBuilder<TableRow<'security', T>> {
    // Use the schema method to ensure proper schema handling
    return this.client.schema('security').from(String(table) as any);
  }

  /**
   * Access a table in the public schema
   *
   * @param table The table name
   * @returns A query builder for the table
   */
  public<T extends PublicTable>(table: T): QueryBuilder<TableRow<'public', T>> {
    // Use the schema method for consistency with other schemas
    return this.client.schema('public').from(String(table) as any);
  }

  /**
   * Access a table in any schema
   *
   * @param schema The schema name
   * @param table The table name
   * @returns A query builder for the table
   */
  schema<S extends SchemaName, T extends TableName<S>>(
    schema: S,
    table: T
  ): QueryBuilder<TableRow<S, T>> {
    try {
      // Always use the schema method for consistency
      return this.client.schema(schema).from(String(table) as any);
    } catch (error) {
      console.error(`Error accessing schema ${schema}, table ${String(table)}:`, error);
      throw new Error(`Failed to access ${schema}.${String(table)}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Execute a query against a table in the tenants schema
   *
   * @param table The table name
   * @param queryFn A function that builds and executes the query
   * @returns The result of the query
   */
  async queryTenants<
    T extends TenantsTable,
    R = TableRow<'tenants', T>[]
  >(
    table: T,
    queryFn: (query: QueryBuilder<TableRow<'tenants', T>>) => Promise<QueryResult<R>>
  ): Promise<R> {
    const query = this.tenants(table);
    const { data, error } = await queryFn(query);

    if (error) throw error;
    if (data === null) throw new Error(`No data returned from tenants.${String(table)}`);

    return data;
  }

  /**
   * Execute a query against a table in the security schema
   *
   * @param table The table name
   * @param queryFn A function that builds and executes the query
   * @returns The result of the query
   */
  async querySecurity<
    T extends SecurityTable,
    R = TableRow<'security', T>[]
  >(
    table: T,
    queryFn: (query: QueryBuilder<TableRow<'security', T>>) => Promise<QueryResult<R>>
  ): Promise<R> {
    const query = this.security(table);
    const { data, error } = await queryFn(query);

    if (error) throw error;
    if (data === null) throw new Error(`No data returned from security.${String(table)}`);

    return data;
  }

  /**
   * Execute a query against a table in the public schema
   *
   * @param table The table name
   * @param queryFn A function that builds and executes the query
   * @returns The result of the query
   */
  async queryPublic<
    T extends PublicTable,
    R = TableRow<'public', T>[]
  >(
    table: T,
    queryFn: (query: QueryBuilder<TableRow<'public', T>>) => Promise<QueryResult<R>>
  ): Promise<R> {
    const query = this.public(table);
    const { data, error } = await queryFn(query);

    if (error) throw error;
    if (data === null) throw new Error(`No data returned from public.${String(table)}`);

    return data;
  }
}

/**
 * Create a schema client
 *
 * @param client The Supabase client
 * @returns A schema client
 */
export function createSchemaClient(client: SupabaseClient<Database>): SchemaClient {
  return new SchemaClient(client);
}
