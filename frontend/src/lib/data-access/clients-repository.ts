/**
 * Clients Repository
 *
 * This module provides a repository for accessing the clients table in the tenants schema.
 */
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../supabase/database.types';
import { BaseRepository } from './base-repository';
import type { TableRow, TableInsert, TableUpdate } from './schema-client';

// Type aliases for better readability
export type ClientRow = TableRow<'tenants', 'clients'>;
export type ClientInsert = TableInsert<'tenants', 'clients'>;
export type ClientUpdate = TableUpdate<'tenants', 'clients'>;

/**
 * Repository for accessing the clients table in the tenants schema
 */
export class ClientsRepository extends BaseRepository<'tenants', 'clients', ClientRow, ClientInsert, ClientUpdate> {
  constructor(client: SupabaseClient<Database>, tenantId: string) {
    super(client, 'tenants', 'clients', tenantId);
  }

  /**
   * Get clients with optional filtering and pagination
   *
   * @param options Query options
   * @returns An array of clients with pagination info
   */
  async getClients(options: {
    page?: number;
    limit?: number;
    status?: string;
    client_type?: string;
    searchTerm?: string;
  } = {}): Promise<{
    data: ClientRow[];
    count: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const {
      page = 1,
      limit = 20,
      status,
      client_type,
      searchTerm
    } = options;

    // Create the query
    let query = this.schemaClient.schema('tenants', 'clients')
      .select('*', { count: 'exact' })
      .eq('tenant_id', this.tenantId!);

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }

    if (client_type) {
      query = query.eq('client_type', client_type);
    }

    // Apply search if provided
    if (searchTerm) {
      query = query.or(
        `first_name.ilike.%${searchTerm}%,last_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%,business_name.ilike.%${searchTerm}%`
      );
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Execute the query
    const { data, error, count } = await query;

    // Handle errors
    if (error) {
      console.error('Error fetching clients:', error);
      console.error('Query details:', { schema: 'tenants', table: 'clients', tenantId: this.tenantId });

      // Provide more specific error messages
      if (error.message?.includes('permission denied')) {
        throw new Error(`Schema access denied: ${error.message}. Check if tenants schema is exposed in PostgREST configuration.`);
      } else if (error.message?.includes('relation') && error.message?.includes('does not exist')) {
        throw new Error(`Table not found: ${error.message}. Check if tenants.clients table exists.`);
      } else {
        throw new Error(`Database query failed: ${error.message}`);
      }
    }

    // Return the results
    return {
      data: data as ClientRow[],
      count: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  }

  /**
   * Get a client by ID with related data
   *
   * @param id The client ID
   * @returns The client or null if not found
   */
  async getClientWithRelations(id: string): Promise<ClientRow | null> {
    const query = this.schemaClient.schema('tenants', 'clients')
      .select(`
        *,
        assigned_attorney:assigned_attorney_id(id, email, first_name, last_name),
        cases(id, status, created_at)
      `)
      .eq('id', id)
      .eq('tenant_id', this.tenantId!)
      .single();

    const { data, error } = await query;

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      console.error('Error fetching client with relations:', error);
      throw error;
    }

    return data as ClientRow;
  }

  /**
   * Get clients by IDs (bulk fetch)
   *
   * @param ids An array of client IDs
   * @returns An array of clients
   */
  async getClientsByIds(ids: string[]): Promise<ClientRow[]> {
    if (!ids.length) return [];

    const query = this.schemaClient.schema('tenants', 'clients')
      .select('*, assigned_attorney:assigned_attorney_id(id, email, first_name, last_name)')
      .in('id', ids)
      .eq('tenant_id', this.tenantId!);

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching clients by IDs:', error);
      throw error;
    }

    return data as ClientRow[];
  }
}
