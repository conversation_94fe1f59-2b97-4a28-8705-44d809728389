/**
 * Cases Repository
 *
 * This module provides a repository for accessing the cases table in the tenants schema.
 */
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../supabase/database.types';
import { BaseRepository } from './base-repository';
import type { TableRow, TableInsert, TableUpdate } from './schema-client';

// Type aliases for better readability
export type CaseRow = TableRow<'tenants', 'cases'>;
export type CaseInsert = TableInsert<'tenants', 'cases'>;
export type CaseUpdate = TableUpdate<'tenants', 'cases'>;

/**
 * Repository for accessing the cases table in the tenants schema
 */
export class CasesRepository extends BaseRepository<'tenants', 'cases', CaseRow, CaseInsert, CaseUpdate> {
  constructor(client: SupabaseClient<Database>, tenantId: string) {
    super(client, 'tenants', 'cases', tenantId);
  }

  /**
   * Get cases with optional filtering and pagination
   *
   * @param options Query options
   * @returns An array of cases with pagination info
   */
  async getCases(options: {
    page?: number;
    limit?: number;
    status?: string;
    client_id?: string;
    searchTerm?: string;
  } = {}): Promise<{
    data: CaseRow[];
    count: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const {
      page = 1,
      limit = 20,
      status,
      client_id,
      searchTerm
    } = options;

    // Create the query - query matters table directly
    let query = this.schemaClient.schema('tenants', 'matters' as any)
      .select('*', { count: 'exact' })
      .eq('tenant_id', this.tenantId!);

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }

    if (client_id) {
      query = query.eq('client_id', client_id);
    }

    // Apply search if provided
    if (searchTerm) {
      query = query.or(
        `title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`
      );
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Execute the query
    const { data, error, count } = await query;

    // Handle errors
    if (error) {
      console.error('Error fetching cases:', error);
      throw error;
    }

    // Return the results
    return {
      data: data as CaseRow[],
      count: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  }

  /**
   * Get a case by ID with related data
   *
   * @param id The case ID
   * @returns The case or null if not found
   */
  async getCaseWithRelations(id: string): Promise<CaseRow | null> {
    const query = this.schemaClient.schema('tenants', 'matters' as any)
      .select(`
        *,
        client:client_id(id, first_name, last_name, email, business_name),
        assigned_to(id, email, first_name, last_name),
        created_by(id, email, first_name, last_name)
      `)
      .eq('id', id)
      .eq('tenant_id', this.tenantId!)
      .single();

    const { data, error } = await query;

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      console.error('Error fetching case with relations:', error);
      throw error;
    }

    return data as CaseRow;
  }

  /**
   * Get cases by client ID
   *
   * @param clientId The client ID
   * @param options Query options
   * @returns An array of cases
   */
  async getCasesByClientId(clientId: string, options: {
    page?: number;
    limit?: number;
  } = {}): Promise<{
    data: CaseRow[];
    count: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const { page = 1, limit = 20 } = options;

    // Create the query - query matters table directly
    let query = this.schemaClient.schema('tenants', 'matters' as any)
      .select(`
        id,
        title,
        description,
        status,
        sensitive,
        created_at,
        updated_at,
        metadata,
        rejection_reason,
        client_id,
        created_by,
        creator:users!matters_created_by_fkey(id, email, first_name, last_name)
      `, { count: 'exact' })
      .eq('tenant_id', this.tenantId!)
      .eq('client_id', clientId);

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Execute the query
    const { data, error, count } = await query;

    // Handle errors
    if (error) {
      console.error('Error fetching cases by client ID:', error);
      throw error;
    }

    // Return the results
    return {
      data: data as CaseRow[],
      count: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  }
}
