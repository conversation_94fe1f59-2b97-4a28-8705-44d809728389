/**
 * Tasks Repository
 *
 * This module provides a repository for accessing the tasks table in the tenants schema.
 */
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../supabase/database.types';
import { BaseRepository } from './base-repository';
import type { TableRow, TableInsert, TableUpdate } from './schema-client';

// Type aliases for better readability
export type TaskRow = TableRow<'tenants', 'tasks'>;
export type TaskInsert = TableInsert<'tenants', 'tasks'>;
export type TaskUpdate = TableUpdate<'tenants', 'tasks'>;

/**
 * Repository for accessing the tasks table in the tenants schema
 */
export class TasksRepository extends BaseRepository<'tenants', 'tasks', TaskRow, TaskInsert, TaskUpdate> {
  constructor(client: SupabaseClient<Database>, tenantId: string) {
    super(client, 'tenants', 'tasks', tenantId);
  }

  /**
   * Get tasks with optional filtering and pagination
   *
   * @param options Query options
   * @returns An array of tasks with pagination info
   */
  async getTasks(options: {
    page?: number;
    limit?: number;
    status?: string;
    assigned_to?: string;
    case_id?: string;
    searchTerm?: string;
  } = {}): Promise<{
    data: TaskRow[];
    count: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const {
      page = 1,
      limit = 20,
      status,
      assigned_to,
      case_id,
      searchTerm
    } = options;

    // Create the query
    let query = this.schemaClient.schema('tenants', 'tasks')
      .select('*', { count: 'exact' })
      .eq('tenant_id', this.tenantId!);

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }

    if (assigned_to) {
      query = query.eq('assigned_to', assigned_to);
    }

    if (case_id) {
      query = query.eq('related_case', case_id);
    }

    // Apply search if provided
    if (searchTerm) {
      query = query.or(
        `title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`
      );
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Execute the query
    const { data, error, count } = await query;

    // Handle errors
    if (error) {
      console.error('Error fetching tasks:', error);
      throw error;
    }

    // Return the results
    return {
      data: data as TaskRow[],
      count: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  }

  /**
   * Get a task by ID with related data
   *
   * @param id The task ID
   * @returns The task or null if not found
   */
  async getTaskWithRelations(id: string): Promise<TaskRow | null> {
    const query = this.schemaClient.schema('tenants', 'tasks')
      .select(`
        *,
        assignee:assigned_to(id, email, first_name, last_name),
        case:related_case(id, title),
        creator:created_by(id, email, first_name, last_name)
      `)
      .eq('id', id)
      .eq('tenant_id', this.tenantId!)
      .single();

    const { data, error } = await query;

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      console.error('Error fetching task with relations:', error);
      throw error;
    }

    return data as TaskRow;
  }

  /**
   * Get task history
   *
   * @param taskId The task ID
   * @returns An array of task history entries
   */
  async getTaskHistory(taskId: string): Promise<any[]> {
    const query = this.schemaClient.schema('tenants', 'task_history')
      .select(`
        *,
        changed_by(id, email, first_name, last_name)
      `)
      .eq('task_id', taskId)
      .eq('tenant_id', this.tenantId!)
      .order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching task history:', error);
      throw error;
    }

    return data;
  }

  /**
   * Get upcoming tasks (due in the next N days)
   *
   * @param days Number of days to look ahead
   * @returns An array of upcoming tasks
   */
  async getUpcomingTasks(days = 7): Promise<TaskRow[]> {
    const now = new Date();
    const future = new Date();
    future.setDate(now.getDate() + days);

    const query = this.schemaClient.schema('tenants', 'tasks')
      .select('*')
      .eq('tenant_id', this.tenantId!)
      .neq('status', 'done')
      .gte('due_date', now.toISOString())
      .lte('due_date', future.toISOString())
      .order('due_date', { ascending: true });

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching upcoming tasks:', error);
      throw error;
    }

    return data as TaskRow[];
  }
}
