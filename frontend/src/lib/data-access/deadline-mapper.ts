/**
 * Deadline Data Mapper
 *
 * Maps between database deadline records and domain deadline objects.
 * Handles deadlines from the tenants.deadlines table.
 */

import { z } from 'zod';
import { createClient } from '@supabase/supabase-js';
import type { Database } from '../supabase/database.types';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient<Database>(supabaseUrl, supabaseKey);

// Deadline status enum
export enum DeadlineStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  OVERDUE = 'OVERDUE',
  CANCELLED = 'CANCELLED'
}

// Deadline priority enum
export enum DeadlinePriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// Domain Deadline Model
export interface Deadline {
  id: string;
  tenantId: string;
  caseId: string;
  title: string;
  description: string | null;
  dueDate: string;
  baseDate: string | null;
  calculationNotes: string | null;
  assignedTo: string[] | null;
  status: DeadlineStatus;
  priority: DeadlinePriority | null;
  ruleId: string | null;
  metadata: Record<string, unknown> | null;
  completedAt: string | null;
  completedBy: string | null;
  createdAt: string | null;
  createdBy: string;
  updatedAt: string | null;
  // Relations
  case?: {
    id: string;
    title: string;
  } | null;
  assignees?: {
    id: string;
    email: string;
    firstName: string | null;
    lastName: string | null;
  }[] | null;
}

// Zod schemas for validation
export const DeadlineSchema = z.object({
  id: z.string().uuid(),
  tenantId: z.string().uuid(),
  caseId: z.string().uuid(),
  title: z.string().min(1, "Title is required"),
  description: z.string().nullable(),
  dueDate: z.string().regex(/^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(\.\d{3})?(Z|[+-]\d{2}:\d{2})?)?$/, "Invalid date format"),
  baseDate: z.string().regex(/^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(\.\d{3})?(Z|[+-]\d{2}:\d{2})?)?$/, "Invalid date format").nullable(),
  calculationNotes: z.string().nullable(),
  assignedTo: z.array(z.string().uuid()).nullable(),
  status: z.enum(['PENDING', 'COMPLETED', 'OVERDUE', 'CANCELLED']),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).nullable(),
  ruleId: z.string().uuid().nullable(),
  metadata: z.record(z.unknown()).nullable(),
  completedAt: z.string().nullable(),
  completedBy: z.string().uuid().nullable(),
  createdAt: z.string().nullable(),
  createdBy: z.string().uuid(),
  updatedAt: z.string().nullable(),
});

export const CreateDeadlineSchema = DeadlineSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  completedAt: true,
  completedBy: true
});

// Update schema - partial schema but requires ID to identify the record
export const UpdateDeadlineSchema = DeadlineSchema.partial().required({
  id: true,
  tenantId: true
});

// Type alias for TypeScript to help with Zod validation
export type UpdateDeadlineInput = z.infer<typeof UpdateDeadlineSchema>;

/**
 * Maps a database deadline row to a domain deadline object
 */
export function mapDeadlineRowToDeadline(row: Database['tenants']['Tables']['deadlines']['Row']): Deadline {
  return {
    id: row.id,
    tenantId: row.tenant_id,
    caseId: row.case_id,
    title: row.title,
    description: row.description,
    dueDate: row.due_date,
    baseDate: row.base_date,
    calculationNotes: row.calculation_notes,
    assignedTo: row.assigned_to,
    status: (row.status as DeadlineStatus) || DeadlineStatus.PENDING,
    priority: (row.priority as DeadlinePriority) || null,
    ruleId: row.rule_id,
    metadata: typeof row.metadata === 'string' ? JSON.parse(row.metadata) : row.metadata,
    completedAt: row.completed_at,
    completedBy: row.completed_by,
    createdAt: row.created_at,
    createdBy: row.created_by,
    updatedAt: row.updated_at,
  };
}

/**
 * Maps a domain deadline object to a database deadline insert
 */
export function mapCreateDeadlineDtoToDeadlineInsert(
  deadline: z.infer<typeof CreateDeadlineSchema>,
  tenantId: string,
  userId: string
): Database['tenants']['Tables']['deadlines']['Insert'] {
  return {
    tenant_id: tenantId,
    case_id: deadline.caseId,
    title: deadline.title,
    description: deadline.description,
    due_date: deadline.dueDate,
    base_date: deadline.baseDate,
    calculation_notes: deadline.calculationNotes,
    assigned_to: deadline.assignedTo,
    status: deadline.status,
    priority: deadline.priority,
    rule_id: deadline.ruleId,
    metadata: deadline.metadata as any,
    created_by: userId,
  };
}

/**
 * Maps a domain deadline update object to a database deadline update
 */
export function mapUpdateDeadlineDtoToDeadlineUpdate(
  deadline: z.infer<typeof UpdateDeadlineSchema>,
  userId: string | null = null
): Database['tenants']['Tables']['deadlines']['Update'] {
  const update: Database['tenants']['Tables']['deadlines']['Update'] = {};

  if (deadline.title !== undefined) update.title = deadline.title;
  if (deadline.description !== undefined) update.description = deadline.description;
  if (deadline.dueDate !== undefined) update.due_date = deadline.dueDate;
  if (deadline.baseDate !== undefined) update.base_date = deadline.baseDate;
  if (deadline.calculationNotes !== undefined) update.calculation_notes = deadline.calculationNotes;
  if (deadline.assignedTo !== undefined) update.assigned_to = deadline.assignedTo;
  if (deadline.status !== undefined) update.status = deadline.status;
  if (deadline.priority !== undefined) update.priority = deadline.priority;
  if (deadline.ruleId !== undefined) update.rule_id = deadline.ruleId;
  if (deadline.metadata !== undefined) update.metadata = deadline.metadata as any;
  if (deadline.completedAt !== undefined) update.completed_at = deadline.completedAt;
  if (deadline.completedBy !== undefined) update.completed_by = deadline.completedBy;

  update.updated_at = new Date().toISOString();
  // Only add updated_by if it exists in the database schema
  if (userId) {
    (update as any).updated_by = userId;
  }

  return update;
}

/**
 * Marks a deadline as completed
 */
export function mapCompleteDeadline(
  deadlineId: string,
  userId: string
): Database['tenants']['Tables']['deadlines']['Update'] {
  // Cast to any to handle updated_by which might not be in the type definition
  return {
    status: DeadlineStatus.COMPLETED,
    completed_at: new Date().toISOString(),
    completed_by: userId,
    updated_at: new Date().toISOString(),
  } as Database['tenants']['Tables']['deadlines']['Update'] & { updated_by: string };
}

/**
 * Validation utilities for working with deadlines
 */
export const DeadlineValidation = {
  /**
   * Check if a deadline is overdue
   */
  isOverdue(deadline: Deadline): boolean {
    if (deadline.status === DeadlineStatus.COMPLETED) return false;
    const dueDate = new Date(deadline.dueDate);
    const now = new Date();
    return dueDate < now;
  },

  /**
   * Check if a deadline is due soon (within the specified days)
   */
  isDueSoon(deadline: Deadline, days = 3): boolean {
    if (deadline.status === DeadlineStatus.COMPLETED) return false;

    const dueDate = new Date(deadline.dueDate);
    const now = new Date();

    // If already overdue, it's not "due soon" - it's already overdue
    if (dueDate < now) return false;

    const timeDiff = dueDate.getTime() - now.getTime();
    const daysDiff = timeDiff / (1000 * 3600 * 24);

    return daysDiff <= days;
  },

  /**
   * Check if a deadline is due today
   */
  isDueToday(deadline: Deadline): boolean {
    if (deadline.status === DeadlineStatus.COMPLETED) return false;

    const dueDate = new Date(deadline.dueDate);
    const now = new Date();

    return dueDate.getFullYear() === now.getFullYear() &&
           dueDate.getMonth() === now.getMonth() &&
           dueDate.getDate() === now.getDate();
  },

  /**
   * Check if a deadline is high priority
   */
  isHighPriority(deadline: Deadline): boolean {
    return deadline.priority === DeadlinePriority.HIGH ||
           deadline.priority === DeadlinePriority.URGENT;
  },

  /**
   * Check if a deadline is assigned to a specific user
   */
  isAssignedToUser(deadline: Deadline, userId: string): boolean {
    return Array.isArray(deadline.assignedTo) &&
           deadline.assignedTo.includes(userId);
  },

  /**
   * Calculate days until a deadline
   * Returns negative number if deadline is in the past
   */
  daysUntil(deadline: Deadline): number {
    const dueDate = new Date(deadline.dueDate);
    const now = new Date();

    const timeDiff = dueDate.getTime() - now.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  },

  /**
   * Get a humanized status string for a deadline
   */
  getStatusLabel(deadline: Deadline): string {
    if (this.isOverdue(deadline) && deadline.status !== DeadlineStatus.COMPLETED) {
      return 'Overdue';
    }

    switch (deadline.status) {
      case DeadlineStatus.COMPLETED:
        return 'Completed';
      case DeadlineStatus.CANCELLED:
        return 'Cancelled';
      case DeadlineStatus.PENDING:
        if (this.isDueToday(deadline)) {
          return 'Due Today';
        } else if (this.isDueSoon(deadline)) {
          return 'Due Soon';
        } else {
          return 'Pending';
        }
      default:
        return deadline.status;
    }
  }
};

/**
 * DeadlineRepository class for working with deadlines
 */
export class DeadlineRepository {
  /**
   * Get all deadlines for a tenant
   */
  static async getAllDeadlines(
    tenantId: string,
    options: {
      status?: DeadlineStatus | DeadlineStatus[];
      limit?: number;
      includeRelations?: boolean;
      caseId?: string;
      userId?: string;
    } = {}
  ): Promise<Deadline[]> {
    try {
      const {
        status,
        limit = 50,
        includeRelations = false,
        caseId,
        userId
      } = options;

      // Basic query - always select from tenants schema
      let query = supabase
        .schema('tenants')
        .from('deadlines')
        .select(includeRelations ?
          `*, case:case_id(id, title)` :
          '*'
        )
        .eq('tenant_id', tenantId);

      // Apply filters
      if (status) {
        if (Array.isArray(status)) {
          query = query.in('status', status);
        } else {
          query = query.eq('status', status);
        }
      }

      if (caseId) {
        query = query.eq('case_id', caseId);
      }

      if (userId) {
        query = query.contains('assigned_to', [userId]);
      }

      // Order and limit
      query = query
        .order('due_date', { ascending: true })
        .limit(limit);

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching deadlines:', error.message);
        return [];
      }

      // Handle potential type issues with the query result
      // Use explicit type assertion to bypass TypeScript's complex union type checking
      const rawData = data as any[] || [];
      const validRows = rawData.filter(row => typeof row === 'object' && row !== null && !('error' in row));
      const deadlines = validRows.map(row => mapDeadlineRowToDeadline(row));

      // Update status of overdue deadlines
      for (const deadline of deadlines) {
        if (DeadlineValidation.isOverdue(deadline) && deadline.status === DeadlineStatus.PENDING) {
          deadline.status = DeadlineStatus.OVERDUE;
        }
      }

      return deadlines;
    } catch (error) {
      console.error('Error in getAllDeadlines:', error);
      return [];
    }
  }

  /**
   * Get a deadline by ID
   */
  static async getDeadlineById(
    deadlineId: string,
    tenantId: string,
    includeRelations = false
  ): Promise<Deadline | null> {
    try {
      const { data, error } = await supabase
        .schema('tenants')
        .from('deadlines')
        .select(includeRelations ?
          `*, case:case_id(id, title)` :
          '*'
        )
        .eq('id', deadlineId)
        .eq('tenant_id', tenantId)
        .single();

      if (error) {
        console.error('Error fetching deadline by ID:', error.message);
        return null;
      }

      if (!data) return null;

      // Ensure we're working with a valid database row
      if (typeof data !== 'object' || data === null) {
        console.error('Invalid data returned from database');
        return null;
      }

      const deadline = mapDeadlineRowToDeadline(data as Database['tenants']['Tables']['deadlines']['Row']);

      // Update status if it's overdue
      if (DeadlineValidation.isOverdue(deadline) && deadline.status === DeadlineStatus.PENDING) {
        deadline.status = DeadlineStatus.OVERDUE;
      }

      return deadline;
    } catch (error) {
      console.error('Error in getDeadlineById:', error);
      return null;
    }
  }

  /**
   * Create a new deadline
   */
  static async createDeadline(
    deadlineData: z.infer<typeof CreateDeadlineSchema>,
    tenantId: string,
    userId: string
  ): Promise<Deadline | null> {
    try {
      // Validate the input data
      CreateDeadlineSchema.parse(deadlineData);

      const deadlineInsert = mapCreateDeadlineDtoToDeadlineInsert(deadlineData as any, tenantId, userId);

      const { data, error } = await supabase
        .schema('tenants')
        .from('deadlines')
        .insert(deadlineInsert)
        .select()
        .single();

      if (error) {
        console.error('Error creating deadline:', error.message);
        return null;
      }

      return mapDeadlineRowToDeadline(data);
    } catch (error) {
      console.error('Error in createDeadline:', error);
      return null;
    }
  }

  /**
   * Update an existing deadline
   */
  static async updateDeadline(
    deadlineData: UpdateDeadlineInput,
    tenantId: string,
    userId: string
  ): Promise<Deadline | null> {
    try {
      // Validate the input data
      // Assert that id exists in the deadlineData object
      if (!deadlineData.id) {
        throw new Error('Missing required property: id');
      }

      // Type cast to fix TypeScript validation error
      const validationData = { ...deadlineData, id: deadlineData.id, tenantId };
      UpdateDeadlineSchema.parse(validationData);

      const { id } = deadlineData;
      // Pass the full deadlineData including id to satisfy the type requirement
      const deadlineUpdate = mapUpdateDeadlineDtoToDeadlineUpdate(deadlineData, userId);

      const { data, error } = await supabase
        .schema('tenants')
        .from('deadlines')
        .update(deadlineUpdate)
        .eq('id', id)
        .eq('tenant_id', tenantId)
        .select()
        .single();

      if (error) {
        console.error('Error updating deadline:', error.message);
        return null;
      }

      return mapDeadlineRowToDeadline(data);
    } catch (error) {
      console.error('Error in updateDeadline:', error);
      return null;
    }
  }

  /**
   * Complete a deadline
   */
  static async completeDeadline(
    deadlineId: string,
    tenantId: string,
    userId: string
  ): Promise<Deadline | null> {
    try {
      const completeUpdate = mapCompleteDeadline(deadlineId, userId);

      const { data, error } = await supabase
        .schema('tenants')
        .from('deadlines')
        .update(completeUpdate)
        .eq('id', deadlineId)
        .eq('tenant_id', tenantId)
        .select()
        .single();

      if (error) {
        console.error('Error completing deadline:', error.message);
        return null;
      }

      return mapDeadlineRowToDeadline(data);
    } catch (error) {
      console.error('Error in completeDeadline:', error);
      return null;
    }
  }

  /**
   * Delete a deadline
   */
  static async deleteDeadline(
    deadlineId: string,
    tenantId: string
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .schema('tenants')
        .from('deadlines')
        .delete()
        .eq('id', deadlineId)
        .eq('tenant_id', tenantId);

      if (error) {
        console.error('Error deleting deadline:', error.message);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in deleteDeadline:', error);
      return false;
    }
  }

  /**
   * Get overdue deadlines for a tenant
   */
  static async getOverdueDeadlines(
    tenantId: string,
    options: {
      limit?: number;
      includeRelations?: boolean;
      caseId?: string;
      userId?: string;
    } = {}
  ): Promise<Deadline[]> {
    try {
      const now = new Date().toISOString();

      const deadlines = await this.getAllDeadlines(tenantId, {
        ...options,
        status: [DeadlineStatus.PENDING]
      });

      return deadlines.filter(deadline => DeadlineValidation.isOverdue(deadline));
    } catch (error) {
      console.error('Error in getOverdueDeadlines:', error);
      return [];
    }
  }

  /**
   * Get upcoming deadlines for a tenant
   */
  static async getUpcomingDeadlines(
    tenantId: string,
    days = 7,
    options: {
      limit?: number;
      includeRelations?: boolean;
      caseId?: string;
      userId?: string;
    } = {}
  ): Promise<Deadline[]> {
    try {
      const now = new Date();
      const future = new Date();
      future.setDate(future.getDate() + days);

      const { limit = 50, includeRelations = false, caseId, userId } = options;

      let query = supabase
        .schema('tenants')
        .from('deadlines')
        .select(includeRelations ?
          `*, case:case_id(id, title)` :
          '*'
        )
        .eq('tenant_id', tenantId)
        .eq('status', DeadlineStatus.PENDING)
        .gte('due_date', now.toISOString())
        .lte('due_date', future.toISOString());

      if (caseId) {
        query = query.eq('case_id', caseId);
      }

      if (userId) {
        query = query.contains('assigned_to', [userId]);
      }

      query = query
        .order('due_date', { ascending: true })
        .limit(limit);

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching upcoming deadlines:', error.message);
        return [];
      }

      // Handle potential type issues with the query result
      // Use explicit type assertion to bypass TypeScript's complex union type checking
      const rawData = data as any[] || [];
      const validRows = rawData.filter(row => typeof row === 'object' && row !== null && !('error' in row));
      return validRows.map(row => mapDeadlineRowToDeadline(row));
    } catch (error) {
      console.error('Error in getUpcomingDeadlines:', error);
      return [];
    }
  }
}
