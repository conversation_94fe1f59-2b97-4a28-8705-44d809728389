import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

export interface LLMSelection {
  id: string;
  tenant_id: string;
  agent: string;
  node: string | null;
  model_name: string;
  temperature: number;
  created_at?: string;
  updated_at?: string;
}

export interface NodeConfig {
  name: string;
  displayName: string;
  currentModel?: string;
  temperature?: number;
  isConfigured: boolean;
  inheritsFrom?: string;
}

export interface AgentConfig {
  name: string;
  displayName: string;
  icon: string;
  nodes: NodeConfig[];
  currentModel?: string;
  temperature?: number;
  isConfigured: boolean;
  inheritsFrom?: string;
}

export interface LLMSelectionsResponse {
  agents: AgentConfig[];
  selections: LLMSelection[];
}

export interface UseLLMSelectionsOptions {
  tenant?: string;
  search?: string;
  provider?: string;
}

export function useLLMSelections(options: UseLLMSelectionsOptions = {}) {
  const [data, setData] = useState<LLMSelectionsResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const { tenant = '*', search = '', provider } = options;

  const fetchSelections = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        tenant,
        ...(search && { search }),
        ...(provider && { provider })
      });

      const response = await fetch(`/api/admin/llm-selections?${params}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch LLM selections');
      }

      const result = await response.json();
      setData(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: `Failed to fetch LLM selections: ${errorMessage}`,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [tenant, search, provider, toast]);

  const updateSelection = useCallback(async (
    agentName: string,
    nodeName: string | null,
    modelName: string,
    temperature = 0.2,
    tenantId = '*'
  ) => {
    try {
      const response = await fetch('/api/admin/llm-selections', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tenant_id: tenantId,
          agent: agentName,
          node: nodeName,
          model_name: modelName,
          temperature,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update LLM selection');
      }

      // Refresh the data
      await fetchSelections();

      toast({
        title: 'Success',
        description: `Updated ${nodeName ? `${agentName}/${nodeName}` : agentName} to use ${modelName}`,
      });

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      toast({
        title: 'Error',
        description: `Failed to update LLM selection: ${errorMessage}`,
        variant: 'destructive',
      });
      return false;
    }
  }, [fetchSelections, toast]);

  const deleteSelection = useCallback(async (
    agentName: string,
    nodeName: string | null,
    tenantId = '*'
  ) => {
    try {
      const params = new URLSearchParams({
        tenant_id: tenantId,
        agent: agentName,
        ...(nodeName && { node: nodeName })
      });

      const response = await fetch(`/api/admin/llm-selections?${params}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete LLM selection');
      }

      // Refresh the data
      await fetchSelections();

      toast({
        title: 'Success',
        description: `Deleted selection for ${nodeName ? `${agentName}/${nodeName}` : agentName}`,
      });

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      toast({
        title: 'Error',
        description: `Failed to delete LLM selection: ${errorMessage}`,
        variant: 'destructive',
      });
      return false;
    }
  }, [fetchSelections, toast]);

  const bulkUpdateSelections = useCallback(async (
    updates: {
      agent: string;
      node: string | null;
      model_name: string;
      temperature?: number;
      tenant_id?: string;
    }[]
  ) => {
    try {
      const promises = updates.map(update =>
        updateSelection(
          update.agent,
          update.node,
          update.model_name,
          update.temperature || 0.2,
          update.tenant_id || '*'
        )
      );

      const results = await Promise.all(promises);
      const successCount = results.filter(Boolean).length;

      if (successCount === updates.length) {
        toast({
          title: 'Success',
          description: `Updated ${successCount} selections successfully`,
        });
      } else {
        toast({
          title: 'Partial Success',
          description: `Updated ${successCount} of ${updates.length} selections`,
          variant: 'destructive',
        });
      }

      return successCount;
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to perform bulk update',
        variant: 'destructive',
      });
      return 0;
    }
  }, [updateSelection, toast]);

  // Fetch data on mount and when dependencies change
  useEffect(() => {
    fetchSelections();
  }, [fetchSelections]);

  return {
    agents: data?.agents || [],
    selections: data?.selections || [],
    isLoading,
    error,
    refetch: fetchSelections,
    updateSelection,
    deleteSelection,
    bulkUpdateSelections,
  };
}

// Helper function to get available models (this should be fetched from the models API)
export function useAvailableModels() {
  const [models, setModels] = useState<{ id: string; name: string; provider: string }[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchModels = async () => {
      try {
        const response = await fetch('/api/admin/models');
        if (response.ok) {
          const data = await response.json();
          setModels(data.models || []);
        }
      } catch (error) {
        console.error('Failed to fetch available models:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchModels();
  }, []);

  return { models, isLoading };
}

// Helper function to parse model string
export function parseModelString(modelString: string): { provider: string; model: string } {
  const [provider, ...modelParts] = modelString.split('/');
  return {
    provider,
    model: modelParts.join('/')
  };
}

// Helper function to format model display name
export function formatModelDisplayName(modelString: string): string {
  const { provider, model } = parseModelString(modelString);
  const providerNames: Record<string, string> = {
    openai: 'OpenAI',
    anthropic: 'Anthropic',
    google: 'Google',
    gemini: 'Gemini',
    groq: 'Groq',
    cohere: 'Cohere',
    mistral: 'Mistral'
  };
  
  return `${providerNames[provider] || provider} ${model}`;
}
