'use client'

import { useState, useCallback } from 'react'
import { toast } from 'sonner'

interface IntakeState {
  currentStep: string
  completedSteps: string[]
  client?: {
    name?: string
    email?: string
    phone?: string
  }
  matter?: {
    practice_area?: string
    work_type?: string
    case_type?: string
    description?: string
    urgency?: string
    display_label?: string
  }
  messages: {
    type: 'human' | 'ai' | 'system'
    content: string
  }[]
  validation_errors?: string[]
  can_proceed?: boolean
  requires_immediate_attention?: boolean
}

interface IntakeResponse {
  success: boolean
  state: IntakeState
  message?: string
  next_step?: string
  display_label?: string
  urgency?: string
  errors?: string[]
}

interface ClassificationResult {
  practice_area: string
  work_type: string
  case_type: string
  urgency: string
  confidence: number
  reasoning: string
  keywords_matched: string[]
  display_label: string
  // Enhanced LLM insights
  key_factors: string[]
  timeline_factors: string[]
  parties_involved: string[]
  potential_damages: string[]
  complexity_indicators: string[]
  red_flags: string[]
  llm_reasoning: string
  fallback_used: boolean
}

interface UseIntakeAgentOptions {
  mode: 'client' | 'staff'
  onComplete?: (state: IntakeState) => void
  onError?: (error: Error) => void
}

export function useIntakeAgent({ mode, onComplete, onError }: UseIntakeAgentOptions) {
  const [state, setState] = useState<IntakeState | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleError = useCallback((err: Error) => {
    console.error('Intake Agent Error:', err)
    setError(err.message)
    onError?.(err)
    toast.error(`Intake Error: ${err.message}`)
  }, [onError])

  const startIntake = useCallback(async (
    initialMessage?: string,
    practiceArea?: string
  ) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/agents/intake/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode,
          message: initialMessage,
          practice_area: practiceArea
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: IntakeResponse = await response.json()

      if (!data.success) {
        throw new Error(data.message || 'Failed to start intake')
      }

      setState(data.state)

      // Check for completion
      if (data.state.currentStep === 'completed') {
        onComplete?.(data.state)
      }

      return data.state

    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error')
      handleError(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [mode, onComplete, handleError])

  const continueIntake = useCallback(async (message: string) => {
    if (!state) {
      throw new Error('No active intake session')
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/agents/intake/continue', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode,
          message,
          state
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: IntakeResponse = await response.json()

      if (!data.success) {
        throw new Error(data.message || 'Failed to continue intake')
      }

      setState(data.state)

      // Check for completion
      if (data.state.currentStep === 'completed') {
        onComplete?.(data.state)
      }

      // Show validation errors if any
      if (data.errors && data.errors.length > 0) {
        data.errors.forEach(error => toast.error(error))
      }

      return data.state

    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error')
      handleError(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [state, mode, onComplete, handleError])

  const classifyMatter = useCallback(async (
    description: string,
    additionalContext?: Record<string, unknown>
  ): Promise<ClassificationResult> => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/agents/intake/classify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          description,
          additional_context: additionalContext
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: ClassificationResult = await response.json()
      return data

    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error')
      handleError(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [handleError])

  const getPracticeAreas = useCallback(async () => {
    try {
      const response = await fetch('/api/agents/intake/practice-areas')
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data.practice_areas

    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error')
      handleError(error)
      throw error
    }
  }, [handleError])

  const getWorkflowSteps = useCallback(async () => {
    try {
      const response = await fetch('/api/agents/intake/workflow-steps')
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data.workflow_steps

    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error')
      handleError(error)
      throw error
    }
  }, [handleError])

  const resetIntake = useCallback(() => {
    setState(null)
    setError(null)
  }, [])

  const getProgress = useCallback(() => {
    if (!state) return { current: 0, total: 7, percentage: 0 }

    const totalSteps = 7
    const completedSteps = state.completedSteps?.length || 0
    const percentage = Math.round((completedSteps / totalSteps) * 100)

    return {
      current: completedSteps,
      total: totalSteps,
      percentage,
      currentStep: state.currentStep,
      completedSteps: state.completedSteps || []
    }
  }, [state])

  const getDisplayLabel = useCallback(() => {
    return state?.matter?.display_label || 'Matter'
  }, [state])

  const getUrgencyLevel = useCallback(() => {
    return state?.matter?.urgency || 'low'
  }, [state])

  const getPracticeArea = useCallback(() => {
    return state?.matter?.practice_area
  }, [state])

  const hasValidationErrors = useCallback(() => {
    return (state?.validation_errors?.length || 0) > 0
  }, [state])

  const canProceed = useCallback(() => {
    return state?.can_proceed !== false
  }, [state])

  const requiresImmediateAttention = useCallback(() => {
    return state?.requires_immediate_attention === true
  }, [state])

  return {
    // State
    state,
    isLoading,
    error,

    // Actions
    startIntake,
    continueIntake,
    classifyMatter,
    getPracticeAreas,
    getWorkflowSteps,
    resetIntake,

    // Computed values
    getProgress,
    getDisplayLabel,
    getUrgencyLevel,
    getPracticeArea,
    hasValidationErrors,
    canProceed,
    requiresImmediateAttention,
  }
}
