/**
 * Real-time Insights Hook
 * 
 * Automatically fetches proactive insights when users return to the app
 * after periods of inactivity (1+ hours).
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/lib/auth/useAuth';

interface QuickInsight {
  type: 'welcome_back' | 'deadline_reminder' | 'activity_summary' | 'suggested_action';
  title: string;
  message: string;
  priority: 'high' | 'medium' | 'low';
  actions?: string[];
  metadata?: Record<string, unknown>;
}

interface InsightResponse {
  insights: QuickInsight[];
  metadata?: {
    inactive_hours?: number;
    activities_while_away?: number;
    upcoming_deadlines?: number;
    trigger?: string;
  };
}

export function useRealtimeInsights() {
  const { user } = useAuth();
  const [insights, setInsights] = useState<QuickInsight[]>([]);
  const [loading, setLoading] = useState(false);
  const [lastFetch, setLastFetch] = useState<Date | null>(null);

  /**
   * Fetch real-time insights from the API
   */
  const fetchInsights = useCallback(async (trigger = 'app_open') => {
    if (!user || loading) return;

    // Don't fetch too frequently (minimum 30 minutes between fetches)
    if (lastFetch && (Date.now() - lastFetch.getTime()) < 30 * 60 * 1000) {
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/insights/realtime', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ trigger }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch insights');
      }

      const data: InsightResponse = await response.json();
      
      if (data.insights && data.insights.length > 0) {
        setInsights(data.insights);
        console.log(`Received ${data.insights.length} real-time insights`, data.metadata);
      } else {
        setInsights([]);
      }

      setLastFetch(new Date());
    } catch (error) {
      console.error('Error fetching real-time insights:', error);
      setInsights([]);
    } finally {
      setLoading(false);
    }
  }, [user, loading, lastFetch]);

  /**
   * Dismiss an insight
   */
  const dismissInsight = useCallback((index: number) => {
    setInsights(prev => prev.filter((_, i) => i !== index));
  }, []);

  /**
   * Dismiss all insights
   */
  const dismissAllInsights = useCallback(() => {
    setInsights([]);
  }, []);

  /**
   * Check for insights on app focus/visibility change
   */
  useEffect(() => {
    if (!user) return;

    const handleVisibilityChange = (): void => {
      if (!document.hidden) {
        // User returned to the app
        fetchInsights('page_focus');
      }
    };

    const handleFocus = (): void => {
      // Window gained focus
      fetchInsights('window_focus');
    };

    // Listen for visibility and focus changes
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    // Fetch insights on initial load
    fetchInsights('app_open');

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [user, fetchInsights]);

  /**
   * Periodic check for insights (every 30 minutes when app is active)
   */
  useEffect(() => {
    if (!user) return;

    const interval = setInterval(() => {
      if (!document.hidden) {
        fetchInsights('periodic_check');
      }
    }, 30 * 60 * 1000); // 30 minutes

    return () => clearInterval(interval);
  }, [user, fetchInsights]);

  return {
    insights,
    loading,
    fetchInsights,
    dismissInsight,
    dismissAllInsights,
    hasInsights: insights.length > 0,
    highPriorityInsights: insights.filter(i => i.priority === 'high'),
    mediumPriorityInsights: insights.filter(i => i.priority === 'medium'),
    lowPriorityInsights: insights.filter(i => i.priority === 'low'),
  };
}
