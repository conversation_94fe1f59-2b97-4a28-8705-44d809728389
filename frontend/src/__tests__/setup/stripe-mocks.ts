/**
 * Stripe Mocks for E2E Testing
 * 
 * Comprehensive mocks for Stripe payment flows including 3D Secure,
 * multi-currency payments, and various failure scenarios.
 */

import { vi } from 'vitest';
import type { Stripe } from 'stripe';

// Mock payment method IDs for different scenarios
export const MOCK_PAYMENT_METHODS = {
  VALID_CARD: 'pm_card_visa',
  VALID_CARD_3DS: 'pm_card_threeDSecureRequired',
  DECLINING_CARD: 'pm_card_visa_chargeDeclined',
  INSUFFICIENT_FUNDS: 'pm_card_visa_chargeDeclinedInsufficientFunds',
  EXPIRED_CARD: 'pm_card_visa_chargeDeclinedExpiredCard',
  PROCESSING_ERROR: 'pm_card_visa_chargeDeclinedProcessingError',
  SEPA_DEBIT: 'pm_sepa_debit',
  ACH_DEBIT: 'pm_usBankAccount',
  BANCONTACT: 'pm_bancontact',
  IDEAL: 'pm_ideal',
  SOFORT: 'pm_sofort'
};

// Mock payment intent IDs for different scenarios
export const MOCK_PAYMENT_INTENTS = {
  SUCCEEDED: 'pi_succeeded',
  REQUIRES_ACTION: 'pi_requires_action',
  REQUIRES_3DS: 'pi_requires_action_3ds',
  REQUIRES_PAYMENT_METHOD: 'pi_requires_payment_method',
  CANCELED: 'pi_canceled',
  PROCESSING: 'pi_processing'
};

// Mock subscription IDs
export const MOCK_SUBSCRIPTIONS = {
  ACTIVE: 'sub_active',
  TRIALING: 'sub_trialing',
  PAST_DUE: 'sub_past_due',
  CANCELED: 'sub_canceled',
  INCOMPLETE: 'sub_incomplete'
};

// Mock customer IDs
export const MOCK_CUSTOMERS = {
  VALID: 'cus_valid',
  DELINQUENT: 'cus_delinquent',
  DELETED: 'cus_deleted'
};

// Mock 3D Secure responses
export const MOCK_3DS_RESPONSES = {
  AUTHENTICATED: {
    status: 'succeeded',
    outcome: {
      network_status: 'approved_by_network',
      reason: null,
      risk_level: 'normal',
      risk_score: 35,
      type: 'authorized'
    }
  },
  FAILED: {
    status: 'failed',
    outcome: {
      network_status: 'declined_by_network',
      reason: 'generic_decline',
      risk_level: 'highest',
      risk_score: 98,
      type: 'blocked'
    }
  },
  REQUIRES_CHALLENGE: {
    status: 'requires_action',
    next_action: {
      type: 'redirect_to_url',
      redirect_to_url: {
        url: 'https://hooks.stripe.com/redirect/authenticate/src_test_3ds',
        return_url: 'https://example.com/return'
      }
    }
  }
};

// Currency exchange rates for multi-currency testing
export const MOCK_EXCHANGE_RATES = {
  USD: 1.0,
  EUR: 0.85,
  GBP: 0.73,
  CAD: 1.25
};

/**
 * Create a mock Stripe instance with comprehensive test scenarios
 */
export function createMockStripe() {
  const mockStripe = {
    // Payment Methods API
    paymentMethods: {
      create: vi.fn().mockImplementation((params: any) => {
        const { type, card } = params;
        
        // Simulate different card behaviors based on card number
        if (card?.number === '****************') {
          throw new Error('Your card was declined.');
        }
        
        if (card?.number === '****************') {
          return Promise.resolve({
            id: MOCK_PAYMENT_METHODS.VALID_CARD_3DS,
            type: 'card',
            card: {
              brand: 'visa',
              last4: '3220',
              exp_month: card.exp_month,
              exp_year: card.exp_year,
              three_d_secure_usage: { supported: true }
            }
          });
        }
        
        // Default successful card
        return Promise.resolve({
          id: MOCK_PAYMENT_METHODS.VALID_CARD,
          type: type || 'card',
          card: {
            brand: 'visa',
            last4: '4242',
            exp_month: card?.exp_month || 12,
            exp_year: card?.exp_year || 2025
          }
        });
      }),
      
      retrieve: vi.fn().mockImplementation((id: string) => {
        const methods: Record<string, any> = {
          [MOCK_PAYMENT_METHODS.VALID_CARD]: {
            id: MOCK_PAYMENT_METHODS.VALID_CARD,
            type: 'card',
            card: { brand: 'visa', last4: '4242' }
          },
          [MOCK_PAYMENT_METHODS.VALID_CARD_3DS]: {
            id: MOCK_PAYMENT_METHODS.VALID_CARD_3DS,
            type: 'card',
            card: { 
              brand: 'visa', 
              last4: '3220',
              three_d_secure_usage: { supported: true }
            }
          },
          [MOCK_PAYMENT_METHODS.SEPA_DEBIT]: {
            id: MOCK_PAYMENT_METHODS.SEPA_DEBIT,
            type: 'sepa_debit',
            sepa_debit: { last4: '3000' }
          }
        };
        
        return Promise.resolve(methods[id] || null);
      }),
      
      attach: vi.fn().mockResolvedValue({ id: MOCK_PAYMENT_METHODS.VALID_CARD }),
      detach: vi.fn().mockResolvedValue({ id: MOCK_PAYMENT_METHODS.VALID_CARD }),
      update: vi.fn().mockResolvedValue({ id: MOCK_PAYMENT_METHODS.VALID_CARD })
    },
    
    // Payment Intents API
    paymentIntents: {
      create: vi.fn().mockImplementation((params: any) => {
        const { amount, currency, payment_method, confirm } = params;
        
        // Simulate different scenarios based on payment method
        if (payment_method === MOCK_PAYMENT_METHODS.DECLINING_CARD) {
          return Promise.resolve({
            id: MOCK_PAYMENT_INTENTS.REQUIRES_PAYMENT_METHOD,
            status: 'requires_payment_method',
            amount,
            currency,
            last_payment_error: {
              code: 'card_declined',
              message: 'Your card was declined.',
              decline_code: 'generic_decline'
            }
          });
        }
        
        if (payment_method === MOCK_PAYMENT_METHODS.VALID_CARD_3DS) {
          return Promise.resolve({
            id: MOCK_PAYMENT_INTENTS.REQUIRES_3DS,
            status: 'requires_action',
            amount,
            currency,
            client_secret: 'pi_test_secret_3ds',
            next_action: MOCK_3DS_RESPONSES.REQUIRES_CHALLENGE.next_action
          });
        }
        
        // Default successful payment
        return Promise.resolve({
          id: MOCK_PAYMENT_INTENTS.SUCCEEDED,
          status: confirm ? 'succeeded' : 'requires_confirmation',
          amount,
          currency,
          client_secret: 'pi_test_secret_succeeded',
          payment_method
        });
      }),
      
      confirm: vi.fn().mockImplementation((id: string) => {
        if (id === MOCK_PAYMENT_INTENTS.REQUIRES_3DS) {
          return Promise.resolve({
            id,
            status: 'requires_action',
            next_action: MOCK_3DS_RESPONSES.REQUIRES_CHALLENGE.next_action
          });
        }
        
        return Promise.resolve({
          id,
          status: 'succeeded'
        });
      }),
      
      cancel: vi.fn().mockImplementation((id: string) => {
        return Promise.resolve({
          id,
          status: 'canceled',
          cancellation_reason: 'requested_by_customer'
        });
      }),
      
      retrieve: vi.fn().mockImplementation((id: string) => {
        const intents: Record<string, any> = {
          [MOCK_PAYMENT_INTENTS.SUCCEEDED]: {
            id: MOCK_PAYMENT_INTENTS.SUCCEEDED,
            status: 'succeeded',
            amount: 5000,
            currency: 'usd'
          },
          [MOCK_PAYMENT_INTENTS.REQUIRES_3DS]: {
            id: MOCK_PAYMENT_INTENTS.REQUIRES_3DS,
            status: 'requires_action',
            next_action: MOCK_3DS_RESPONSES.REQUIRES_CHALLENGE.next_action
          }
        };
        
        return Promise.resolve(intents[id] || null);
      })
    },
    
    // Subscriptions API
    subscriptions: {
      create: vi.fn().mockImplementation((params: any) => {
        const { customer, items, trial_period_days } = params;
        
        return Promise.resolve({
          id: trial_period_days ? MOCK_SUBSCRIPTIONS.TRIALING : MOCK_SUBSCRIPTIONS.ACTIVE,
          customer,
          status: trial_period_days ? 'trialing' : 'active',
          current_period_start: Math.floor(Date.now() / 1000),
          current_period_end: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60,
          items: {
            data: items.map((item: any, index: number) => ({
              id: `si_${index}`,
              price: item.price,
              quantity: item.quantity || 1
            }))
          }
        });
      }),
      
      update: vi.fn().mockImplementation((id: string, params: any) => {
        return Promise.resolve({
          id,
          ...params,
          status: 'active'
        });
      }),
      
      cancel: vi.fn().mockImplementation((id: string) => {
        return Promise.resolve({
          id,
          status: 'canceled',
          canceled_at: Math.floor(Date.now() / 1000)
        });
      }),
      
      retrieve: vi.fn().mockImplementation((id: string) => {
        const subscriptions: Record<string, any> = {
          [MOCK_SUBSCRIPTIONS.ACTIVE]: {
            id: MOCK_SUBSCRIPTIONS.ACTIVE,
            status: 'active',
            current_period_end: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60
          },
          [MOCK_SUBSCRIPTIONS.TRIALING]: {
            id: MOCK_SUBSCRIPTIONS.TRIALING,
            status: 'trialing',
            trial_end: Math.floor(Date.now() / 1000) + 14 * 24 * 60 * 60
          }
        };
        
        return Promise.resolve(subscriptions[id] || null);
      })
    },
    
    // Customers API
    customers: {
      create: vi.fn().mockImplementation((params: any) => {
        return Promise.resolve({
          id: MOCK_CUSTOMERS.VALID,
          email: params.email,
          name: params.name,
          metadata: params.metadata || {}
        });
      }),
      
      retrieve: vi.fn().mockImplementation((id: string) => {
        const customers: Record<string, any> = {
          [MOCK_CUSTOMERS.VALID]: {
            id: MOCK_CUSTOMERS.VALID,
            email: '<EMAIL>',
            delinquent: false
          },
          [MOCK_CUSTOMERS.DELINQUENT]: {
            id: MOCK_CUSTOMERS.DELINQUENT,
            email: '<EMAIL>',
            delinquent: true
          }
        };
        
        return Promise.resolve(customers[id] || null);
      }),
      
      update: vi.fn().mockResolvedValue({ id: MOCK_CUSTOMERS.VALID })
    },
    
    // Checkout Sessions API
    checkout: {
      sessions: {
        create: vi.fn().mockImplementation((params: any) => {
          return Promise.resolve({
            id: 'cs_test_session',
            url: 'https://checkout.stripe.com/pay/cs_test_session',
            payment_status: 'unpaid',
            status: 'open',
            success_url: params.success_url,
            cancel_url: params.cancel_url
          });
        }),
        
        retrieve: vi.fn().mockImplementation((id: string) => {
          return Promise.resolve({
            id,
            payment_status: 'paid',
            status: 'complete',
            payment_intent: MOCK_PAYMENT_INTENTS.SUCCEEDED
          });
        })
      }
    },
    
    // Prices API for multi-currency
    prices: {
      create: vi.fn().mockImplementation((params: any) => {
        const { unit_amount, currency } = params;
        const exchangeRate = MOCK_EXCHANGE_RATES[currency.toUpperCase() as keyof typeof MOCK_EXCHANGE_RATES] || 1;
        
        return Promise.resolve({
          id: `price_${currency}_${unit_amount}`,
          unit_amount: Math.round(unit_amount * exchangeRate),
          currency,
          type: 'one_time'
        });
      })
    }
  };
  
  return mockStripe;
}

/**
 * Helper to simulate 3D Secure authentication flow
 */
export function simulate3DSecureFlow(
  paymentIntentId: string,
  success = true
): Promise<any> {
  return new Promise((resolve) => {
    // Simulate authentication delay
    setTimeout(() => {
      if (success) {
        resolve({
          id: paymentIntentId,
          status: 'succeeded',
          outcome: MOCK_3DS_RESPONSES.AUTHENTICATED.outcome
        });
      } else {
        resolve({
          id: paymentIntentId,
          status: 'requires_payment_method',
          last_payment_error: {
            code: 'authentication_failed',
            message: '3D Secure authentication failed'
          }
        });
      }
    }, 1000); // 1 second delay to simulate authentication
  });
}

/**
 * Helper to simulate webhook events
 */
export function createMockWebhookEvent(
  type: string,
  data: any
): Stripe.Event {
  return {
    id: 'evt_test_webhook',
    object: 'event',
    api_version: '2025-06-30.basil',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: data,
      previous_attributes: {}
    },
    livemode: false,
    pending_webhooks: 0,
    request: { id: null, idempotency_key: null },
    type
  } as Stripe.Event;
}

/**
 * Mock Stripe webhook signature verification
 */
export function mockWebhookSignatureVerification(valid = true) {
  return {
    constructEvent: vi.fn().mockImplementation((payload, sig, secret) => {
      if (!valid) {
        throw new Error('Webhook signature verification failed');
      }
      return JSON.parse(payload);
    })
  };
}