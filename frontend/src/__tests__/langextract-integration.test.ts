/**
 * LangExtract Integration Tests
 * Comprehensive test suite for LangExtract medical records integration
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { LangExtractService } from '@/lib/services/medical/langextract-service'
import { structuredExtractionService } from '@/lib/services/medical/structured-extraction-service'
import { providerFollowupService } from '@/lib/services/medical/provider-followup-service'
import type {
  FollowUpCue,
  LangExtractRequest,
  LangExtractResponse
} from '@/types/medical-records';
import {
  MedicationRow,
  RadiologyRow,
  ProblemRow
} from '@/types/medical-records'

// Mock data
const MOCK_DOCUMENT_AI_RESPONSE = {
  pages: [
    {
      layout: {
        textAnchor: {
          textSegments: [
            { startIndex: '0', endIndex: '500' },
            { startIndex: '500', endIndex: '1000' },
          ]
        }
      }
    },
    {
      layout: {
        textAnchor: {
          textSegments: [
            { startIndex: '1000', endIndex: '1500' },
          ]
        }
      }
    }
  ]
}

const MOCK_MEDICAL_TEXT = `
DISCHARGE SUMMARY

Patient: John <PERSON>e
Date: 03/15/2024

MEDICATIONS:
1. Metformin 1000mg PO BID
2. Lisinopril 10mg PO daily 
3. Naproxen 500mg PO BID PRN pain

RADIOLOGY:
MRI of lumbar spine shows mild degenerative changes at L4-L5. No acute abnormality.

PROBLEMS:
1. Type 2 diabetes mellitus
2. Hypertension
3. Chronic low back pain

FOLLOW-UP:
Follow up with orthopedics in 6 weeks for back pain evaluation.
MRI to be scheduled if symptoms worsen.
`

const MOCK_LANGEXTRACT_MEDICATIONS_RESPONSE: LangExtractResponse = {
  extractions: [
    {
      class: 'Medication',
      attributes: {
        drug: 'Metformin',
        dose: '1000mg',
        route: 'PO',
        frequency: 'BID'
      },
      span: { start: 95, end: 119 }
    },
    {
      class: 'Medication', 
      attributes: {
        drug: 'Lisinopril',
        dose: '10mg',
        route: 'PO', 
        frequency: 'daily'
      },
      span: { start: 123, end: 146 }
    },
    {
      class: 'Medication',
      attributes: {
        drug: 'Naproxen',
        dose: '500mg',
        route: 'PO',
        frequency: 'BID PRN pain'
      },
      span: { start: 150, end: 178 }
    }
  ],
  reviewHtml: '<html><body>Interactive review</body></html>'
}

const MOCK_LANGEXTRACT_RADIOLOGY_RESPONSE: LangExtractResponse = {
  extractions: [
    {
      class: 'Study',
      attributes: {
        modality: 'MRI',
        region: 'lumbar spine'
      },
      span: { start: 190, end: 210 }
    },
    {
      class: 'Finding',
      attributes: {
        bodyPart: 'L4-L5',
        description: 'mild degenerative changes'
      },
      span: { start: 217, end: 248 }
    },
    {
      class: 'Impression',
      attributes: {
        description: 'No acute abnormality'
      },
      span: { start: 250, end: 270 }
    }
  ]
}

// Mock fetch globally
global.fetch = jest.fn()

describe('LangExtract Integration', () => {
  let langExtractService: LangExtractService

  beforeEach(() => {
    jest.clearAllMocks()
    langExtractService = new LangExtractService()
    
    // Mock environment variables
    process.env.LANGEXTRACT_SERVICE_URL = 'http://localhost:8080'
    process.env.LANGEXTRACT_AUTH_TOKEN = 'test-token'
  })

  describe('LangExtractService', () => {
    it('should extract medication data successfully', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => MOCK_LANGEXTRACT_MEDICATIONS_RESPONSE
      } as Response)

      const request: LangExtractRequest = {
        matterId: 'matter-123',
        documentId: 'doc-456',
        text: MOCK_MEDICAL_TEXT,
        task: 'medications'
      }

      const response = await langExtractService.extractStructuredData(request)

      expect(response.extractions).toHaveLength(3)
      expect(response.extractions[0].class).toBe('Medication')
      expect(response.extractions[0].attributes.drug).toBe('Metformin')
      expect(response.reviewHtml).toContain('Interactive review')
    })

    it('should process all extraction types', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>
      
      // Mock multiple calls for different extraction types
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => MOCK_LANGEXTRACT_MEDICATIONS_RESPONSE
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => MOCK_LANGEXTRACT_RADIOLOGY_RESPONSE
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ extractions: [] })
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ extractions: [] })
        } as Response)

      const result = await langExtractService.processAllExtractions(
        'matter-123',
        'doc-456',
        MOCK_MEDICAL_TEXT,
        MOCK_DOCUMENT_AI_RESPONSE
      )

      expect(result.medications).toHaveLength(3)
      expect(result.radiology).toHaveLength(1)
      expect(result.problems).toHaveLength(0)
      expect(result.followups).toHaveLength(0)
      expect(result.confidence).toBeGreaterThan(0)
    })

    it('should map text spans to page numbers', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => MOCK_LANGEXTRACT_MEDICATIONS_RESPONSE
      } as Response)

      const result = await langExtractService.processAllExtractions(
        'matter-123',
        'doc-456',
        MOCK_MEDICAL_TEXT,
        MOCK_DOCUMENT_AI_RESPONSE
      )

      // First medication should be on page 1 (span 95-119 falls within 0-500)
      expect(result.medications[0].evidence[0].page).toBe(1)
      expect(result.medications[0].evidence[0].quote).toBe('Metformin 1000mg PO BID')
    })

    it('should handle service errors gracefully', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      } as Response)

      const request: LangExtractRequest = {
        matterId: 'matter-123',
        documentId: 'doc-456',
        text: MOCK_MEDICAL_TEXT,
        task: 'medications'
      }

      await expect(langExtractService.extractStructuredData(request))
        .rejects.toThrow('LangExtract service failed: 500 Internal Server Error')
    })
  })

  describe('Data Type Processing', () => {
    it('should process medications with proper structure', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => MOCK_LANGEXTRACT_MEDICATIONS_RESPONSE
      } as Response)

      const result = await langExtractService.processAllExtractions(
        'matter-123',
        'doc-456', 
        MOCK_MEDICAL_TEXT,
        MOCK_DOCUMENT_AI_RESPONSE
      )

      const metformin = result.medications.find(med => med.drug === 'Metformin')
      expect(metformin).toBeDefined()
      expect(metformin?.dose).toBe('1000mg')
      expect(metformin?.route).toBe('PO')
      expect(metformin?.frequency).toBe('BID')
      expect(metformin?.evidence).toHaveLength(1)
      expect(metformin?.evidence[0].documentId).toBe('doc-456')
    })

    it('should process radiology with findings and impressions', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => MOCK_LANGEXTRACT_RADIOLOGY_RESPONSE
      } as Response)

      const result = await langExtractService.processAllExtractions(
        'matter-123',
        'doc-456',
        MOCK_MEDICAL_TEXT,
        MOCK_DOCUMENT_AI_RESPONSE
      )

      expect(result.radiology).toHaveLength(1)
      const study = result.radiology[0]
      expect(study.study).toBe('MRI lumbar spine')
      expect(study.bodyPart).toBe('lumbar spine')
      expect(study.finding).toContain('mild degenerative changes')
      expect(study.impression).toContain('No acute abnormality')
    })
  })

  describe('Provider Follow-up Service', () => {
    it('should create follow-up tasks from cues', async () => {
      const followupCue: FollowUpCue = {
        cue: 'Follow up with orthopedics in 6 weeks',
        target: 'orthopedics',
        dueDateGuess: '6 weeks',
        evidence: [{
          documentId: 'doc-456',
          page: 1,
          quote: 'Follow up with orthopedics in 6 weeks'
        }]
      }

      // Mock Supabase client
      const mockSupabase = {
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: { id: 'user-123' } },
            error: null
          })
        },
        from: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { tenant_id: 'tenant-123' }
              })
            })
          }),
          insert: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: {
                  id: 'task-123',
                  matter_id: 'matter-123',
                  title: 'Follow up with orthopedics',
                  description: 'Medical follow-up required: Follow up with orthopedics in 6 weeks',
                  priority: 'medium',
                  target_provider: 'orthopedics',
                  due_date: '2024-04-26',
                  status: 'pending',
                  followup_cue_id: '',
                  created_at: '2024-03-15T10:00:00Z',
                  updated_at: '2024-03-15T10:00:00Z'
                }
              })
            })
          })
        })
      }

      // Temporarily replace the service's supabase client
      const originalClient = (providerFollowupService as any).supabase
      ;(providerFollowupService as any).supabase = mockSupabase

      try {
        const task = await providerFollowupService.createFollowupTask(
          'matter-123',
          followupCue
        )

        expect(task.title).toBe('Follow up with orthopedics')
        expect(task.targetProvider).toBe('orthopedics')
        expect(task.priority).toBe('medium')
        expect(task.status).toBe('pending')
      } finally {
        // Restore original client
        ;(providerFollowupService as any).supabase = originalClient
      }
    })

    it('should determine correct task priority', async () => {
      const urgentCue: FollowUpCue = {
        cue: 'Immediate follow-up required for severe pain',
        evidence: []
      }

      const normalCue: FollowUpCue = {
        cue: 'Routine follow-up in 3 months',
        evidence: []
      }

      // Test priority determination through the service's private method
      // Note: In a real implementation, you might expose this as a public method for testing
      const service = providerFollowupService as any
      
      // We can't directly test private methods, but we can test the behavior
      // through the public createFollowupTask method
      expect(urgentCue.cue).toContain('Immediate')
      expect(normalCue.cue).toContain('Routine')
    })
  })

  describe('Error Handling and Edge Cases', () => {
    it('should handle malformed Document AI response', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => MOCK_LANGEXTRACT_MEDICATIONS_RESPONSE
      } as Response)

      const malformedDocAI = {
        pages: [] // Empty pages array
      }

      const result = await langExtractService.processAllExtractions(
        'matter-123',
        'doc-456',
        MOCK_MEDICAL_TEXT,
        malformedDocAI
      )

      // Should still process but page numbers might default to 1
      expect(result.medications).toHaveLength(3)
      expect(result.medications[0].evidence[0].page).toBe(1)
    })

    it('should handle empty extraction responses', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ extractions: [] })
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ extractions: [] })
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ extractions: [] })
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ extractions: [] })
        } as Response)

      const result = await langExtractService.processAllExtractions(
        'matter-123',
        'doc-456',
        'No medical content',
        MOCK_DOCUMENT_AI_RESPONSE
      )

      expect(result.medications).toHaveLength(0)
      expect(result.radiology).toHaveLength(0)
      expect(result.problems).toHaveLength(0)
      expect(result.followups).toHaveLength(0)
      expect(result.confidence).toBe(0)
    })

    it('should handle network failures gracefully', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      const request: LangExtractRequest = {
        matterId: 'matter-123',
        documentId: 'doc-456',
        text: MOCK_MEDICAL_TEXT,
        task: 'medications'
      }

      await expect(langExtractService.extractStructuredData(request))
        .rejects.toThrow('Extraction failed: Network error')
    })
  })

  describe('Integration Performance', () => {
    it('should process large documents efficiently', async () => {
      const largeMedicalText = MOCK_MEDICAL_TEXT.repeat(100) // Simulate large document
      
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => MOCK_LANGEXTRACT_MEDICATIONS_RESPONSE
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ extractions: [] })
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ extractions: [] })
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ extractions: [] })
        } as Response)

      const startTime = Date.now()
      
      const result = await langExtractService.processAllExtractions(
        'matter-123',
        'doc-456',
        largeMedicalText,
        MOCK_DOCUMENT_AI_RESPONSE
      )

      const processingTime = Date.now() - startTime
      
      expect(processingTime).toBeLessThan(10000) // Should complete within 10 seconds
      expect(result.medications).toHaveLength(3)
    })
  })

  describe('HIPAA Compliance', () => {
    it('should not log sensitive medical information', () => {
      const consoleSpy = jest.spyOn(console, 'log')
      
      // Process medical text that contains PHI
      const phiText = 'Patient: John Doe, SSN: ***********, DOB: 01/01/1980'
      
      // Any logging should not contain actual PHI
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('***********')
      )
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('John Doe')
      )
      
      consoleSpy.mockRestore()
    })
  })
})

describe('Gold Dataset Validation', () => {
  // These tests would run against a curated set of real (de-identified) medical documents
  // to validate accuracy and ensure the system meets the 90% precision requirement
  
  const GOLD_DATASET = [
    {
      name: 'discharge_summary_1',
      text: 'Sample discharge summary...',
      expectedMedications: 3,
      expectedRadiology: 1,
      expectedProblems: 2,
      expectedFollowups: 1
    }
    // More gold standard examples...
  ]

  it.skip('should meet 90% precision on gold dataset', async () => {
    // This test would be skipped in CI but run manually for validation
    const totalCorrect = 0
    const totalExtracted = 0

    for (const goldCase of GOLD_DATASET) {
      // Process each gold case and compare against expected results
      // Calculate precision metrics
    }

    const precision = totalCorrect / totalExtracted
    expect(precision).toBeGreaterThanOrEqual(0.9)
  })
})