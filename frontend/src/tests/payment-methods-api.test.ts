/**
 * Payment Methods API Integration Tests
 * 
 * Tests for the payment methods API endpoints including
 * availability, validation, preferences, and Stripe integration.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import type { NextRequest } from 'next/server';

// Mock the API route handlers
const mockApiHandlers = {
  GET: vi.fn(),
  POST: vi.fn(),
  PUT: vi.fn()
};

// Mock Next.js request/response
const createMockRequest = (method: string, body?: any, searchParams?: Record<string, string>) => {
  const url = new URL('http://localhost:3000/api/payment-methods');
  if (searchParams) {
    Object.entries(searchParams).forEach(([key, value]) => {
      url.searchParams.set(key, value);
    });
  }

  return {
    method,
    json: vi.fn(() => Promise.resolve(body)),
    url: url.toString(),
    nextUrl: url
  } as unknown as NextRequest;
};

const createMockResponse = (data: any, status = 200) => ({
  json: vi.fn(() => Promise.resolve(data)),
  status,
  ok: status >= 200 && status < 300
});

describe('Payment Methods API Endpoints', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('GET /api/payment-methods', () => {
    it('should return available payment methods for US', async () => {
      const mockResponse = {
        payment_methods: [
          {
            id: '1',
            code: 'card',
            name: 'Credit/Debit Card',
            country_code: 'US',
            currency_code: 'USD',
            is_primary: true,
            processing_fee_percentage: 2.9,
            processing_fee_fixed_cents: 30,
            estimated_completion_time: 'Instant'
          },
          {
            id: '2',
            code: 'ach_debit',
            name: 'ACH Direct Debit',
            country_code: 'US',
            currency_code: 'USD',
            is_primary: false,
            processing_fee_percentage: 0.8,
            processing_fee_fixed_cents: 5,
            estimated_completion_time: '3-5 business days'
          }
        ]
      };

      const request = createMockRequest('GET', null, {
        country_code: 'US',
        currency_code: 'USD'
      });

      // Mock the API response
      global.fetch = vi.fn(() => 
        Promise.resolve(createMockResponse(mockResponse))
      ) as any;

      const response = await fetch('/api/payment-methods?country_code=US&currency_code=USD');
      const data = await response.json();

      expect(data.payment_methods).toHaveLength(2);
      expect(data.payment_methods[0].code).toBe('card');
      expect(data.payment_methods[1].code).toBe('ach_debit');
    });

    it('should return available payment methods for Belgium', async () => {
      const mockResponse = {
        payment_methods: [
          {
            id: '3',
            code: 'card',
            name: 'Credit/Debit Card',
            country_code: 'BE',
            currency_code: 'EUR',
            is_primary: true,
            processing_fee_percentage: 1.4,
            processing_fee_fixed_cents: 25
          },
          {
            id: '4',
            code: 'sepa_debit',
            name: 'SEPA Direct Debit',
            country_code: 'BE',
            currency_code: 'EUR',
            is_primary: false,
            processing_fee_percentage: 0.35,
            processing_fee_fixed_cents: 0
          },
          {
            id: '5',
            code: 'bancontact',
            name: 'Bancontact',
            country_code: 'BE',
            currency_code: 'EUR',
            is_primary: false,
            processing_fee_percentage: 1.4,
            processing_fee_fixed_cents: 25
          }
        ]
      };

      global.fetch = vi.fn(() => 
        Promise.resolve(createMockResponse(mockResponse))
      ) as any;

      const response = await fetch('/api/payment-methods?country_code=BE&currency_code=EUR');
      const data = await response.json();

      expect(data.payment_methods).toHaveLength(3);
      expect(data.payment_methods.some((pm: any) => pm.code === 'sepa_debit')).toBe(true);
      expect(data.payment_methods.some((pm: any) => pm.code === 'bancontact')).toBe(true);
    });

    it('should filter payment methods by amount', async () => {
      const mockResponse = {
        payment_methods: [
          {
            id: '1',
            code: 'card',
            min_amount_cents: 50,
            max_amount_cents: 100000
          }
        ]
      };

      global.fetch = vi.fn(() => 
        Promise.resolve(createMockResponse(mockResponse))
      ) as any;

      const response = await fetch('/api/payment-methods?country_code=US&currency_code=USD&amount_cents=1000');
      const data = await response.json();

      expect(data.payment_methods).toHaveLength(1);
    });

    it('should handle invalid country code', async () => {
      const mockResponse = {
        error: 'Invalid country code: XX'
      };

      global.fetch = vi.fn(() => 
        Promise.resolve(createMockResponse(mockResponse, 400))
      ) as any;

      const response = await fetch('/api/payment-methods?country_code=XX&currency_code=USD');
      expect(response.status).toBe(400);
    });
  });

  describe('POST /api/payment-methods', () => {
    it('should create payment method for valid card data', async () => {
      const requestBody = {
        payment_method_code: 'card',
        country_code: 'US',
        currency_code: 'USD',
        tenant_id: 'tenant_123',
        payment_data: {
          number: '****************',
          exp_month: '12',
          exp_year: '2025',
          cvc: '123'
        }
      };

      const mockResponse = {
        stripe_payment_method_id: 'pm_test_123',
        requires_action: false,
        status: 'succeeded',
        processing_log_id: 'log_123'
      };

      global.fetch = vi.fn(() => 
        Promise.resolve(createMockResponse(mockResponse))
      ) as any;

      const response = await fetch('/api/payment-methods', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();
      expect(data.stripe_payment_method_id).toBe('pm_test_123');
      expect(data.status).toBe('succeeded');
    });

    it('should create payment method for SEPA direct debit', async () => {
      const requestBody = {
        payment_method_code: 'sepa_debit',
        country_code: 'BE',
        currency_code: 'EUR',
        tenant_id: 'tenant_123',
        payment_data: {
          iban: '****************'
        }
      };

      const mockResponse = {
        stripe_payment_method_id: 'pm_test_sepa_123',
        requires_action: false,
        status: 'succeeded',
        processing_log_id: 'log_124'
      };

      global.fetch = vi.fn(() => 
        Promise.resolve(createMockResponse(mockResponse))
      ) as any;

      const response = await fetch('/api/payment-methods', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();
      expect(data.stripe_payment_method_id).toBe('pm_test_sepa_123');
    });

    it('should handle invalid payment data', async () => {
      const requestBody = {
        payment_method_code: 'card',
        country_code: 'US',
        currency_code: 'USD',
        tenant_id: 'tenant_123',
        payment_data: {
          number: '1234567890123456', // Invalid Luhn
          exp_month: '13', // Invalid month
          exp_year: '2020', // Expired
          cvc: '12' // Too short
        }
      };

      const mockResponse = {
        error: 'Validation failed',
        validation_errors: [
          { field: 'number', message: 'Invalid card number' },
          { field: 'exp_month', message: 'Invalid expiry month' },
          { field: 'exp_year', message: 'Card has expired' },
          { field: 'cvc', message: 'CVC too short' }
        ]
      };

      global.fetch = vi.fn(() => 
        Promise.resolve(createMockResponse(mockResponse, 400))
      ) as any;

      const response = await fetch('/api/payment-methods', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.validation_errors).toHaveLength(4);
    });
  });

  describe('POST /api/payment-methods/validate', () => {
    it('should validate credit card data', async () => {
      const requestBody = {
        payment_method_code: 'card',
        country_code: 'US',
        payment_data: {
          number: '****************',
          exp_month: '12',
          exp_year: '2025',
          cvc: '123'
        }
      };

      const mockResponse = {
        is_valid: true,
        errors: [],
        formatted_data: {
          number: '****************',
          exp_month: '12',
          exp_year: '2025',
          cvc: '123'
        }
      };

      global.fetch = vi.fn(() => 
        Promise.resolve(createMockResponse(mockResponse))
      ) as any;

      const response = await fetch('/api/payment-methods/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();
      expect(data.is_valid).toBe(true);
      expect(data.errors).toHaveLength(0);
    });

    it('should validate IBAN for SEPA', async () => {
      const requestBody = {
        payment_method_code: 'sepa_debit',
        country_code: 'BE',
        payment_data: {
          iban: '****************'
        }
      };

      const mockResponse = {
        is_valid: true,
        errors: [],
        formatted_data: {
          iban: '****************'
        }
      };

      global.fetch = vi.fn(() => 
        Promise.resolve(createMockResponse(mockResponse))
      ) as any;

      const response = await fetch('/api/payment-methods/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();
      expect(data.is_valid).toBe(true);
    });

    it('should return validation errors for invalid data', async () => {
      const requestBody = {
        payment_method_code: 'card',
        country_code: 'US',
        payment_data: {
          number: '1234567890123456'
        }
      };

      const mockResponse = {
        is_valid: false,
        errors: [
          { field: 'number', message: 'Invalid card number', code: 'INVALID_LUHN' }
        ]
      };

      global.fetch = vi.fn(() => 
        Promise.resolve(createMockResponse(mockResponse))
      ) as any;

      const response = await fetch('/api/payment-methods/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();
      expect(data.is_valid).toBe(false);
      expect(data.errors).toHaveLength(1);
    });
  });

  describe('POST /api/payment-methods/recommend', () => {
    it('should recommend payment methods based on preferences', async () => {
      const requestBody = {
        country_code: 'US',
        currency_code: 'USD',
        amount_cents: 1000,
        customer_id: 'cust_123',
        preferred_types: ['card']
      };

      const mockResponse = {
        recommended_methods: [
          {
            payment_method_code: 'card',
            priority: 1,
            reason: 'Customer preference',
            processing_fee_cents: 59
          },
          {
            payment_method_code: 'ach_debit',
            priority: 2,
            reason: 'Lower fees',
            processing_fee_cents: 13
          }
        ]
      };

      global.fetch = vi.fn(() => 
        Promise.resolve(createMockResponse(mockResponse))
      ) as any;

      const response = await fetch('/api/payment-methods/recommend', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();
      expect(data.recommended_methods).toHaveLength(2);
      expect(data.recommended_methods[0].payment_method_code).toBe('card');
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors', async () => {
      global.fetch = vi.fn(() => 
        Promise.reject(new Error('Network error'))
      ) as any;

      await expect(fetch('/api/payment-methods')).rejects.toThrow('Network error');
    });

    it('should handle server errors', async () => {
      global.fetch = vi.fn(() => 
        Promise.resolve(createMockResponse({ error: 'Internal server error' }, 500))
      ) as any;

      const response = await fetch('/api/payment-methods');
      expect(response.status).toBe(500);
    });

    it('should handle malformed requests', async () => {
      global.fetch = vi.fn(() => 
        Promise.resolve(createMockResponse({ error: 'Bad request' }, 400))
      ) as any;

      const response = await fetch('/api/payment-methods', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid json'
      });

      expect(response.status).toBe(400);
    });
  });
});
