[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "insurance-battle-station"
version = "1.0.0"
description = "Insurance Battle Station for PI Lawyer AI"
authors = [{name = "AiLex Team", email = "<EMAIL>"}]
license = {text = "Proprietary"}
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Legal Industry",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
]

dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "sqlalchemy[asyncio]>=2.0.23",
    "asyncpg>=0.29.0",
    "structlog>=23.2.0",
    "google-cloud-documentai>=2.21.0",
    "google-cloud-aiplatform>=1.38.1",
    "vertexai>=1.38.1",
    "google-cloud-storage>=2.10.0",
    "httpx>=0.25.2",
    "python-dateutil>=2.8.2",
    "beautifulsoup4>=4.12.2",
    "jinja2>=3.1.2",
    "weasyprint>=60.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "pytest-xdist>=3.5.0",
    "coverage[toml]>=7.3.2",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "bandit>=1.7.5",
    "pre-commit>=3.6.0",
    "factory-boy>=3.3.0",
    "faker>=20.1.0",
    "httpx>=0.25.2",
    "respx>=0.20.2",
]

test = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "coverage[toml]>=7.3.2",
    "factory-boy>=3.3.0",
    "faker>=20.1.0",
    "httpx>=0.25.2",
    "respx>=0.20.2",
]

[project.urls]
Homepage = "https://ailex.com"
Repository = "https://github.com/ailex/insurance-battle-station"

[tool.setuptools.packages.find]
where = ["."]
include = ["backend*", "packages*"]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["backend", "packages"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]
force_single_line = false
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

# mypy configuration
[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "google.cloud.*",
    "vertexai.*",
    "structlog.*",
    "factory_boy.*",
    "faker.*",
    "respx.*",
]
ignore_missing_imports = true

# Coverage configuration
[tool.coverage.run]
source = ["backend", "packages"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
    "*/venv/*",
    "*/env/*",
    "setup.py",
]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
skip_covered = false
skip_empty = false

[tool.coverage.html]
directory = "htmlcov"

[tool.coverage.xml]
output = "coverage.xml"

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=backend",
    "--cov=packages",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=60",
    "-ra",
    "--tb=short",
]
testpaths = ["tests", "backend/tests", "packages/insurance-battle-station/tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "e2e: End-to-end tests",
    "slow: Slow running tests",
    "security: Security related tests",
    "external: Tests requiring external services",
]
asyncio_mode = "auto"
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

# Bandit security configuration
[tool.bandit]
exclude_dirs = ["tests", "venv", "env"]
skips = ["B101", "B601"]